# Руководство по тестированию проекта Sputnik Gateway

## Обзор

Данный документ описывает структуру тестов и инструкции по их запуску для проекта Sputnik Gateway.

## Структура тестов

### Cucumber BDD тесты

Созданы следующие feature файлы с тест-кейсами:

#### Gateway модуль (`src/test/resources/features/`)
- `troika-refill-api.feature` - Тестирование API пополнения карт Тройка
- `security-authentication.feature` - Тестирование безопасности и аутентификации

#### Admin-API модуль (`admin-api/src/test/resources/features/`)
- `agent-management.feature` - Управление агентами и контрактами
- `payment-management.feature` - Управление платежами и долгами  
- `nbs-registry-management.feature` - Управление НБС и реестрами

#### Registry модуль (`regestry/src/test/resources/features/`)
- `registry-processing.feature` - Обработка файлов реестров
- `registry-validation.feature` - Валидация данных реестров

## Настройка тестового окружения

### Требования

- Java 17+
- Docker (для TestContainers)
- PostgreSQL (запускается автоматически в контейнере)

### Зависимости

Добавьте в `build.gradle.kts` каждого модуля:

```kotlin
dependencies {
    // Cucumber
    testImplementation("io.cucumber:cucumber-java:7.14.0")
    testImplementation("io.cucumber:cucumber-spring:7.14.0")
    testImplementation("io.cucumber:cucumber-junit-platform-engine:7.14.0")
    
    // TestContainers
    testImplementation("org.testcontainers:junit-jupiter:1.19.0")
    testImplementation("org.testcontainers:postgresql:1.19.0")
    testImplementation("org.testcontainers:r2dbc:1.19.0")
    
    // WireMock для мокирования внешних API
    testImplementation("com.github.tomakehurst:wiremock-jre8:2.35.0")
    
    // Spring Boot Test
    testImplementation("org.springframework.boot:spring-boot-starter-test")
    testImplementation("org.springframework.boot:spring-boot-testcontainers")
    testImplementation("io.projectreactor:reactor-test")
}
```

### Конфигурация JUnit

Создайте файл `src/test/resources/junit-platform.properties`:

```properties
cucumber.plugin=pretty,html:target/cucumber-reports,json:target/cucumber-reports/Cucumber.json
cucumber.glue=ru.metrosoft.sputnikgate.test.steps
cucumber.features=src/test/resources/features
```

## Запуск тестов

### Все тесты

```bash
# Запуск всех тестов проекта
./gradlew test

# Запуск тестов конкретного модуля
./gradlew :admin-api:test
./gradlew :regestry:test
```

### Cucumber тесты

```bash
# Запуск только Cucumber тестов
./gradlew test --tests "*CucumberTest"

# Запуск конкретного feature файла
./gradlew test --tests "*TroikaRefillApiTest"
```

### Тесты с профилями

```bash
# Интеграционные тесты
./gradlew test -Dspring.profiles.active=integration-test

# Тесты с реальной БД
./gradlew test -Dspring.profiles.active=db-test
```

## Реализация Step Definitions

### Пример базового класса

```kotlin
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@TestPropertySource(properties = ["spring.profiles.active=test"])
@Testcontainers
abstract class BaseStepDefinitions {
    
    @Autowired
    protected lateinit var webTestClient: WebTestClient
    
    @Container
    companion object {
        val postgres = PostgreSQLContainer<Nothing>("postgres:13")
    }
}
```

### Создание Step Definitions

1. Создайте пакет `src/test/kotlin/ru/metrosoft/sputnikgate/test/steps`
2. Реализуйте классы с аннотациями `@Дано`, `@Когда`, `@Тогда`
3. Используйте Spring DI для внедрения сервисов

## Мокирование внешних сервисов

### WireMock для НБС API

```kotlin
@TestConfiguration
class TestConfig {
    @Bean
    fun wireMockServer(): WireMockServer {
        val server = WireMockServer(8089)
        server.start()
        
        // Настройка мок ответов
        server.stubFor(post(urlEqualTo("/api/v1/get_keys"))
            .willReturn(aResponse()
                .withStatus(200)
                .withBody("""{"keys": ["key1", "key2"]}""")))
        
        return server
    }
}
```

## Тестовые данные

### Инициализация данных

Создайте классы для инициализации тестовых данных:

```kotlin
@Component
class TestDataInitializer {
    suspend fun initializeTestData() {
        // Создание тестовых агентов, платежей и т.д.
    }
}
```

### Очистка данных

```kotlin
@AfterEach
fun cleanup() {
    testDataInitializer.cleanupTestData()
}
```

## Отчеты о тестировании

### Cucumber отчеты

После запуска тестов отчеты доступны в:
- `target/cucumber-reports/index.html` - HTML отчет
- `target/cucumber-reports/Cucumber.json` - JSON отчет

### Покрытие кода

```bash
# Генерация отчета о покрытии
./gradlew jacocoTestReport

# Просмотр отчета
open build/reports/jacoco/test/html/index.html
```

## Лучшие практики

### Структура тестов

1. **Изоляция тестов** - каждый тест должен быть независимым
2. **Очистка данных** - очищайте тестовые данные после каждого теста
3. **Мокирование** - мокируйте внешние зависимости
4. **Читаемость** - используйте понятные названия сценариев

### Cucumber сценарии

1. **Фокус на поведении** - описывайте поведение, а не реализацию
2. **Переиспользование шагов** - создавайте переиспользуемые step definitions
3. **Данные в таблицах** - используйте DataTable для структурированных данных
4. **Контекст** - используйте Background для общих предусловий

### Производительность

1. **TestContainers** - используйте reuse для ускорения тестов
2. **Параллельное выполнение** - настройте параллельный запуск тестов
3. **Профили** - разделяйте быстрые и медленные тесты

## Отладка тестов

### Логирование

```yaml
# application-test.yml
logging:
  level:
    ru.metrosoft: DEBUG
    org.springframework.test: DEBUG
```

### Отладка в IDE

1. Установите breakpoint в step definition
2. Запустите тест в режиме отладки
3. Используйте переменные окружения для настройки

## Continuous Integration

### GitHub Actions

```yaml
name: Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-java@v3
        with:
          java-version: '17'
      - run: ./gradlew test
      - uses: actions/upload-artifact@v3
        with:
          name: test-reports
          path: build/reports/
```

## Контакты

При возникновении вопросов по тестированию обращайтесь к команде разработки.
