FROM gradle:8.13.0-jdk23-alpine as builder
RUN apk add gcompat
WORKDIR /build
ADD . /build

RUN --mount=type=cache,target=/home/<USER>/.gradle/caches \
    --mount=type=cache,target=/home/<USER>/.gradle/wrapper \
    gradle --no-daemon bootJar -i


FROM openjdk:23-jdk
COPY --from=builder build/build/libs/sputnik-gateway-*.jar ./sputnik-gateway.jar
COPY spytnik_prod.pem ./sbertroika_dev3.pem

EXPOSE 8081

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dspring.active.profiles=prod", "-jar", "sputnik-gateway.jar"]

