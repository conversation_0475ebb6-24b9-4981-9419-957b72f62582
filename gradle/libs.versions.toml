[versions]
# Kotlin versions
kotlinJvm = "2.1.10"
kotlinCoroutinesReactor = "1.6.3"
kotlinCoroutinesOkhttp = "1.0" # From root and admin-api
kotlinCoroutinesSlf4j = "1.8.1" # From root
kotlinTest = "1.9.23" # From nbs-client and common-lib test

# Spring Boot & related
springBoot = "3.4.2"
springDependencyManagement = "1.1.0"
reactorKotlinExtensions = "1.1.2" # From root, admin-api, common-lib
springBootStarterActuator = "3.4.2" # From root, admin-api
springBootStarterDataR2dbc = "3.4.2" # From root, admin-api, regestry, common-lib
springBootStarterLogging = "3.4.2" # From root
springBootStarterSecurity = "3.4.2" # From root, admin-api
springBootStarterWebflux = "3.4.2" # From root, admin-api
springSecurityOauth2Jose = "6.4.2" # From admin-api
springBootStarterOauth2ResourceServer = "3.4.2" # From admin-api
springBootStarterDataJpa = "3.4.2" # From common-lib

# Databases
flyway = "9.14.1" # From root, admin-api
postgresql = "42.5.2" # From root, admin-api, regestry
r2dbcPostgresql = "1.0.4.RELEASE" # From root, admin-api, regestry, common-lib
r2dbcPool = "1.0.1.RELEASE" # From root, admin-api, regestry

# Other libraries
okhttp = "4.12.0" # Use the latest version from nbs-client
jacksonModuleKotlin = "2.14.0" # From root
arrowCore = "1.0.1" # From root, admin-api, common-lib, regestry
mapstruct = "1.5.3.Final" # From root, admin-api, common-lib, regestry
mapstructProcessor = "1.5.3.Final" # From root, admin-api, common-lib, regestry
logbookSpringBootWebfluxAutoconfigure = "3.10.0" # From root
logbookOkhttp = "3.10.0" # From root
jjwt = "0.11.5" # From root
springdocOpenapiStarterWebfluxApi = "2.8.5" # From root
springdocOpenapiStarterWebfluxUi = "2.8.5" # From root
commonsIo = "2.16.1" # From root, admin-api
bouncycastleBcprovJdk15on = "1.70" # From admin-api
bouncycastleBcpkixJdk15on = "1.70" # From admin-api
sftpClient = "1.0.3" # From admin-api, common-lib
simplePemKeystore = "0.3" # From nbs-client
moshiKotlin = "1.15.1" # From nbs-client
moshiAdapters = "1.15.1" # From nbs-client
jakartaPersistenceApi = "3.1.0" # From common-lib
lombok = "1.18.30" # From admin-api, regestry (testCompileOnly)

# Test libraries
junitJupiterApi = "5.10.0" # From root, admin-api, regestry
junitJupiterEngine = "5.10.0" # From root, admin-api, regestry
mockk = "1.13.13" # From admin-api, regestry

[libraries]
# Kotlin
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect" }
kotlin-stdlib-jdk8 = { module = "org.jetbrains.kotlin:kotlin-stdlib-jdk8" }
kotlinx-coroutines-reactor = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-reactor", version.ref = "kotlinCoroutinesReactor" }
kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core" }
kotlinx-coroutines-slf4j = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-slf4j", version.ref = "kotlinCoroutinesSlf4j" }
kotlin-coroutines-okhttp = { module = "ru.gildor.coroutines:kotlin-coroutines-okhttp", version.ref = "kotlinCoroutinesOkhttp" }

# Spring Boot & related
spring-boot-starter-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator", version.ref = "springBootStarterActuator" }
spring-boot-starter-data-r2dbc = { module = "org.springframework.boot:spring-boot-starter-data-r2dbc", version.ref = "springBootStarterDataR2dbc" }
spring-boot-starter-logging = { module = "org.springframework.boot:spring-boot-starter-logging", version.ref = "springBootStarterLogging" }
spring-boot-starter-security = { module = "org.springframework.boot:spring-boot-starter-security", version.ref = "springBootStarterSecurity" }
spring-boot-starter-webflux = { module = "org.springframework.boot:spring-boot-starter-webflux", version.ref = "springBootStarterWebflux" }
spring-security-oauth2-jose = { module = "org.springframework.security:spring-security-oauth2-jose", version.ref = "springSecurityOauth2Jose" }
spring-boot-starter-oauth2-resource-server = { module = "org.springframework.boot:spring-boot-starter-oauth2-resource-server", version.ref = "springBootStarterOauth2ResourceServer" }
spring-boot-starter-data-jpa = { module = "org.springframework.boot:spring-boot-starter-data-jpa", version.ref = "springBootStarterDataJpa" }
reactor-kotlin-extensions = { module = "io.projectreactor.kotlin:reactor-kotlin-extensions", version.ref = "reactorKotlinExtensions" }

# Databases
flyway-core = { module = "org.flywaydb:flyway-core", version.ref = "flyway" }
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
r2dbc-postgresql = { module = "org.postgresql:r2dbc-postgresql", version.ref = "r2dbcPostgresql" }
r2dbc-pool = { module = "io.r2dbc:r2dbc-pool", version.ref = "r2dbcPool" }

# Other libraries
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }
okhttp-logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "okhttp" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin", version.ref = "jacksonModuleKotlin" }
arrow-core = { module = "io.arrow-kt:arrow-core", version.ref = "arrowCore" }
mapstruct = { module = "org.mapstruct:mapstruct", version.ref = "mapstruct" }
mapstruct-processor = { module = "org.mapstruct:mapstruct-processor", version.ref = "mapstructProcessor" }
logbook-spring-boot-webflux-autoconfigure = { module = "org.zalando:logbook-spring-boot-webflux-autoconfigure", version.ref = "logbookSpringBootWebfluxAutoconfigure" }
logbook-okhttp = { module = "org.zalando:logbook-okhttp", version.ref = "logbookOkhttp" }
jjwt-api = { module = "io.jsonwebtoken:jjwt-api", version.ref = "jjwt" }
jjwt-impl = { module = "io.jsonwebtoken:jjwt-impl", version.ref = "jjwt" }
jjwt-jackson = { module = "io.jsonwebtoken:jjwt-jackson", version.ref = "jjwt" }
springdoc-openapi-starter-webflux-api = { module = "org.springdoc:springdoc-openapi-starter-webflux-api", version.ref = "springdocOpenapiStarterWebfluxApi" }
springdoc-openapi-starter-webflux-ui = { module = "org.springdoc:springdoc-openapi-starter-webflux-ui", version.ref = "springdocOpenapiStarterWebfluxUi" }
commons-io = { module = "commons-io:commons-io", version.ref = "commonsIo" }
bouncycastle-bcprov-jdk15on = { module = "org.bouncycastle:bcprov-jdk15on", version.ref = "bouncycastleBcprovJdk15on" }
bouncycastle-bcpkix-jdk15on = { module = "org.bouncycastle:bcpkix-jdk15on", version.ref = "bouncycastleBcpkixJdk15on" }
sftp-client = { module = "com.springml:sftp.client", version.ref = "sftpClient" }
simple-pem-keystore = { module = "io.r2:simple-pem-keystore", version.ref = "simplePemKeystore" }
moshi-kotlin = { module = "com.squareup.moshi:moshi-kotlin", version.ref = "moshiKotlin" }
moshi-adapters = { module = "com.squareup.moshi:moshi-adapters", version.ref = "moshiAdapters" }
jakarta-persistence-api = { module = "jakarta.persistence:jakarta.persistence-api", version.ref = "jakartaPersistenceApi" }
lombok = { module = "org.projectlombok:lombok", version.ref = "lombok" }

junit-jupiter-api = { module = "org.junit.jupiter:junit-jupiter-api", version.ref = "junitJupiterApi" }
junit-jupiter-engine = { module = "org.junit.jupiter:junit-jupiter-engine", version.ref = "junitJupiterEngine" }
kotlin-test = { module = "org.jetbrains.kotlin:kotlin-test", version.ref = "kotlinTest" }
mockk-jvm = { module = "io.mockk:mockk-jvm", version.ref = "mockk" }
kotlinx-coroutines-test = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-test" }
spring-boot-starter-test = { module = "org.springframework.boot:spring-boot-starter-test", version.ref = "springBoot" }


[plugins]
idea = { id = "idea" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlinJvm" }
kotlin-kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlinJvm" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlinJvm" }
spring-boot = { id = "org.springframework.boot", version.ref = "springBoot" }
dependency-management = { id = "io.spring.dependency-management", version.ref = "springDependencyManagement" }
java-library = { id = "java-library" }
maven-publish = { id = "maven-publish" }
spotless = { id = "com.diffplug.spotless", version = "6.25.0" } # Version from nbs-client