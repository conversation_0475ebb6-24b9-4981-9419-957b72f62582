buildscript {
    ext {
        springBootVersion = '3.4.2'
        springDependencyManagementVersion = '1.1.0'

        kotlinJvmVersion = '2.1.10'
        kotlinCoroutinesOkhttpVersion = "1.0"

        okhttpVersion = '4.10.0'
        jacksonModuleKotlinVersion = "2.14.0"

        arrowCoreVersion = "1.0.1"

        flywayVersion = "9.14.1"
        postgresqlVersion = "42.5.2"

        jjwtVersion = "0.11.5"
        springdocVersion = "2.8.5"
    }
}

plugins {
//    id 'idea'
    id 'java-library'
    id 'org.jetbrains.kotlin.jvm' version "$kotlinJvmVersion"
    id "org.jetbrains.kotlin.kapt" version "$kotlinJvmVersion"
    id 'org.jetbrains.kotlin.plugin.spring' version "$kotlinJvmVersion"
    id 'org.springframework.boot' version "$springBootVersion"
    id 'io.spring.dependency-management' version "$springDependencyManagementVersion"
}

group = 'ru.metrosoft'
version = '1.0.0-SNAPSHOT'

repositories {
    mavenCentral()
}

kapt {
    showProcessorStats = true
    correctErrorTypes true
}
bootJar {
    enabled = false
}

jar {
    enabled = true
}

dependencies {
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
//    implementation "org.springframework.boot:spring-boot-starter-webflux"

    implementation 'io.projectreactor.kotlin:reactor-kotlin-extensions'
    implementation 'org.jetbrains.kotlin:kotlin-reflect'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-reactor'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core"

    implementation("org.springframework.boot:spring-boot-starter-data-jpa")
    implementation("jakarta.persistence:jakarta.persistence-api:3.1.0")

    //Kotlin-ext
    implementation "io.arrow-kt:arrow-core:$arrowCoreVersion"

    // MapStruct
    compileOnly "org.mapstruct:mapstruct:1.5.3.Final"
    kapt "org.mapstruct:mapstruct-processor:1.5.3.Final"

    compileOnly "org.postgresql:r2dbc-postgresql"

    implementation 'com.springml:sftp.client:1.0.3'

    testImplementation 'org.jetbrains.kotlin:kotlin-test'
}

test {
    useJUnitPlatform()
}