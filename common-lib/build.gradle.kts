plugins {
    java
    kotlin("jvm")
    alias(libs.plugins.kotlin.kapt)
}

group = "ru.metrosoft"
version = "1.0.0-SNAPSHOT"

kapt {
    showProcessorStats = true
    correctErrorTypes = true
}

dependencies {
    implementation(libs.spring.boot.starter.data.r2dbc)

    implementation(libs.reactor.kotlin.extensions)
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib.jdk8)
    implementation(libs.kotlinx.coroutines.reactor)
    implementation(libs.kotlinx.coroutines.core)

    implementation(libs.spring.boot.starter.data.jpa)
    implementation(libs.jakarta.persistence.api)

    //Kotlin-ext
    implementation(libs.arrow.core)

    // MapStruct
    compileOnly(libs.mapstruct)
    kapt(libs.mapstruct.processor)

    compileOnly(libs.r2dbc.postgresql)

    implementation(libs.sftp.client)

    testImplementation(libs.kotlin.test)
}

tasks.test {
    useJUnitPlatform()
}