package ru.metrosoft.sputnikgate.service

import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.BeforeEach
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.TestPropertySource
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import ru.metrosoft.sputnikgate.model.AgentPayment
import ru.metrosoft.sputnikgate.model.AgentPaymentType
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentPaymentRepository
import ru.metrosoft.sputnikgate.output.persistance.repository.RegistryRepository
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentPaymentMapper
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*
import kotlin.test.assertTrue
import kotlin.test.assertEquals

@SpringBootTest
@TestPropertySource(properties = [
    "spring.r2dbc.url=r2dbc:postgresql://localhost:5432/test_db",
    "spring.flyway.url=****************************************"
])
@Testcontainers
class RegistryServiceIntegrationTest {

    @Container
    companion object {
        val postgres = PostgreSQLContainer<Nothing>("postgres:13").apply {
            withDatabaseName("test_db")
            withUsername("test")
            withPassword("test")
        }
    }

    @Autowired
    private lateinit var registryService: RegistryService

    @Autowired
    private lateinit var agentPaymentRepository: AgentPaymentRepository

    @Autowired
    private lateinit var registryRepository: RegistryRepository

    @Autowired
    private lateinit var agentPaymentMapper: AgentPaymentMapper

    @BeforeEach
    fun setUp() = runBlocking {
        // Очищаем данные перед каждым тестом
        agentPaymentRepository.deleteAll()
        registryRepository.deleteAll()
    }

    @Test
    fun `should generate registry with FBT prefix for PAYWRITE payments`() = runBlocking {
        // Given
        val testDate = LocalDate.of(2024, 1, 15)
        val agentNBSId = UUID.randomUUID()
        
        // Создаем тестовый платеж PAYWRITE
        val paywritePayment = createTestPayment(
            type = AgentPaymentType.PAYWRITE,
            payStatus = true,
            processTime = testDate.atStartOfDay()
        )
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(paywritePayment))

        // When
        val registry = registryService.newRegistry("SALEREG", testDate, agentNBSId)

        // Then
        assertTrue(registry.name!!.contains("FBT"), "Registry name should contain FBT prefix for PAYWRITE payments")
        assertEquals("salereg_FBT_240115_01.csv", registry.name)
    }

    @Test
    fun `should generate registry with DWB prefix for PAY payments only`() = runBlocking {
        // Given
        val testDate = LocalDate.of(2024, 1, 15)
        val agentNBSId = UUID.randomUUID()
        
        // Создаем тестовый платеж PAY
        val payPayment = createTestPayment(
            type = AgentPaymentType.PAY,
            processTime = testDate.atStartOfDay()
        )
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(payPayment))

        // When
        val registry = registryService.newRegistry("SALEREG", testDate, agentNBSId)

        // Then
        assertTrue(registry.name!!.contains("DWB"), "Registry name should contain DWB prefix for PAY payments only")
        assertEquals("salereg_DWB_240115_01.csv", registry.name)
    }

    @Test
    fun `should generate registry with FBT prefix when both PAYWRITE and PAY payments exist`() = runBlocking {
        // Given
        val testDate = LocalDate.of(2024, 1, 15)
        val agentNBSId = UUID.randomUUID()
        
        // Создаем оба типа платежей
        val paywritePayment = createTestPayment(
            type = AgentPaymentType.PAYWRITE,
            payStatus = true,
            processTime = testDate.atStartOfDay()
        )
        val payPayment = createTestPayment(
            type = AgentPaymentType.PAY,
            processTime = testDate.atStartOfDay()
        )
        
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(paywritePayment))
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(payPayment))

        // When
        val registry = registryService.newRegistry("SALEREG", testDate, agentNBSId)

        // Then
        assertTrue(registry.name!!.contains("FBT"), "Registry name should contain FBT prefix when PAYWRITE payments exist")
        assertEquals("salereg_FBT_240115_01.csv", registry.name)
    }

    @Test
    fun `should generate registry with FBT prefix when no payments exist`() = runBlocking {
        // Given
        val testDate = LocalDate.of(2024, 1, 15)
        val agentNBSId = UUID.randomUUID()
        
        // Нет платежей в системе

        // When
        val registry = registryService.newRegistry("SALEREG", testDate, agentNBSId)

        // Then
        assertTrue(registry.name!!.contains("FBT"), "Registry name should contain FBT prefix by default")
        assertEquals("salereg_FBT_240115_01.csv", registry.name)
    }

    @Test
    fun `should not consider PAYWRITE payments without payStatus for prefix determination`() = runBlocking {
        // Given
        val testDate = LocalDate.of(2024, 1, 15)
        val agentNBSId = UUID.randomUUID()
        
        // Создаем PAYWRITE платеж без payStatus и PAY платеж
        val paywritePaymentWithoutPayStatus = createTestPayment(
            type = AgentPaymentType.PAYWRITE,
            payStatus = false,
            processTime = testDate.atStartOfDay()
        )
        val payPayment = createTestPayment(
            type = AgentPaymentType.PAY,
            processTime = testDate.atStartOfDay()
        )
        
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(paywritePaymentWithoutPayStatus))
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(payPayment))

        // When
        val registry = registryService.newRegistry("SALEREG", testDate, agentNBSId)

        // Then
        assertTrue(registry.name!!.contains("DWB"), "Registry name should contain DWB prefix when only valid PAY payments exist")
        assertEquals("salereg_DWB_240115_01.csv", registry.name)
    }

    @Test
    fun `should generate sequential registry names correctly`() = runBlocking {
        // Given
        val testDate = LocalDate.of(2024, 1, 15)
        val agentNBSId = UUID.randomUUID()
        
        // Создаем тестовый платеж
        val payPayment = createTestPayment(
            type = AgentPaymentType.PAY,
            processTime = testDate.atStartOfDay()
        )
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(payPayment))

        // When - создаем несколько реестров
        val registry1 = registryService.newRegistry("SALEREG", testDate, agentNBSId)
        val registry2 = registryService.newRegistry("SALEREG", testDate, agentNBSId)
        val registry3 = registryService.newRegistry("SALEREG", testDate, agentNBSId)

        // Then
        assertEquals("salereg_DWB_240115_01.csv", registry1.name)
        assertEquals("salereg_DWB_240115_02.csv", registry2.name)
        assertEquals("salereg_DWB_240115_03.csv", registry3.name)
    }

    private fun createTestPayment(
        type: AgentPaymentType,
        status: Boolean = true,
        payStatus: Boolean = false,
        processTime: LocalDateTime? = null
    ): AgentPayment {
        return AgentPayment(
            _id = UUID.randomUUID().toString(),
            type = type,
            ts = LocalDateTime.now(),
            agentId = UUID.randomUUID(),
            agentNBSId = UUID.randomUUID(),
            amount = BigDecimal("100"),
            payStatus = payStatus,
            writeStatus = false,
            status = status,
            cardNum = "1234567890"
        ).apply {
            this.processTime = processTime
        }
    }
}
