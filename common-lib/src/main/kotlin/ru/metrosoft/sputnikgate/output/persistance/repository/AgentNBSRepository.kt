package ru.metrosoft.sputnikgate.output.persistance.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository

import ru.metrosoft.sputnikgate.output.persistance.model.*

import java.util.*


interface AgentNBSRepository: CoroutineCrudRepository<AgentNBS, UUID> {

    @Query("SELECT * FROM agent_nbs WHERE id = :id")
    fun findTopByOrderByIdDesc(id: UUID): Flow<AgentNBS>?
}