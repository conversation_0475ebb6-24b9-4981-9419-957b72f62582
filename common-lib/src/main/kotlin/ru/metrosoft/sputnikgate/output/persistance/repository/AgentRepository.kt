package ru.metrosoft.sputnikgate.output.persistance.repository

import kotlinx.coroutines.flow.Flow
import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.output.persistance.model.*
import java.math.BigDecimal
import java.time.LocalDate
import java.util.*


interface AgentRepository: CoroutineCrudRepository<Agent, UUID>

interface AgentPaymentRepository: CoroutineCrudRepository<AgentPayment, String> {
    @Query(value = "SELECT * FROM agent_payment WHERE ts::date = :day")
    fun findAllByTs(day: LocalDate): Flow<AgentPayment>

    @Query(value = "SELECT * FROM agent_payment WHERE (process_time + interval '3' hour)::date = :day")
    fun findAllByProcessTime(day: LocalDate): Flow<AgentPayment>

    @Query(value = "SELECT * FROM agent_payment WHERE (notify_time + interval '3' hour)::date = :day")
    fun findAllByNotifyTime(day: LocalDate): Flow<AgentPayment>
}

interface AgentPaymentServiceRepository: CoroutineCrudRepository<AgentPaymentService, AgentPaymentServicePK> {
    @Query(value = "SELECT * FROM agent_payment_service WHERE id = :id")
    fun byId(id: String): Flow<AgentPaymentService>
}

interface AgentRequestRepository: CoroutineCrudRepository<AgentRequest, BigDecimal> {
    @Query(value = "SELECT * FROM agent_request ORDER BY TS DESC LIMIT 50")
    fun findLast(): Flow<AgentRequest>
}

interface AgentContractRepository: CoroutineCrudRepository<AgentContract, UUID> {

    fun findAllByAgentId(agentId: UUID): Flow<AgentContract>
}

interface AgentCertificateRepository: CoroutineCrudRepository<ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate, UUID> {
    @Query(value = "SELECT * FROM agent_certificate WHERE id = :id")
    fun byId(id: UUID): Flow<ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate>
}

interface AgentDebtRepository: CoroutineCrudRepository<AgentDebt, Unit> {
    @Query(value = "" +
            "select\n" +
            "    ap.agent_id,\n" +
            "    ap.ts::date as date,\n" +
            "    sum(\n" +
            "            CASE pay_status\n" +
            "                WHEN true THEN (ac.sale_rate / 100) * ap.amount\n" +
            "                WHEN false THEN 0\n" +
            "            END\n" +
            "    ) as summ_p,\n" +
            "    sum(\n" +
            "        CASE write_status\n" +
            "            WHEN true THEN ap.amount * (ac.write_rate / 100)\n" +
            "            WHEN false THEN 0\n" +
            "        END\n" +
            "    ) as summ_w,\n" +
            "    count(ap.id) as cnt,\n" +
            "    sum(ap.amount) as sum_all\n" +
            "from agent_payment ap\n" +
            "left join agent_contract ac on ac.agent_id = ap.agent_id and ap.ts >= ac.start_date and ap.ts <= coalesce(ac.end_date, '2099-01-01')\n" +
            "where ap.status = true\n" +
            "group by ap.agent_id, ap.ts::date")
    fun getAll(): Flow<AgentDebt>

    fun findAllByAgentId(agentId: UUID): Flow<AgentDebt>
}

interface RegistryRepository: CoroutineCrudRepository<Registry, UUID> {
    fun countByTypeAndDate(type: String, date: LocalDate): Mono<Long>

}

//interface CardSectorReadRepository: CoroutineCrudRepository<CardSectorRead, String>
//interface CardSectorWriteRepository: CoroutineCrudRepository<CardSectorWrite, String>
//interface AgentSalePointRepository: CoroutineCrudRepository<AgentSalePoint, UUID>
//
//interface SaleGateRepository: CoroutineCrudRepository<SaleGate, UUID>
//
//interface SaleGateAuthMethodRepository: CoroutineCrudRepository<SaleGateAuthMethod, UUID>
//
//interface BackendRepository: CoroutineCrudRepository<Backend, UUID>
//
//interface AgentGateConfigRepository: CoroutineCrudRepository<AgentGateConfig, UUID>
//
//interface ContractorDayConfigRepository: CoroutineCrudRepository<ContractorDayConfig, UUID>
//
//interface ProjectDayConfigRepository: CoroutineCrudRepository<ProjectDayConfig, UUID>
