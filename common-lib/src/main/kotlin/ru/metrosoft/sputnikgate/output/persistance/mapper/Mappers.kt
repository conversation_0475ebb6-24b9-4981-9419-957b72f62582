package ru.metrosoft.sputnikgate.output.persistance.mapper

import org.mapstruct.Mapper
import org.mapstruct.Mapping
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate
import ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS


@Mapper(componentModel = "spring")
interface AgentMapper {

    fun toPersistModel(agent: Agent) : ru.metrosoft.sputnikgate.output.persistance.model.Agent

    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.Agent) : Agent
}

@Mapper(componentModel = "spring")
interface AgentNBSRegistryMapper {
    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS) : AgentNBSRegistry
}

@Mapper(componentModel = "spring")
interface AgentPaymentMapper {

    fun toPersistModel(agent: AgentPayment) : ru.metrosoft.sputnikgate.output.persistance.model.AgentPayment

    @Mapping(target = "services", ignore = true)
    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.AgentPayment) : AgentPayment
}

@Mapper(componentModel = "spring")
interface AgentPaymentServiceMapper {

    fun toPersistModel(agent: AgentPaymentService) : ru.metrosoft.sputnikgate.output.persistance.model.AgentPaymentService

    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.AgentPaymentService) : AgentPaymentService
}

@Mapper(componentModel = "spring")
interface AgentRequestMapper {

    fun toPersistModel(agent: AgentRequest) : ru.metrosoft.sputnikgate.output.persistance.model.AgentRequest

    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.AgentRequest) : AgentRequest
}

@Mapper(componentModel = "spring")
interface AgentContractMapper {

    fun toPersistModel(agentContract: AgentContract) : ru.metrosoft.sputnikgate.output.persistance.model.AgentContract

    fun toDomain(agentContract: ru.metrosoft.sputnikgate.output.persistance.model.AgentContract) : AgentContract
}

@Mapper(componentModel = "spring")
interface AgentCertificateMapper {
    fun toPersistModel(agentCertificate: AgentCertificate): ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate
    fun toDomain(agentCertificate: ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate): AgentCertificate
}


@Mapper(componentModel = "spring")
interface AgentDebtMapper {

    fun toPersistModel(agentDebt: AgentDebt) : ru.metrosoft.sputnikgate.output.persistance.model.AgentDebt

    fun toDomain(agentDebt: ru.metrosoft.sputnikgate.output.persistance.model.AgentDebt) : AgentDebt
}


@Mapper(componentModel = "spring")
interface CardSectorReadMapper {

    fun toPersistModel(agent: CardSector) : ru.metrosoft.sputnikgate.output.persistance.model.CardSectorRead

    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.CardSectorRead) : CardSector
}

@Mapper(componentModel = "spring")
interface CardSectorWriteMapper {

    fun toPersistModel(agent: CardSector) : ru.metrosoft.sputnikgate.output.persistance.model.CardSectorWrite

    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.CardSectorWrite) : CardSector
}

@Mapper(componentModel = "spring")
interface RegistryMapper {

    fun toPersistModel(agent: Registry) : ru.metrosoft.sputnikgate.output.persistance.model.Registry

    fun toDomain(agent: ru.metrosoft.sputnikgate.output.persistance.model.Registry) : Registry
}


//@Mapper(componentModel = "spring")
//interface AgentSalePointMapper {
//
//    fun toPersistModel(point: AgentSalePoint) : ru.sbertroika.agentgate.output.persistance.model.AgentSalePoint
//
//    fun toDomain(point: ru.sbertroika.agentgate.output.persistance.model.AgentSalePoint) : AgentSalePoint
//}
//
//@Mapper(componentModel = "spring")
//interface SaleGateMapper {
//
//    fun toPersistModel(gate: AgentSaleGate) : ru.sbertroika.agentgate.output.persistance.model.SaleGate
//
//    fun toDomain(gate: ru.sbertroika.agentgate.output.persistance.model.SaleGate) : AgentSaleGate
//}
//
//@Mapper(componentModel = "spring")
//interface SaleGateAuthMethodMapper {
//
//    fun toPersistModel(auth: GateAuthMethod) : ru.sbertroika.agentgate.output.persistance.model.SaleGateAuthMethod
//
//    fun toDomain(auth: ru.sbertroika.agentgate.output.persistance.model.SaleGateAuthMethod) : GateAuthMethod
//}
//
//@Mapper(componentModel = "spring")
//interface BackendMapper {
//
//    fun toPersistModel(backend: AgentBackend) : ru.sbertroika.agentgate.output.persistance.model.Backend
//
//    fun toDomain(backend: ru.sbertroika.agentgate.output.persistance.model.Backend) : AgentBackend
//}
//
//@Mapper(componentModel = "spring")
//interface AgentGateConfigMapper {
//
//    @Mapping(target = "pointId", expression = "java(gateConfig.getPoint().getId())")
//    @Mapping(target = "gateId", expression = "java(gateConfig.getGate().getId())")
//    @Mapping(target = "gateType", expression = "java(gateConfig.getGate().getType())")
//    @Mapping(target = "authMethodId", expression = "java(gateConfig.getAuthMethod().getId())")
//    @Mapping(target = "authType", expression = "java(gateConfig.getAuthMethod().getType())")
//    @Mapping(target = "backendId", expression = "java(gateConfig.getBackend().getId())")
//    @Mapping(target = "backendType", expression = "java(gateConfig.getBackend().getType())")
//    fun toPersistModel(gateConfig: AgentGateConfig) : ru.sbertroika.agentgate.output.persistance.model.AgentGateConfig
//
//    @Mapping(target = "gate", expression = "java(new ru.sbertroika.agentgate.model.AgentSaleGate(gateConfig.getId(), 0, \"\"))")
//    @Mapping(target = "point", expression = "java(new ru.sbertroika.agentgate.model.AgentSalePoint(gateConfig.getId(), \"\"))")
//    @Mapping(target = "authMethod", expression = "java(new ru.sbertroika.agentgate.model.GateAuthMethod(gateConfig.getAuthMethodId(), 0))")
//    @Mapping(target = "backend", expression = "java(new ru.sbertroika.agentgate.model.AgentBackend(gateConfig.getAuthMethodId(), 0))")
//    fun toDomain(gateConfig: ru.sbertroika.agentgate.output.persistance.model.AgentGateConfig) : AgentGateConfig
//}
//
//@Mapper(componentModel = "spring")
//interface ContractorDayConfigMapper {
//
//    @Mapping(target = "agentId", expression = "java(dayConfig.getAgent().getId())")
//    @Mapping(target = "gateConfigId", expression = "java(dayConfig.getAgentGateConfig().getId())")
//    @Mapping(target = "projectDayConfigId", expression = "java(dayConfig.getProjectDayConfig().getId())")
//    fun toPersistModel(dayConfig: ContractorDayConfig) : ru.sbertroika.agentgate.output.persistance.model.ContractorDayConfig
//
//    @Mapping(target = "agent", source = "dayConfig.agentId", qualifiedByName = [ "UUIDToAgent" ])
//    @Mapping(target = "agentGateConfig", expression = "java(new AgentGateConfig(dayConfig.getGateConfigId(), null, null, null, null))")
//    @Mapping(target = "projectDayConfig", expression = "java(new ProjectDayConfig(dayConfig.getProjectDayConfigId(), null, null))")
//    fun toDomain(dayConfig: ru.sbertroika.agentgate.output.persistance.model.ContractorDayConfig) : ContractorDayConfig
//
//    companion object {
//
//        @JvmStatic
//        @Named("UUIDToAgent")
//        fun UUIDToAgent(value: UUID): Agent = Agent(value, "", UUID(0,0),
//            UUID(0,0), UUID(0,0), LocalDate.now(), LocalDate.now(), TimeZone.getDefault())
//
//    }
//}
//
//
//@Mapper(componentModel = "spring")
//interface ProjectDayConfigMapper {
//
//    fun toPersistModel(dayConfig: ProjectDayConfig) : ru.sbertroika.agentgate.output.persistance.model.ProjectDayConfig
//
//    @Mapping(target = "project",
//        expression = "java(new Project(dayConfig.getProjectId(), \"dummy\", LocalDate.now(), LocalDate.now(), java.util.TimeZone.getDefault()))")
//    fun toDomain(dayConfig: ru.sbertroika.agentgate.output.persistance.model.ProjectDayConfig) : ProjectDayConfig
//}