package ru.metrosoft.sputnikgate.service

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.reactor.asFlux
import kotlinx.coroutines.withContext
import org.springframework.data.relational.core.query.Criteria
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentDebtMapper
import ru.metrosoft.sputnikgate.output.persistance.model.Agent
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentDebtRepository
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentRepository
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

@Service
class AgentDebtService(
    private val agentRepository: AgentRepository,
    private val agentDebtRepository: AgentDebtRepository,
    private val agentDebtMapper: AgentDebtMapper
) {

    suspend fun findDebts(): Flux<AgentDebt> {
        return agentDebtRepository.getAll().map(agentDebtMapper::toDomain).asFlux()
    }

    suspend fun findDebtsByAgent(agentId: UUID): Flux<AgentDebt> {
        return agentDebtRepository.findAllByAgentId(agentId).map(agentDebtMapper::toDomain).asFlux()
    }

    suspend fun calcDebts(filter: DebtFilter, pagination: Pagination): DebtFilterResult {
        var startPeriod = LocalDate.now().withDayOfMonth(1)
        var endPeriod = LocalDate.now()

        var debts = findDebts()

        val agentList = withContext(Dispatchers.IO) {
            agentRepository.findAll().asFlux().collectList().block()
        }

        // Apply filters for agentId and agentNBSId
        filter.agentId?.let { agentId ->
            debts = debts.filter { it.agentId == agentId }
        }
        filter.agentNBSId?.let { agentNBSId ->
            debts = debts.filter { it.agentNBSId == agentNBSId }
        }

        // Handle start and end date filtering
        filter.tsStart?.let { tsStart ->
            startPeriod = ZonedDateTime.parse(tsStart).toOffsetDateTime().atZoneSameInstant(ZoneId.systemDefault()).toLocalDate()
            debts = debts.filter { it.date.isAfter(startPeriod) || it.date.isEqual(startPeriod) }
        }
        filter.tsEnd?.let { tsEnd ->
            endPeriod = ZonedDateTime.parse(tsEnd).toOffsetDateTime().atZoneSameInstant(ZoneId.systemDefault()).toLocalDate()
            debts = debts.filter { it.date.isBefore(endPeriod) }
        }

        // Collect debts into a list
        val resultDebts = withContext(Dispatchers.IO) {
            debts.collectList().block()
        }

        val retDebts = mutableListOf<AgentDebt>()
        var c = startPeriod

        while (c.isBefore(endPeriod) || c.isEqual(endPeriod)) {
            val dayDebt = resultDebts?.filter { it.date.isEqual(c) }

            if (!dayDebt.isNullOrEmpty()) {
                retDebts.addAll(dayDebt)
            } else {
                if (filter.agentId != null && filter.agentNBSId != null) {
                    retDebts.add(AgentDebt(
                        agentId = filter.agentId,
                        agentNBSId = filter.agentNBSId,
                        date = c,
                        summPayment = 0.0,
                        summWrite = 0.0,
                        count = 0,
                        sumAll = 0
                    ))
                } else {
                    agentList?.forEach {
                        retDebts.add(AgentDebt(
                            agentId = it.id.toString(),
                            date = c,
                            summPayment = 0.0,
                            summWrite = 0.0,
                            count = 0,
                            sumAll = 0
                        ))
                    }
                }
            }
            c = c.plusDays(1)
        }

        val count = retDebts.count()
        return DebtFilterResult(
            result = retDebts,
            pagination = pagination.copy(
                totalCount = count,
                totalPage = if (count % pagination.limit == 0) count / pagination.limit else count / pagination.limit + 1
            )
        )
    }
}