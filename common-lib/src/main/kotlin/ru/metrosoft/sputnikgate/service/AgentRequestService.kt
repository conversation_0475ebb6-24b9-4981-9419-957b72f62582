package ru.metrosoft.sputnikgate.service

import kotlinx.coroutines.flow.map
import kotlinx.coroutines.reactor.asFlux
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import ru.metrosoft.sputnikgate.model.AgentRequest
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentRequestMapper
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentRequestRepository

@Component
class AgentRequestService(private val agentRequestRepository: AgentRequestRepository, private val agentRequestMapper: AgentRequestMapper) {


    suspend fun saveAgentRequest(agentRequest: AgentRequest) : AgentRequest {
        return agentRequestMapper.toDomain(agentRequestRepository.save(agentRequestMapper.toPersistModel(agentRequest)))
    }

    fun findAll(): Flux<AgentRequest> {
        return agentRequestRepository.findLast().map(agentRequestMapper::toDomain).asFlux()
    }


}