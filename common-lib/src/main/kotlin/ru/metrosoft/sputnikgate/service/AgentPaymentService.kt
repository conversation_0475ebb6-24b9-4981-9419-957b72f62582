package ru.metrosoft.sputnikgate.service

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.withContext
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.metrosoft.sputnikgate.model.AgentPayment
import ru.metrosoft.sputnikgate.model.AgentPaymentFilter
import ru.metrosoft.sputnikgate.model.AgentPaymentFilterResult
import ru.metrosoft.sputnikgate.model.AgentPaymentService
import ru.metrosoft.sputnikgate.model.Pagination
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentPaymentMapper
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentPaymentServiceMapper
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentPaymentRepository
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentPaymentServiceRepository
import java.time.LocalDate
import java.util.*

@Service
class AgentPaymentService(
    private val agentPaymentRepository: AgentPaymentRepository,
    private val agentPaymentServiceRepository: AgentPaymentServiceRepository,
    private val agentPaymentMapper: AgentPaymentMapper,
    private val agentPaymentServiceMapper: AgentPaymentServiceMapper,
    private val entityTemplate: R2dbcEntityOperations
) {

    suspend fun persist(agentPayment: AgentPayment) {
        agentPaymentRepository.save(agentPaymentMapper.toPersistModel(agentPayment))
        if (agentPayment.services.isNotEmpty()) {
            agentPayment.services.map(agentPaymentServiceMapper::toPersistModel)
                .toList()
                .forEach {
                    agentPaymentServiceRepository.save(it)
                }
        }
    }

    suspend fun findAgentPayment(id: String): Either<AgentError, AgentPayment> {
        val payment = agentPaymentRepository.findById(id) ?: return AgentError.NoAgentError.left()

        return agentPaymentMapper.toDomain(payment).right()
    }

    enum class AgentError(val message: String) {
        NoAgentError("Агент не найден")
    }

    suspend fun findPayments(filter: AgentPaymentFilter, pagination: Pagination): AgentPaymentFilterResult {
        val search = mutableListOf<Criteria>()
        filter.agentId?.let {
            if (it.isNotEmpty()) search.add(Criteria.where("agentId").`is`(it))
        }
        filter.agentNBSId?.let {
            if (it.isNotEmpty()) search.add(Criteria.where("agentNBSId").`is`(it))
        }
        filter.sessionId?.let {
            if (it.isNotEmpty()) search.add(Criteria.where("id").`is`(it))
        }
        filter.tsStart?.let {
            search.add(Criteria.where("ts").greaterThanOrEquals(it))
        }
        filter.tsEnd?.let {
            search.add(Criteria.where("ts").lessThan(it))
        }

        val count = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.AgentPayment::class.java)
                .matching(Query.query(Criteria.from(search)))
                .count().block()
        }?.toInt()!!

        val sort: Sort = if (filter.sort.isEmpty()) Sort.by("ts").descending() else {
            filter.sort.first().let {
                if (it.sort == ru.metrosoft.sputnikgate.model.Sort.ASC) Sort.by(it.field).ascending()
                else Sort.by(it.field).descending()
            }
        }

        val res = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.AgentPayment::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(sort)
                        .offset((pagination.limit * (pagination.page - 1)).toLong())
                        .limit(pagination.limit)
                ).all()
                .collectList()
                .block()
        }

        return AgentPaymentFilterResult(
            result = res?.map(agentPaymentMapper::toDomain)?.toList() ?: emptyList(),
            pagination = pagination.copy(
                totalCount = count,
                totalPage = if (count % pagination.limit == 0) count / pagination.limit else count / pagination.limit + 1
            )
        )
    }

    suspend fun findAgentPayments(agentId: UUID, filter: AgentPaymentFilter, pagination: Pagination): AgentPaymentFilterResult {
        val search = mutableListOf<Criteria>()
        search.add(Criteria.where("agentId").`is`(agentId))
        filter.sessionId?.let {
            if (it.isNotEmpty()) search.add(Criteria.where("id").`is`(it))
        }
        filter.tsStart?.let {
            search.add(Criteria.where("ts").greaterThanOrEquals(it))
        }
        filter.tsEnd?.let {
            search.add(Criteria.where("ts").lessThan(it))
        }

        val count = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.AgentPayment::class.java)
                .matching(Query.query(Criteria.from(search)))
                .count().block()
        }?.toInt()!!

        val sort: Sort = if (filter.sort.isEmpty()) Sort.by("ts").descending() else {
            filter.sort.first().let {
                if (it.sort == ru.metrosoft.sputnikgate.model.Sort.ASC) Sort.by(it.field).ascending()
                else Sort.by(it.field).descending()
            }
        }

        val res = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.AgentPayment::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(sort)
                        .offset((pagination.limit * (pagination.page - 1)).toLong())
                        .limit(pagination.limit)
                ).all()
                .collectList()
                .block()
        }

        return AgentPaymentFilterResult(
            result = res?.map(agentPaymentMapper::toDomain)?.toList() ?: emptyList(),
            pagination = pagination.copy(
                totalCount = count,
                totalPage = if (count % pagination.limit == 0) count / pagination.limit else count / pagination.limit + 1
            )
        )
    }

    suspend fun findByDay(day: LocalDate): Flow<AgentPayment> {
        return agentPaymentRepository.findAllByTs(day).map { agentPaymentMapper.toDomain(it) }
    }

    fun findSalesByDay(day: LocalDate): Flow<AgentPayment> {
        return agentPaymentRepository.findAllByProcessTime(day).map { agentPaymentMapper.toDomain(it) }
    }

    fun findWritesByDay(day: LocalDate): Flow<AgentPayment> {
        return agentPaymentRepository.findAllByNotifyTime(day).map { agentPaymentMapper.toDomain(it) }
    }

    fun findServiceById(paymentId: String): Flow<AgentPaymentService> {
        return agentPaymentServiceRepository.byId(paymentId).map { agentPaymentServiceMapper.toDomain(it) }
    }
}