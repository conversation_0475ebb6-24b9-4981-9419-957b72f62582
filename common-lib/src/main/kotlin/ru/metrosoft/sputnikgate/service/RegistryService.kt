package ru.metrosoft.sputnikgate.service

import com.springml.sftp.client.SFTPClient
import kotlinx.coroutines.*
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.toSet
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.output.persistance.mapper.RegistryMapper
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentPaymentServiceRepository
import ru.metrosoft.sputnikgate.output.persistance.repository.RegistryRepository
import java.lang.Thread.sleep
import java.math.BigDecimal
import java.nio.file.Path
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import kotlin.concurrent.thread
import kotlin.io.path.deleteIfExists
import kotlin.io.path.exists

@Service
class RegistryService(
    private val agentPaymentService: AgentPaymentService,
    private val registryRepository: RegistryRepository,
    private val registryMapper: RegistryMapper,
    private val entityTemplate: R2dbcEntityOperations,
    private val agentPaymentServiceRepository: AgentPaymentServiceRepository,
    private val agentNBSService: AgentNBSRegistryService
) {

    companion object {
        val DATE_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

        val REGISTRY_DATE_FORMAT = DateTimeFormatter.ofPattern("yyMMdd")
    }

    suspend fun newRegistry(type: String, date: LocalDate, agentNBSId: UUID): Registry {
        val agentNBS = agentNBSService.getAgent(agentNBSId)

        val name =
            withContext(Dispatchers.IO) {
                when (type) {
                    "SALEREG" -> {
                        "salereg_${agentNBS.registryPrefix}_" + date.format(REGISTRY_DATE_FORMAT) + "_" + (registryRepository.countByTypeAndDate(type, date).block()!! + 1).toString().padStart(2, '0') + ".csv"
                    }
                    "WRITEREG" -> { "writeticketsreg_" + date.format(REGISTRY_DATE_FORMAT) + "_" + (registryRepository.countByTypeAndDate(type, date).block()!! + 1).toString().padStart(2, '0') + ".csv" }
                    "AGENT" -> { "AGENT_" + date.format(REGISTRY_DATE_FORMAT) + "_" + (registryRepository.countByTypeAndDate(type, date).block()!! + 1).toString().padStart(2, '0') + ".csv" }
                    else -> throw IllegalArgumentException()
                }
            }

        val content = when (type) {
            "SALEREG" -> { createSaleRegContent(date, agentNBSId) }
            "WRITEREG" -> { createWriteTicketsRegContent(date, agentNBSId) }
            "AGENT" -> { createAgentRegContent(date) }
            else -> throw IllegalArgumentException()
        }

        val registry = Registry(
            type = type,
            date = date,
            createdAt = LocalDateTime.now(),
            status = "CREATED",
            name = name,
            agentNBSId = agentNBSId
        )

        registry.content = content

        val ans = registryMapper.toDomain(registryRepository.save(registryMapper.toPersistModel(registry)))

        thread {
            val tempFile: Path = kotlin.io.path.createTempFile()

            tempFile.toFile().printWriter().use { out ->
                out.write(content)
                out.flush()
            }

            val client = SFTPClient("", agentNBS.SFTPUsername, agentNBS.SFTPPassword, agentNBS.SFTPUrl, agentNBS.SFTPPort!!.toInt())

            client.copyToFTP(tempFile.toAbsolutePath().toString(), name)

            tempFile.deleteIfExists()

            runBlocking {
                registryRepository.save(registryRepository.findById(ans.id!!)!!.copy(transferedAt = ZonedDateTime.now().toLocalDateTime(), status = "TRANSFERED"))
            }

            runUpdateThread(name, ans.id!!, agentNBSId)
        }

        return ans
    }

    private fun runUpdateThread(name: String, uuid: UUID, agentNBSId: UUID) {
        val agentNBS = runBlocking {
            agentNBSService.getAgent(agentNBSId)
        }

        thread {
            for (i in 1..8) {
                sleep(60000 * 5)
                val client = SFTPClient("", agentNBS.SFTPUsername, agentNBS.SFTPPassword, agentNBS.SFTPUrl, agentNBS.SFTPPort!!.toInt())

                val tempFile: Path = kotlin.io.path.createTempFile()
                val path = tempFile.toAbsolutePath().toString()
                tempFile.deleteIfExists()

                try {
                    client.copy("diff_" + name, path)

                    if (tempFile.exists()) {

                        tempFile.toFile().reader().use { out ->
                            runBlocking {
                                registryRepository.save(
                                    registryRepository.findById(uuid)!!.copy(
                                        processedAt = ZonedDateTime.now().toLocalDateTime(),
                                        status = "DIFF",
                                        diffContent = out.readText()
                                    )
                                )
                            }
                        }

                        tempFile.deleteIfExists()
                        return@thread
                    }
                } catch (ex: Exception) {
                    continue
                }

            }

            runBlocking {
                registryRepository.save(
                    registryRepository.findById(uuid)!!
                        .copy(processedAt = ZonedDateTime.now().toLocalDateTime(), status = "PROCESSED")
                )
            }

        }
    }

    /**
     * Определяет префикс для реестра продаж на основе типов платежей за указанный день.
     * FBT - если есть платежи с записью (PAYWRITE)
     * DWB - если есть только удаленные платежи (PAY)
     */
    private suspend fun determineSaleRegistryPrefix(day: LocalDate, agentNBSId: UUID): String {
        val payments = agentPaymentService.findSalesByDay(day)
            .filter { it.agentNBSId == agentNBSId }
            .filter { it.status }
            .filter { (it.type == AgentPaymentType.PAYWRITE && it.payStatus) || it.type == AgentPaymentType.PAY }
            .toSet()

        val hasPaywrite = payments.any { it.type == AgentPaymentType.PAYWRITE }
        val hasPay = payments.any { it.type == AgentPaymentType.PAY }

        return when {
            hasPaywrite -> "FBT"  // Есть платежи с записью - используем FBT
            hasPay -> "DWB"       // Есть только удаленные платежи - используем DWB
            else -> "FBT"         // По умолчанию FBT (если нет платежей или для обратной совместимости)
        }
    }

    suspend fun createSaleRegContent(day: LocalDate, agentNBSId: UUID): String {

        val payments = agentPaymentService.findSalesByDay(day)
            .filter { it.agentNBSId == agentNBSId }
            .filter { it.status }
            .filter { (it.type == AgentPaymentType.PAYWRITE && it.payStatus) || it.type == AgentPaymentType.PAY }
            .toSet()

        val content = StringBuilder()

        content.appendLine("Operation;PaymentID;CardNum;Amount;PaymentTime;ResourceAmount;TicketType;TicketService;OperationType;SAM")
        content.appendLine(payments.size.toString() + ";;;" + payments.fold(BigDecimal.ZERO) { acc, payment -> acc + payment.amount }.multiply(BigDecimal(100L)) + ";" + day.format(DATE_FORMAT) + ";;;;;")

        payments.forEach { p ->

            agentPaymentServiceRepository.byId(p._id).collect { s ->
                content.appendLine("1;" + p._id + ";" + p.cardNum + ";" + p.amount.multiply(BigDecimal(100L)) + ";" + p.processTime?.plusHours(3)?.format(TIME_FORMAT) + ";" + s.amount?.multiply(BigDecimal(100L)) + ";;" + s.serviceCode + ";" + (if (p.type == AgentPaymentType.PAYWRITE) 1 else 2) + ";")
            }
        }

        return content.toString()
    }

    suspend fun createWriteTicketsRegContent(day: LocalDate, agentNBSId: UUID): String {
        val payments = agentPaymentService.findWritesByDay(day)
            .filter { it.agentNBSId == agentNBSId }
            .filter { it.status }
            .filter { it.writeStatus }
            .filter { (it.type == AgentPaymentType.PAYWRITE) || (it.type == AgentPaymentType.WRITE) }
            .toSet()

        val content = StringBuilder()

        content.appendLine("TicketsWriteID;CardNum;WriteTime;TicketType;TicketService;Amount;SAM;WriteResult")
        content.appendLine(payments.size.toString() + ";;"+day.format(DATE_FORMAT)+";;;" + payments.fold(BigDecimal.ZERO) { acc, payment -> acc + payment.amount }.multiply(BigDecimal(100L)) + ";;")

        payments.forEach { p ->

            agentPaymentServiceRepository.byId(p._id).collect { s ->
                content.appendLine(p._id + ";" + p.cardNum + ";" + p.notifyTime?.plusHours(3)?.format(TIME_FORMAT) + ";;" + s.serviceCode + ";" + p.amount.multiply(BigDecimal(100L)) + ";;" + 1)
            }
        }

        return content.toString()
    }

    suspend fun createAgentRegContent(day: LocalDate): String {
        val payments = agentPaymentService.findSalesByDay(day)
            .filter { it.status }
            .filter { it.type == AgentPaymentType.PAYWRITE || it.type == AgentPaymentType.WRITE }
            .toSet()

        val content = StringBuilder()
        val domainZone = ZoneId.of("Europe/Moscow")

        content.appendLine("type;session;time;service_id;sum;num")
//        content.appendLine(payments.size.toString() + ";;;" + payments.fold(BigDecimal.ZERO) { acc, payment -> acc + payment.amount }.multiply(BigDecimal(100L)) + ";" + day.format(DATE_FORMAT) + ";;;;;")

        payments.forEach { p ->

            val type = if (p.type == AgentPaymentType.PAYWRITE) "PAYWRITE" else "WRITE"
            agentPaymentServiceRepository.byId(p._id).collect { s ->
                val time = if (p.notifyTime == null) p.processTime?.plusHours(3)?.format(TIME_FORMAT) else p.notifyTime?.plusHours(3)?.format(TIME_FORMAT)
                val formattedTime: String = if (time.isNullOrBlank()) {
                    ZonedDateTime.now().format(TIME_FORMAT)
                } else {
                    time
                }
                content.appendLine(type + ";" + p._id + ";" + formattedTime + ";" + s.serviceCode + ";" + p.amount + ";" + p.cardNum + ";")
            }
        }

        return content.toString()
    }

    suspend fun findRegistries(filter: RegistryFilter, pagination: Pagination): RegistryFilterResult {
        val search = mutableListOf<Criteria>()
        filter.sessionId?.let {
            if (it.isNotEmpty()) search.add(Criteria.where("id").`is`(it))
        }
        filter.tsStart?.let {
            search.add(Criteria.where("ts").greaterThanOrEquals(it))
        }
        filter.tsEnd?.let {
            search.add(Criteria.where("ts").lessThan(it))
        }

        val count = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.Registry::class.java)
                .matching(Query.query(Criteria.from(search)))
                .count().block()
        }?.toInt()!!

        val sort: Sort = if (filter.sort.isEmpty()) Sort.by("day").descending() else {
            filter.sort.first().let {
                if (it.sort == ru.metrosoft.sputnikgate.model.Sort.ASC) Sort.by(it.field).ascending()
                else Sort.by(it.field).descending()
            }
        }

        val res = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.Registry::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(sort)
                        .offset((pagination.limit * (pagination.page - 1)).toLong())
                        .limit(pagination.limit)
                ).all()
                .collectList()
                .block()
        }

        return RegistryFilterResult(
            result = res?.map(registryMapper::toDomain)?.toList() ?: emptyList(),
            pagination = pagination.copy(
                totalCount = count,
                totalPage = if (count % pagination.limit == 0) count / pagination.limit else count / pagination.limit + 1
            )
        )
    }

    suspend fun byId(registryId: String) : Registry {
        return registryMapper.toDomain(registryRepository.findById(UUID.fromString(registryId))!!)
    }
}