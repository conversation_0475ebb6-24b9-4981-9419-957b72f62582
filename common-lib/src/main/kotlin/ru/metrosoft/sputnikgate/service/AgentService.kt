package ru.metrosoft.sputnikgate.service

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.reactor.asFlux
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import ru.metrosoft.sputnikgate.model.Agent
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentMapper
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentRepository
import java.util.*

@Service
class AgentService(
    val agentRepository: AgentRepository,
    val agentMapper: AgentMapper
) {

    suspend fun getAgentByAuthToken(authToken: String): Either<String, Agent> {
        val agent = agentRepository.findById(UUID.fromString(authToken)) ?: return "Agent not found".left()

        return agentMapper.toDomain(agent).right()
    }

    suspend fun getAgent(id: UUID): Agent {
        val agent = agentRepository.findById(id) ?: throw IllegalArgumentException("Agent not found")

        return agentMapper.toDomain(agent)
    }

    fun getAgents(): Flux<Agent> {
        return agentRepository.findAll().map(agentMapper::toDomain).asFlux()
    }

    suspend fun getAgentList(): List<Agent> {
        val ret = mutableListOf<Agent>()
        agentRepository.findAll().map(agentMapper::toDomain).toList(ret)
        return ret
    }

    suspend fun saveAgent(agent: Agent): Agent {
        return agentMapper.toDomain(agentRepository.save(agentMapper.toPersistModel(agent)))
    }

//    suspend fun getAllAgents(): Either<String, List<Agent>> {
//        return Either.Right( emptyList<Agent>() )
//    }
//
//    suspend fun createAgent(agent: Agent): Agent {
//        val repoAgent = agentRepository.save(agentMapper.toPersistModel(agent))
//        return agentMapper.toDomain(repoAgent)
//    }

}
