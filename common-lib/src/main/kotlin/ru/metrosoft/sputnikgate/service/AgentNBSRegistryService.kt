package ru.metrosoft.sputnikgate.service

import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.metrosoft.sputnikgate.model.AgentNBSRegistry
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentNBSRegistryMapper
import ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentNBSRepository
import java.util.*

@Service
class AgentNBSRegistryService(
    val agentNBSRepository: AgentNBSRepository,
    val agentNBSMapper: AgentNBSRegistryMapper
) {
    suspend fun getAgent(agentNBSId: UUID) : AgentNBSRegistry {
        return agentNBSMapper.toDomain(agentNBSRepository.findById(agentNBSId)!!)
    }

    suspend fun getAgentList(): List<AgentNBSRegistry> {
        val list = mutableListOf<AgentNBS>()
        agentNBSRepository.findAll().toList(list)
        return list.map { agentNBSMapper.toDomain(it) }
    }


}