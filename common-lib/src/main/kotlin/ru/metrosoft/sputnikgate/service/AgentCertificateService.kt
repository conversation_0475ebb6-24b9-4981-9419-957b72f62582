package ru.metrosoft.sputnikgate.service

import org.springframework.stereotype.Service
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentCertificateMapper
import ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentCertificateRepository
import java.util.*

@Service
class AgentCertificateService(
    private val agentCertificateRepository: AgentCertificateRepository,
    private val agentCertificateMapper: AgentCertificateMapper
) {

    suspend fun getAgentCertificate(id: UUID): AgentCertificate {
        val persisted = agentCertificateRepository.findById(id)
            ?: throw IllegalArgumentException("AgentCertificate not found")
        return agentCertificateMapper.toDomain(persisted)
    }

    suspend fun saveAgentCertificate(certificate: AgentCertificate): AgentCertificate {
        val saved = agentCertificateRepository.save(agentCertificateMapper.toPersistModel(certificate))
        return agentCertificateMapper.toDomain(saved)
    }
}