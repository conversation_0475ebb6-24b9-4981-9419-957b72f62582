package ru.metrosoft.sputnikgate.service

import kotlinx.coroutines.flow.map
import kotlinx.coroutines.reactor.asFlux
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import ru.metrosoft.sputnikgate.model.AgentContract
import ru.metrosoft.sputnikgate.output.persistance.mapper.AgentContractMapper
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentContractRepository
import java.util.*

@Service
class AgentContractService(
    private val agentContractRepository: AgentContractRepository,
    private val agentContractMapper: AgentContractMapper
) {

    suspend fun getAgentContract(id: UUID): AgentContract {
        val agent = agentContractRepository.findById(id) ?: throw IllegalArgumentException("AgentContract not found")

        return agentContractMapper.toDomain(agent)
    }

    suspend fun getAllAgentContract(id: UUID): Flux<AgentContract> {
        return agentContractRepository.findAllByAgentId(id).map(agentContractMapper::toDomain).asFlux()
    }

    fun getAgentContracts(): Flux<AgentContract> {
        return agentContractRepository.findAll().map(agentContractMapper::toDomain).asFlux()
    }

    suspend fun saveAgentContract(contract: AgentContract): AgentContract {
        return agentContractMapper.toDomain(agentContractRepository.save(agentContractMapper.toPersistModel(contract)))
    }
}