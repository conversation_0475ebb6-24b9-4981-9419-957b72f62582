group 'ru.metrosoft'
version '1.0.0'

buildscript {
    ext.kotlin_version = '1.9.23'
    ext.spotless_version = "6.25.0"

    repositories {
        maven { url "https://repo1.maven.org/maven2" }
    }
    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "com.diffplug.spotless:spotless-plugin-gradle:$spotless_version"
    }
}

apply plugin: 'java'
apply plugin: 'kotlin'
apply plugin: 'maven-publish'
apply plugin: 'com.diffplug.spotless'

repositories {
    mavenCentral()
    maven { url 'https://repo.spring.io/milestone' }
}

// Use spotless plugin to automatically format code, remove unused import, etc
// To apply changes directly to the file, run `gradlew spotlessApply`
// Ref: https://github.com/diffplug/spotless/tree/main/plugin-gradle
spotless {
    // comment out below to run spotless as part of the `check` task
    enforceCheck false

    format 'misc', {
        // define the files (e.g. '*.gradle', '*.md') to apply `misc` to
        target '.gitignore'

        // define the steps to apply to those files
        trimTrailingWhitespace()
        indentWithSpaces() // Takes an integer argument if you don't like 4
        endWithNewline()
    }
    kotlin {
        ktfmt()
    }
}

test {
    useJUnitPlatform()
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"
    implementation "com.squareup.moshi:moshi-kotlin:1.15.1"
    implementation "com.squareup.moshi:moshi-adapters:1.15.1"
    implementation "com.squareup.okhttp3:okhttp:4.12.0"
    implementation("com.squareup.okhttp3:logging-interceptor:4.12.0")

    implementation "io.r2:simple-pem-keystore:0.3"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jacksonModuleKotlinVersion"

    testImplementation "io.kotlintest:kotlintest-runner-junit5:3.4.2"
}
