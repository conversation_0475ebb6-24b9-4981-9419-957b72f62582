
# AgentTicket

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **lifetimeEnd** | **kotlin.String** | Срок годности билета. |  [optional] |
| **code** | **kotlin.String** | Код. |  [optional] |
| **name** | **kotlin.String** | Название. |  [optional] |
| **number** | **kotlin.String** | Номер. |  [optional] |
| **status** | [**AgentTicketStatus**](AgentTicketStatus.md) |  |  [optional] |
| **currentServices** | [**kotlin.collections.List&lt;AgentTicketService&gt;**](AgentTicketService.md) | Список текущих услуг транспортного приложения, записанных на Носитель. Может быть пустым. |  [optional] |
| **unwrittenServices** | [**kotlin.collections.List&lt;AgentTicketService&gt;**](AgentTicketService.md) | Список услуг отложенных ЭБ транспортного приложения, ожидающих записи. |  [optional] |
| **availableServices** | [**kotlin.collections.List&lt;AgentTicketAvailableService&gt;**](AgentTicketAvailableService.md) | Список доступных для покупки ЭБ. |  [optional] |
| **expiredServices** | [**kotlin.collections.List&lt;AgentTicketService&gt;**](AgentTicketService.md) | Список просроченных услуг, срок действия которых истек и поэтому они уже никогда не будут записаны. Просроченные услуги находятся в этом списке настраиваемый период времени, далее убираются из него. |  [optional] |



