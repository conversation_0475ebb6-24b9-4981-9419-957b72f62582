
# ReadResponse

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **state** | **kotlin.Int** | Текущее состояние сессии. |  [optional] |
| **cardCommand** | **kotlin.ByteArray** | Команда для отправки на носитель. |  [optional] |
| **num** | **kotlin.String** | Печатный номер Носителя. |  [optional] |
| **tickets** | [**kotlin.collections.List&lt;AgentTicket&gt;**](AgentTicket.md) | Список билетов. |  [optional] |



