# ExternalInfoApi

All URIs are relative to *https://api-partners.mosmetro.ru/tks.ticketspayment*

| Method | HTTP request | Description |
| ------------- | ------------- | ------------- |
| [**externalInfoGetCardByUid**](ExternalInfoApi.md#externalInfoGetCardByUid) | **POST** /v1/get_card_by_uid | Запрос информации о носителе по его UID. |
| [**externalInfoGetNum**](ExternalInfoApi.md#externalInfoGetNum) | **POST** /v1/get_num | Запрос печатного номера Носителя по UID. |
| [**externalInfoGetUid**](ExternalInfoApi.md#externalInfoGetUid) | **POST** /v1/get_uid | Запрос UID Носителя по его транспортному номеру. |
| [**externalInfoPing**](ExternalInfoApi.md#externalInfoPing) | **GET** /v1/ping | Проверка доступности канала связи. |


<a id="externalInfoGetCardByUid"></a>
# **externalInfoGetCardByUid**
> GetCardResponse externalInfoGetCardByUid(uid)

Запрос информации о носителе по его UID.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = ExternalInfoApi()
val uid : kotlin.String = uid_example // kotlin.String | UID Носителя в шестнадцатеричном представлении.
try {
    val result : GetCardResponse = apiInstance.externalInfoGetCardByUid(uid)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling ExternalInfoApi#externalInfoGetCardByUid")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling ExternalInfoApi#externalInfoGetCardByUid")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **uid** | **kotlin.String**| UID Носителя в шестнадцатеричном представлении. | |

### Return type

[**GetCardResponse**](GetCardResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="externalInfoGetNum"></a>
# **externalInfoGetNum**
> GetNumResponse externalInfoGetNum(uid)

Запрос печатного номера Носителя по UID.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = ExternalInfoApi()
val uid : kotlin.String = uid_example // kotlin.String | UID Носителя в шестнадцатеричном представлении.
try {
    val result : GetNumResponse = apiInstance.externalInfoGetNum(uid)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling ExternalInfoApi#externalInfoGetNum")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling ExternalInfoApi#externalInfoGetNum")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **uid** | **kotlin.String**| UID Носителя в шестнадцатеричном представлении. | |

### Return type

[**GetNumResponse**](GetNumResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="externalInfoGetUid"></a>
# **externalInfoGetUid**
> AgentGetUidResponse externalInfoGetUid(num)

Запрос UID Носителя по его транспортному номеру.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = ExternalInfoApi()
val num : kotlin.String = num_example // kotlin.String | Транспортный номер носителя.
try {
    val result : AgentGetUidResponse = apiInstance.externalInfoGetUid(num)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling ExternalInfoApi#externalInfoGetUid")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling ExternalInfoApi#externalInfoGetUid")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **num** | **kotlin.String**| Транспортный номер носителя. | |

### Return type

[**AgentGetUidResponse**](AgentGetUidResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="externalInfoPing"></a>
# **externalInfoPing**
> kotlin.String externalInfoPing()

Проверка доступности канала связи.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = ExternalInfoApi()
try {
    val result : kotlin.String = apiInstance.externalInfoPing()
    println(result)
} catch (e: ClientException) {
    println("4xx response calling ExternalInfoApi#externalInfoPing")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling ExternalInfoApi#externalInfoPing")
    e.printStackTrace()
}
```

### Parameters
This endpoint does not need any parameter.

### Return type

**kotlin.String**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

