# CityServiceApi

All URIs are relative to *https://api-partners.mosmetro.ru/tks.ticketspayment*

| Method | HTTP request | Description |
| ------------- | ------------- | ------------- |
| [**cityServiceCancelPass**](CityServiceApi.md#cityServiceCancelPass) | **POST** /v1/cancel_pass | Отмена списания. |
| [**cityServiceProcessPass**](CityServiceApi.md#cityServiceProcessPass) | **POST** /v1/process_pass | Подтверждение списания. |
| [**cityServiceRequestWalletPass**](CityServiceApi.md#cityServiceRequestWalletPass) | **POST** /v1/request_wallet_pass | Запрос на спиcание ЭК. |


<a id="cityServiceCancelPass"></a>
# **cityServiceCancelPass**
> cityServiceCancelPass(session)

Отмена списания.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CityServiceApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
try {
    apiInstance.cityServiceCancelPass(session)
} catch (e: ClientException) {
    println("4xx response calling CityServiceApi#cityServiceCancelPass")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CityServiceApi#cityServiceCancelPass")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **session** | **kotlin.String**| Сессия. | |

### Return type

null (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="cityServiceProcessPass"></a>
# **cityServiceProcessPass**
> cityServiceProcessPass(session)

Подтверждение списания.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CityServiceApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
try {
    apiInstance.cityServiceProcessPass(session)
} catch (e: ClientException) {
    println("4xx response calling CityServiceApi#cityServiceProcessPass")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CityServiceApi#cityServiceProcessPass")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **session** | **kotlin.String**| Сессия. | |

### Return type

null (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="cityServiceRequestWalletPass"></a>
# **cityServiceRequestWalletPass**
> AgentRequestWalletPassResponse cityServiceRequestWalletPass(agentRequestWalletPassRequest)

Запрос на спиcание ЭК.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CityServiceApi()
val agentRequestWalletPassRequest : AgentRequestWalletPassRequest =  // AgentRequestWalletPassRequest | Данные запроса.
try {
    val result : AgentRequestWalletPassResponse = apiInstance.cityServiceRequestWalletPass(agentRequestWalletPassRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CityServiceApi#cityServiceRequestWalletPass")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CityServiceApi#cityServiceRequestWalletPass")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **agentRequestWalletPassRequest** | [**AgentRequestWalletPassRequest**](AgentRequestWalletPassRequest.md)| Данные запроса. | |

### Return type

[**AgentRequestWalletPassResponse**](AgentRequestWalletPassResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

