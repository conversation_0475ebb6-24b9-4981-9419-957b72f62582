
# AgentRequestWalletPassResponse

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **session** | **kotlin.String** | Идентификатор сессии списания. |  |
| **blocks** | [**kotlin.collections.List&lt;AgentWriteBlock&gt;**](AgentWriteBlock.md) | Блоки для записи. |  |
| **passTime** | [**java.time.OffsetDateTime**](java.time.OffsetDateTime.md) | Дата и время списания. |  |
| **balanceOnCard** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Баланс на карте. |  [optional] |



