
# WalletPassRequest

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **clientSessionId** | **kotlin.String** | Идентификатор сессии. 16 байт в шестнадцатиричном формате. |  [optional] |
| **uid** | **kotlin.ByteArray** | UID Носителя в шестнадцатеричном представлении. |  [optional] |
| **amount** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Сумма списания. |  [optional] |
| **state** | **kotlin.Int** | Номер состояния запроса |  [optional] |
| **cardResponse** | **kotlin.ByteArray** | Ответ носителя на команду, отправленную ранее. |  [optional] |



