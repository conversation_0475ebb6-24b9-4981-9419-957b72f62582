
# AgentWriteBlock

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **block** | **kotlin.Int** | Сквозной номер блока (начиная с нуля) |  |
| **&#x60;value&#x60;** | **kotlin.String** | Содержимое блока в виде строки в шестнадцатеричном представлении |  |
| **writeKeyType** | **kotlin.String** | Тип ключа записи, A или B. По умолчанию A |  |
| **writeKeyId** | **kotlin.Int** | Идентификатор ключа записи в SAM |  |
| **writeKeyVersion** | **kotlin.Int** | Версия ключа записи в SAM. По умолчанию 0 |  |
| **writeKeyValue** | **kotlin.String** | Значение ключа для записи. |  |
| **readKeyId** | **kotlin.Int** | Идентификатор ключа чтения в SAM |  [optional] |
| **readKeyVersion** | **kotlin.Int** | Версия ключа чтения в SAM. По умолчанию 0 |  [optional] |
| **readKeyValue** | **kotlin.String** | Значение ключа для чтения. |  [optional] |
| **readKeyType** | **kotlin.String** | Тип ключа чтения, A или B. По умолчанию A |  [optional] |



