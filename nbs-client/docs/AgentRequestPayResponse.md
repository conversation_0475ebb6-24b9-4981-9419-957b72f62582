
# AgentRequestPayResponse

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **session** | **kotlin.String** | Сессия. |  [optional] |
| **cardSerialNumber** | **kotlin.Long** | Номер кристалла (чипа) карты. |  [optional] |
| **uid** | **kotlin.String** | UID носителя в HEX. |  [optional] |
| **tickets** | [**kotlin.collections.List&lt;AgentTicket&gt;**](AgentTicket.md) | Список билетов. |  [optional] |



