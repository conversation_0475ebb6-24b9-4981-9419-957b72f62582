# TicketsPaymentApi

All URIs are relative to *https://api-partners.mosmetro.ru/tks.ticketspayment*

| Method | HTTP request | Description |
| ------------- | ------------- | ------------- |
| [**ticketsPaymentCancelPay**](TicketsPaymentApi.md#ticketsPaymentCancelPay) | **POST** /v1/cancel_pay | Отмена удаленного пополнения. |
| [**ticketsPaymentProcessPay**](TicketsPaymentApi.md#ticketsPaymentProcessPay) | **POST** /v1/process_pay | Подтверждение удаленного пополнения. |
| [**ticketsPaymentRequestPay**](TicketsPaymentApi.md#ticketsPaymentRequestPay) | **POST** /v1/request_pay | Запрос удаленного пополнения. |
| [**ticketsPaymentRequestPayByUid**](TicketsPaymentApi.md#ticketsPaymentRequestPayByUid) | **POST** /v1/request_pay_by_uid | Запрос удаленного пополнения по UID. |


<a id="ticketsPaymentCancelPay"></a>
# **ticketsPaymentCancelPay**
> ticketsPaymentCancelPay(session)

Отмена удаленного пополнения.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = TicketsPaymentApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
try {
    apiInstance.ticketsPaymentCancelPay(session)
} catch (e: ClientException) {
    println("4xx response calling TicketsPaymentApi#ticketsPaymentCancelPay")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling TicketsPaymentApi#ticketsPaymentCancelPay")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **session** | **kotlin.String**| Сессия. | |

### Return type

null (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="ticketsPaymentProcessPay"></a>
# **ticketsPaymentProcessPay**
> AgentProcessPayResponse ticketsPaymentProcessPay(session, agentProcessPayRequest)

Подтверждение удаленного пополнения.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = TicketsPaymentApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
val agentProcessPayRequest : AgentProcessPayRequest =  // AgentProcessPayRequest | Запрос.
try {
    val result : AgentProcessPayResponse = apiInstance.ticketsPaymentProcessPay(session, agentProcessPayRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling TicketsPaymentApi#ticketsPaymentProcessPay")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling TicketsPaymentApi#ticketsPaymentProcessPay")
    e.printStackTrace()
}
```

### Parameters
| **session** | **kotlin.String**| Сессия. | |
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **agentProcessPayRequest** | [**AgentProcessPayRequest**](AgentProcessPayRequest.md)| Запрос. | |

### Return type

[**AgentProcessPayResponse**](AgentProcessPayResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="ticketsPaymentRequestPay"></a>
# **ticketsPaymentRequestPay**
> AgentRequestPayResponse ticketsPaymentRequestPay(num)

Запрос удаленного пополнения.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = TicketsPaymentApi()
val num : kotlin.String = num_example // kotlin.String | Транспортный номер носителя.
try {
    val result : AgentRequestPayResponse = apiInstance.ticketsPaymentRequestPay(num)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling TicketsPaymentApi#ticketsPaymentRequestPay")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling TicketsPaymentApi#ticketsPaymentRequestPay")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **num** | **kotlin.String**| Транспортный номер носителя. | |

### Return type

[**AgentRequestPayResponse**](AgentRequestPayResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="ticketsPaymentRequestPayByUid"></a>
# **ticketsPaymentRequestPayByUid**
> AgentRequestPayResponse ticketsPaymentRequestPayByUid(uid)

Запрос удаленного пополнения по UID.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = TicketsPaymentApi()
val uid : kotlin.String = uid_example // kotlin.String | UID носителя.
try {
    val result : AgentRequestPayResponse = apiInstance.ticketsPaymentRequestPayByUid(uid)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling TicketsPaymentApi#ticketsPaymentRequestPayByUid")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling TicketsPaymentApi#ticketsPaymentRequestPayByUid")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **uid** | **kotlin.String**| UID носителя. | |

### Return type

[**AgentRequestPayResponse**](AgentRequestPayResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

