
# PayWriteRequest

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **clientSessionId** | **kotlin.String** | Идентификатор сессии, использованный в запросе read. |  [optional] |
| **state** | **kotlin.Int** | Номер состояния запроса. |  [optional] |
| **service** | [**PayWriteRequestService**](PayWriteRequestService.md) |  |  [optional] |
| **agentParams** | [**kotlin.collections.List&lt;AgentTicketPaymentParamDto&gt;**](AgentTicketPaymentParamDto.md) | Параметры платежа агента. |  [optional] |
| **cardResponse** | **kotlin.ByteArray** | Ответ носителя на команду, отправленную ранее. |  [optional] |



