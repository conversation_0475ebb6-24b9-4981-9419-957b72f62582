
# WalletPassResponse

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **state** | **kotlin.Int** | Текущее состояние сессии. |  [optional] |
| **cardCommand** | **kotlin.ByteArray** | Команда для отправки на носитель. |  [optional] |
| **balanceOnCard** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Баланс на носителе после списания. |  [optional] |



