
# AgentTicketService

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **active** | **kotlin.Boolean** | Признак действующего билета. |  [optional] |
| **id** | **kotlin.Int** | Идентификатор услуги. |  [optional] |
| **name** | **kotlin.String** | Наименование услуги (краткое). |  [optional] |
| **session** | **kotlin.String** | Идентификатор сессии (транзакции), в рамках которой была приобретена эта услуга. |  [optional] |
| **payTime** | [**java.time.OffsetDateTime**](java.time.OffsetDateTime.md) | Дата и время оплаты. |  [optional] |
| **sum** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Сумма. |  [optional] |
| **params** | [**kotlin.collections.List&lt;AgentTicketPaymentParam&gt;**](AgentTicketPaymentParam.md) | Параметры. |  [optional] |
| **prolongation** | **kotlin.Boolean** | Признак продлённого билета. |  [optional] |



