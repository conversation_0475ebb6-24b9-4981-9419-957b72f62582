# CloudSamApi

All URIs are relative to *https://api-partners.mosmetro.ru/tks.ticketspayment*

| Method | HTTP request | Description |
| ------------- | ------------- | ------------- |
| [**cloudSamCancelPayWrite**](CloudSamApi.md#cloudSamCancelPayWrite) | **POST** /v1/cloud/cancel-pay-write | Отмена прямой записи. |
| [**cloudSamCancelWalletPass**](CloudSamApi.md#cloudSamCancelWalletPass) | **POST** /v1/cloud/cancel-wallet-pass | Отмена списания. |
| [**cloudSamCancelWriteUnwritten**](CloudSamApi.md#cloudSamCancelWriteUnwritten) | **POST** /v1/cloud/cancel-write-unwritten | Отмена записи отложенного. |
| [**cloudSamPayWrite**](CloudSamApi.md#cloudSamPayWrite) | **POST** /v1/cloud/pay-write | Покупка и запись билета на носитель. |
| [**cloudSamRead**](CloudSamApi.md#cloudSamRead) | **POST** /v1/cloud/read | Запрос информации о билетах на носителе. |
| [**cloudSamWalletPass**](CloudSamApi.md#cloudSamWalletPass) | **POST** /v1/cloud/wallet-pass | Списание средств с ЭК носителя. |
| [**cloudSamWriteUnwritten**](CloudSamApi.md#cloudSamWriteUnwritten) | **POST** /v1/cloud/write-unwritten | Запись отложенного пополнения на носитель. |


<a id="cloudSamCancelPayWrite"></a>
# **cloudSamCancelPayWrite**
> CancelPayWriteResponse cloudSamCancelPayWrite(cancelPayWriteRequest)

Отмена прямой записи.

Если в ответе ConsiderAsWritten содержит true, то транзакция всё равно считается успешной.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CloudSamApi()
val cancelPayWriteRequest : CancelPayWriteRequest =  // CancelPayWriteRequest | Запрос.
try {
    val result : CancelPayWriteResponse = apiInstance.cloudSamCancelPayWrite(cancelPayWriteRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CloudSamApi#cloudSamCancelPayWrite")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CloudSamApi#cloudSamCancelPayWrite")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **cancelPayWriteRequest** | [**CancelPayWriteRequest**](CancelPayWriteRequest.md)| Запрос. | |

### Return type

[**CancelPayWriteResponse**](CancelPayWriteResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="cloudSamCancelWalletPass"></a>
# **cloudSamCancelWalletPass**
> CancelWalletPassResponse cloudSamCancelWalletPass(cancelWalletPassRequest)

Отмена списания.

Если в ответе ConsiderAsWritten содержит true, то транзакция всё равно считается успешной.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CloudSamApi()
val cancelWalletPassRequest : CancelWalletPassRequest =  // CancelWalletPassRequest | Запрос.
try {
    val result : CancelWalletPassResponse = apiInstance.cloudSamCancelWalletPass(cancelWalletPassRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CloudSamApi#cloudSamCancelWalletPass")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CloudSamApi#cloudSamCancelWalletPass")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **cancelWalletPassRequest** | [**CancelWalletPassRequest**](CancelWalletPassRequest.md)| Запрос. | |

### Return type

[**CancelWalletPassResponse**](CancelWalletPassResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="cloudSamCancelWriteUnwritten"></a>
# **cloudSamCancelWriteUnwritten**
> CancelWriteUnwrittenResponse cloudSamCancelWriteUnwritten(cancelWriteUnwrittenRequest)

Отмена записи отложенного.

Если в ответе ConsiderAsWritten содержит true, то транзакция всё равно считается успешной.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CloudSamApi()
val cancelWriteUnwrittenRequest : CancelWriteUnwrittenRequest =  // CancelWriteUnwrittenRequest | Запрос.
try {
    val result : CancelWriteUnwrittenResponse = apiInstance.cloudSamCancelWriteUnwritten(cancelWriteUnwrittenRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CloudSamApi#cloudSamCancelWriteUnwritten")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CloudSamApi#cloudSamCancelWriteUnwritten")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **cancelWriteUnwrittenRequest** | [**CancelWriteUnwrittenRequest**](CancelWriteUnwrittenRequest.md)| Запрос. | |

### Return type

[**CancelWriteUnwrittenResponse**](CancelWriteUnwrittenResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="cloudSamPayWrite"></a>
# **cloudSamPayWrite**
> PayWriteResponse cloudSamPayWrite(payWriteRequest)

Покупка и запись билета на носитель.

Фиксируется оплачиваемая услуга и записывается на носитель.              Этот запрос вызывается после получения успешного ответа на запрос read. В качестве значения параметра *clientSessionId* передаётся то же значение, что было использовано в запросе read.              Данный метод вызывается несколько раз до получения информации о записанном билете на носителе или сообщения об ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать данный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.              В первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное из ответа на предыдущий запрос, и ответ носителя.              Пример.              Запрос на пополнение ЭК на 120 рублей:                  {         \&quot;clientSessionId\&quot;: \&quot;1b1fb55513e446d69c4c14f2f4d3786c\&quot;,         \&quot;state\&quot;: 0,         \&quot;service\&quot;: {             \&quot;service_id\&quot;: 433,             \&quot;sum\&quot;: 120.0         },         \&quot;agentParams\&quot;: [         {             \&quot;id\&quot;: \&quot;pay_type\&quot;,             \&quot;value\&quot;: \&quot;CARD\&quot;         },         {             \&quot;id\&quot;: \&quot;pos\&quot;,             \&quot;value\&quot;: \&quot;14180\&quot;         }         ]     }              Ответ на последний запрос:                  {         \&quot;state\&quot;: 0,         \&quot;services\&quot;: [         {             \&quot;id\&quot;: 433,             \&quot;name\&quot;: \&quot;КОШЕЛЕК\&quot;,             \&quot;session\&quot;: \&quot;ee338399-d60a-4224-bdd3-2323aea7c0d6\&quot;,             \&quot;pay_time\&quot;: \&quot;2023-10-03T11:13:33+03:00\&quot;,             \&quot;sum\&quot;: 120.0,             \&quot;params\&quot;: [             {                 \&quot;id\&quot;: \&quot;sum\&quot;,                 \&quot;value\&quot;: \&quot;120.00\&quot;             },             {                 \&quot;id\&quot;: \&quot;valid_interval\&quot;,                 \&quot;value\&quot;: \&quot;06.11.2024\&quot;             }             ]         }         ]     }

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CloudSamApi()
val payWriteRequest : PayWriteRequest =  // PayWriteRequest | Запрос.
try {
    val result : PayWriteResponse = apiInstance.cloudSamPayWrite(payWriteRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CloudSamApi#cloudSamPayWrite")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CloudSamApi#cloudSamPayWrite")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **payWriteRequest** | [**PayWriteRequest**](PayWriteRequest.md)| Запрос. | |

### Return type

[**PayWriteResponse**](PayWriteResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="cloudSamRead"></a>
# **cloudSamRead**
> ReadResponse cloudSamRead(readRequest)

Запрос информации о билетах на носителе.

Запрос read является первым запросом для записи отложенного пополнения и покупки билета с записью на носитель.              Данный метод вызывается несколько раз до получения информации о билетах на носителе или сообщения об ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать данный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.              В первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное из ответа на предыдущий запрос, и ответ носителя.              Пример.              1. Запрос информации о носителе 04145A9AC25B80:                     {            \&quot;clientSessionId\&quot;: \&quot;7F956ACE60F711EEB21DFB803D876A30\&quot;,            \&quot;state\&quot;: 0            \&quot;uid\&quot;: \&quot;BBRamsJbgA&#x3D;&#x3D;\&quot;        }                 Ответ на запрос будет содержать новое значение *state* и APDU-команду, которую необходимо отправить на носитель:                     {            \&quot;state\&quot;: 1,            \&quot;cardCommand\&quot;: \&quot;cBFAAA&#x3D;&#x3D;\&quot;        }              2. Если в ответе присутствует команда для носителя *cardCommand*, то результат её выполнения необходимо    отправить следующим запросом, указав в запросе значение *state* равное полученному значению из ответа с    командой:                     {            \&quot;clientSessionId\&quot;: \&quot;7F956ACE60F711EEB21DFB803D876A30\&quot;,            \&quot;uid\&quot;: \&quot;BBRamsJbgA&#x3D;&#x3D;\&quot;,            \&quot;state\&quot;: 1,            \&quot;cardResponse\&quot;: \&quot;kD3WFH/XzaKkpMcvs1RWlCg&#x3D;\&quot;        }                 Ответ на запрос будет содержать следующую команду и новое значение параметра *state*:                     {            \&quot;state\&quot;: 2,            \&quot;cardCommand\&quot;: \&quot;ctV873dVlOk6pioDN50l7bdfVuxPjx/kfhpKoECt/C+b\&quot;        }              3. После успешного чтения всех требуемых данных на очередной запрос от сервера будет получен ответ, в котором    нет команды для носителя *cardCommand* и *state&#x3D;0*:                     {          \&quot;state\&quot;: 0,          \&quot;num\&quot;: \&quot;0040149455\&quot;,          \&quot;tickets\&quot;: [            {              \&quot;lifetime_end\&quot;: \&quot;2028-03-06\&quot;,              \&quot;code\&quot;: \&quot;MOSGORTRANS\&quot;,              \&quot;name\&quot;: \&quot;Билет на наземный транспорт\&quot;,              \&quot;number\&quot;: \&quot;0040149455\&quot;,              \&quot;status\&quot;: {                \&quot;code\&quot;: \&quot;OK\&quot;              },              \&quot;current_services\&quot;: [],              \&quot;unwritten_services\&quot;: [],              \&quot;available_services\&quot;: [                {                  \&quot;id\&quot;: 800,                  \&quot;name\&quot;: \&quot;30 дней ТАТ\&quot;,                  \&quot;price_min\&quot;: 1560.0,                  \&quot;price_max\&quot;: 1560.0,                  \&quot;expiration_period\&quot;: 738832,                  \&quot;expiration_type\&quot;: \&quot;PAYMENT\&quot;                },                {                  \&quot;id\&quot;: 801,                  \&quot;name\&quot;: \&quot;90 дней ТАТ\&quot;,                  \&quot;price_min\&quot;: 4140.0,                  \&quot;price_max\&quot;: 4140.0,                  \&quot;expiration_period\&quot;: 738892,                  \&quot;expiration_type\&quot;: \&quot;PAYMENT\&quot;                }              ]            },            {              \&quot;lifetime_end\&quot;: \&quot;2024-11-07\&quot;,              \&quot;code\&quot;: \&quot;WALLET\&quot;,              \&quot;name\&quot;: \&quot;Электронный кошелек\&quot;,              \&quot;number\&quot;: \&quot;0040149455\&quot;,              \&quot;status\&quot;: {                \&quot;code\&quot;: \&quot;OK\&quot;              },              \&quot;current_services\&quot;: [                {                  \&quot;active\&quot;: true,                  \&quot;id\&quot;: 433,                  \&quot;name\&quot;: \&quot;КОШЕЛЕК\&quot;,                  \&quot;params\&quot;: [                    {                      \&quot;id\&quot;: \&quot;sum\&quot;,                      \&quot;value\&quot;: \&quot;1000.00\&quot;                    }                  ]                }              ],              \&quot;unwritten_services\&quot;: [],              \&quot;available_services\&quot;: [                {                  \&quot;id\&quot;: 433,                  \&quot;name\&quot;: \&quot;КОШЕЛЕК\&quot;,                  \&quot;price_min\&quot;: 1.0,                  \&quot;price_max\&quot;: 9000.0,                  \&quot;expiration_period\&quot;: 739195,                  \&quot;expiration_type\&quot;: \&quot;PAYMENT\&quot;                }              ]            },            {              \&quot;lifetime_end\&quot;: \&quot;2028-03-06\&quot;,              \&quot;code\&quot;: \&quot;UNICARD\&quot;,              \&quot;name\&quot;: \&quot;Билет на метро и наземный транспорт\&quot;,              \&quot;number\&quot;: \&quot;0040149455\&quot;,              \&quot;status\&quot;: {                \&quot;code\&quot;: \&quot;OK\&quot;              },              \&quot;current_services\&quot;: [],              \&quot;unwritten_services\&quot;: [],              \&quot;available_services\&quot;: [                {                  \&quot;id\&quot;: 994,                  \&quot;name\&quot;: \&quot;365 дней\&quot;,                  \&quot;price_min\&quot;: 19500.0,                  \&quot;price_max\&quot;: 19500.0,                  \&quot;expiration_period\&quot;: 739167,                  \&quot;expiration_type\&quot;: \&quot;PAYMENT\&quot;                },                {                  \&quot;id\&quot;: 468,                  \&quot;name\&quot;: \&quot;1 сутки ЕДИНЫЙ ТК\&quot;,                  \&quot;price_min\&quot;: 285.0,                  \&quot;price_max\&quot;: 285.0,                  \&quot;expiration_period\&quot;: 738802,                  \&quot;expiration_type\&quot;: \&quot;PAYMENT\&quot;                }              ]            }          ]        }               Чтение носителя считается успешным, если в ответе от сервера была получена информация о билетах на носителе и  *state&#x3D;0*.               После получения ответа можно либо записать незаписанное отложенное, вызвав write-unwritten, либо  купить и записать новый билет при помощи метода pay-write.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CloudSamApi()
val readRequest : ReadRequest =  // ReadRequest | Запрос.
try {
    val result : ReadResponse = apiInstance.cloudSamRead(readRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CloudSamApi#cloudSamRead")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CloudSamApi#cloudSamRead")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **readRequest** | [**ReadRequest**](ReadRequest.md)| Запрос. | |

### Return type

[**ReadResponse**](ReadResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="cloudSamWalletPass"></a>
# **cloudSamWalletPass**
> WalletPassResponse cloudSamWalletPass(walletPassRequest)

Списание средств с ЭК носителя.

Данный метод вызывается несколько раз до получения данных об успешном выполнении списания или сообщения об ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать данный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.              В первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное из ответа на предыдущий запрос, и ответ носителя.              Пример.              1. Запрос на списание 100 рублей с носителя 04145A9AC25B80:                     {            \&quot;clientSessionId\&quot;: \&quot;7F956ACE60F711EEB21DFB803D876A30\&quot;,            \&quot;state\&quot;: 0            \&quot;uid\&quot;: \&quot;BBRamsJbgA&#x3D;&#x3D;\&quot;,            \&quot;amount\&quot;: 100.0        }                 Ответ на запрос будет содержать новое значение *state* и APDU-команду, которую необходимо отправить на носитель:                     {            \&quot;state\&quot;: 1,            \&quot;cardCommand\&quot;: \&quot;cBFAAA&#x3D;&#x3D;\&quot;        }              2. Если в ответе присутствует команда для носителя *cardCommand*, то результат её выполнения необходимо    отправить следующим запросом, указав в запросе значение *state* равное полученному значению из ответа с    командой:                     {            \&quot;clientSessionId\&quot;: \&quot;7F956ACE60F711EEB21DFB803D876A30\&quot;,            \&quot;uid\&quot;: \&quot;BBRamsJbgA&#x3D;&#x3D;\&quot;,            \&quot;amount\&quot;: 100.0,            \&quot;state\&quot;: 1,            \&quot;cardResponse\&quot;: \&quot;kD3WFH/XzaKkpMcvs1RWlCg&#x3D;\&quot;        }                 Ответ на запрос будет содержать следующую команду и новое значение параметра *state*:                     {            \&quot;state\&quot;: 2,            \&quot;cardCommand\&quot;: \&quot;ctV873dVlOk6pioDN50l7bdfVuxPjx/kfhpKoECt/C+b\&quot;        }              3. После успешного списания на очередной запрос от сервера будет получен ответ без команды и *state&#x3D;0*:                     {            \&quot;state\&quot;: 0,            \&quot;balanceOnCard\&quot;: 900.0        }               Списание считается успешным, если в ответе от сервера была получена информация об остатке на носителе и  *state&#x3D;0*.               В случае отсутствия средств для списания сервер вернёт ошибку, после получения очередного запроса от Агента:                  {         \&quot;title\&quot;: \&quot;Сумма ресурса на ЭК недостаточна для списания в счет использования услуги\&quot;,         \&quot;status\&quot;: 400,         \&quot;error\&quot;: {             \&quot;code\&quot;: \&quot;INSUFFICIENT_FUNDS\&quot;,             \&quot;comment\&quot;: \&quot;Сумма ресурса на ЭК недостаточна для списания в счет использования услуги\&quot;         },         \&quot;traceId\&quot;: \&quot;00-69f09c855e5834577ad7f49b23846736-7c5a56beab7900e3-00\&quot;     }

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CloudSamApi()
val walletPassRequest : WalletPassRequest =  // WalletPassRequest | Запрос на списание.
try {
    val result : WalletPassResponse = apiInstance.cloudSamWalletPass(walletPassRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CloudSamApi#cloudSamWalletPass")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CloudSamApi#cloudSamWalletPass")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **walletPassRequest** | [**WalletPassRequest**](WalletPassRequest.md)| Запрос на списание. | |

### Return type

[**WalletPassResponse**](WalletPassResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="cloudSamWriteUnwritten"></a>
# **cloudSamWriteUnwritten**
> WriteUnwrittenResponse cloudSamWriteUnwritten(writeUnwrittenRequest)

Запись отложенного пополнения на носитель.

Записывает отложенное пополнение на носитель.              Этот запрос вызывается после получения успешного ответа на запрос read. Хотя бы у одного билета в ответе должен быть статус *UNWRITTEN*. В качестве значения параметра *clientSessionId* передаётся то же значение, что было использовано в запросе read.              Данный метод вызывается несколько раз до получения информации о записанном билете на носителе или сообщения об ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать данный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.              В первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное из ответа на предыдущий запрос, и ответ носителя.              Пример.              Информация о незаписанном билете из ответа на операцию read:                  {       \&quot;lifetime_end\&quot;: \&quot;2028-10-01\&quot;,       \&quot;code\&quot;: \&quot;WALLET\&quot;,       \&quot;name\&quot;: \&quot;Электронный кошелек\&quot;,       \&quot;number\&quot;: \&quot;0040149455\&quot;,       \&quot;status\&quot;: {         \&quot;code\&quot;: \&quot;UNWRITTEN\&quot;       },       \&quot;current_services\&quot;: [         {           \&quot;active\&quot;: true,           \&quot;id\&quot;: 433,           \&quot;name\&quot;: \&quot;КОШЕЛЕК\&quot;,           \&quot;params\&quot;: [             {               \&quot;id\&quot;: \&quot;sum\&quot;,               \&quot;value\&quot;: \&quot;1360.00\&quot;             },             {               \&quot;id\&quot;: \&quot;valid_interval\&quot;,               \&quot;value\&quot;: \&quot;01.10.2028\&quot;             }           ]         }       ],       \&quot;unwritten_services\&quot;: [         {           \&quot;id\&quot;: 433,           \&quot;name\&quot;: \&quot;КОШЕЛЕК\&quot;,           \&quot;session\&quot;: \&quot;28531b4c-c0c7-4575-9e94-8818b3293251\&quot;,           \&quot;pay_time\&quot;: \&quot;2023-10-03T11:20:37+03:00\&quot;,           \&quot;sum\&quot;: 120.0,           \&quot;params\&quot;: [             {               \&quot;id\&quot;: \&quot;valid_interval\&quot;,               \&quot;value\&quot;: \&quot;01.10.2028\&quot;             },             {               \&quot;id\&quot;: \&quot;sum\&quot;,               \&quot;value\&quot;: \&quot;120.00\&quot;             }           ]         }       ],       \&quot;available_services\&quot;: []     }              Ответа на последний запрос записи отложенного пополнения ЭК на 120 рублей:                  {         \&quot;state\&quot;: 0,         \&quot;services\&quot;: [           {             \&quot;id\&quot;: 433,             \&quot;name\&quot;: \&quot;КОШЕЛЕК\&quot;,             \&quot;session\&quot;: \&quot;76071d3c-2779-4cca-9ef9-bf5572158e5a\&quot;,             \&quot;pay_time\&quot;: \&quot;2023-10-03T11:02:53+03:00\&quot;,             \&quot;sum\&quot;: 120.0,             \&quot;params\&quot;: [               {                 \&quot;id\&quot;: \&quot;sum\&quot;,                 \&quot;value\&quot;: \&quot;120.00\&quot;               },               {                 \&quot;id\&quot;: \&quot;valid_interval\&quot;,                 \&quot;value\&quot;: \&quot;06.11.2024\&quot;               }             ]           }         ]     }

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = CloudSamApi()
val writeUnwrittenRequest : WriteUnwrittenRequest =  // WriteUnwrittenRequest | Запрос.
try {
    val result : WriteUnwrittenResponse = apiInstance.cloudSamWriteUnwritten(writeUnwrittenRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling CloudSamApi#cloudSamWriteUnwritten")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling CloudSamApi#cloudSamWriteUnwritten")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **writeUnwrittenRequest** | [**WriteUnwrittenRequest**](WriteUnwrittenRequest.md)| Запрос. | |

### Return type

[**WriteUnwrittenResponse**](WriteUnwrittenResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

