
# AgentProcessPayRequest

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **serviceId** | **kotlin.Long** | Код продукта (GoodCode). |  |
| **sum** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Сумма. |  |
| **agentParams** | [**kotlin.collections.List&lt;AgentTicketPaymentParamDto&gt;**](AgentTicketPaymentParamDto.md) | Список параметров агента. |  [optional] |



