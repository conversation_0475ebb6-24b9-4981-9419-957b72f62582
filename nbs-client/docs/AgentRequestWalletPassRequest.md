
# AgentRequestWalletPassRequest

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **uid** | **kotlin.String** | UID Носителя в шестнадцатеричном представлении. |  |
| **bitmap** | [**kotlin.collections.List&lt;AgentReadBlock&gt;**](AgentReadBlock.md) | Считанные блоки. |  |
| **amount** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Сумма списания. |  |



