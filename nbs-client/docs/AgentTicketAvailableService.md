
# AgentTicketAvailableService

## Properties
| Name | Type | Description | Notes |
| ------------ | ------------- | ------------- | ------------- |
| **id** | **kotlin.Int** | Идентификатор. |  [optional] |
| **name** | **kotlin.String** | Название. |  [optional] |
| **priceMin** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Стоимость услуги, нижняя граница. |  [optional] |
| **priceMax** | [**java.math.BigDecimal**](java.math.BigDecimal.md) | Стоимость услуги, верхняя граница. |  [optional] |
| **expirationPeriod** | **kotlin.Int** | Срок действия в днях. |  [optional] |
| **expirationType** | **kotlin.String** | Тип срока действия. |  [optional] |
| **lifetimeExceeded** | **kotlin.String** | Признак, что срок действия услуги выходит за срок действия карты. |  [optional] |
| **remainedDays** | **kotlin.Int** | Кол-во оставшихся (действующих) дней |  [optional] |



