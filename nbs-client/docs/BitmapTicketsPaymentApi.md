# BitmapTicketsPaymentApi

All URIs are relative to *https://api-partners.mosmetro.ru/tks.ticketspayment*

| Method | HTTP request | Description |
| ------------- | ------------- | ------------- |
| [**bitmapTicketsPaymentCancelPaywrite**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentCancelPaywrite) | **POST** /v1/cancel_paywrite | Отмена записи Прямого пополненения. |
| [**bitmapTicketsPaymentCancelUnwritten**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentCancelUnwritten) | **POST** /v1/cancel_unwritten | Отмена записи Удалённого пополненения. |
| [**bitmapTicketsPaymentGetKeys**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentGetKeys) | **POST** /v1/get_keys | Запрос параметров чтения блоков Носителя. |
| [**bitmapTicketsPaymentNotifyWriteResult**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentNotifyWriteResult) | **POST** /v1/notify_write_result | Уведомление о результате записи на Носитель. |
| [**bitmapTicketsPaymentProcessPaywrite**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentProcessPaywrite) | **POST** /v1/process_paywrite | Подтверждение прямого пополнение. |
| [**bitmapTicketsPaymentProcessUnwritten**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentProcessUnwritten) | **POST** /v1/process_unwritten | Подтверждение записи удалённого пополнения. |
| [**bitmapTicketsPaymentRequestPaywrite**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentRequestPaywrite) | **POST** /v1/request_paywrite | Запрос на прямое пополнение. |
| [**bitmapTicketsPaymentRequestUnwritten**](BitmapTicketsPaymentApi.md#bitmapTicketsPaymentRequestUnwritten) | **POST** /v1/request_unwritten | Запрос на запись удалённого пополнения. |


<a id="bitmapTicketsPaymentCancelPaywrite"></a>
# **bitmapTicketsPaymentCancelPaywrite**
> bitmapTicketsPaymentCancelPaywrite(session)

Отмена записи Прямого пополненения.

Отмена транзакции записи прямого пополнения ЭБ, завершает эту транзакцию. Агент сообщает ЦОТТ, что не было попыток записи.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
try {
    apiInstance.bitmapTicketsPaymentCancelPaywrite(session)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentCancelPaywrite")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentCancelPaywrite")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **session** | **kotlin.String**| Сессия. | |

### Return type

null (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="bitmapTicketsPaymentCancelUnwritten"></a>
# **bitmapTicketsPaymentCancelUnwritten**
> bitmapTicketsPaymentCancelUnwritten(session)

Отмена записи Удалённого пополненения.

Отмена транзакции записи отложенного ЭБ, завершает эту транзакцию. Агент сообщает ЦОТТ, что не было попыток записи.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_unwritten&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
try {
    apiInstance.bitmapTicketsPaymentCancelUnwritten(session)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentCancelUnwritten")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentCancelUnwritten")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **session** | **kotlin.String**| Сессия. | |

### Return type

null (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="bitmapTicketsPaymentGetKeys"></a>
# **bitmapTicketsPaymentGetKeys**
> GetKeysResponse bitmapTicketsPaymentGetKeys(uid)

Запрос параметров чтения блоков Носителя.

ЦОТТ сообщает Агенту, какие блоки и какими ключами из SAM следует считать с Носителя, чтобы работать с ЭБ.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val uid : kotlin.String = uid_example // kotlin.String | UID Носителя в шестнадцатеричном представлении.
try {
    val result : GetKeysResponse = apiInstance.bitmapTicketsPaymentGetKeys(uid)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentGetKeys")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentGetKeys")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **uid** | **kotlin.String**| UID Носителя в шестнадцатеричном представлении. | |

### Return type

[**GetKeysResponse**](GetKeysResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="bitmapTicketsPaymentNotifyWriteResult"></a>
# **bitmapTicketsPaymentNotifyWriteResult**
> NotifyWriteResultResponse bitmapTicketsPaymentNotifyWriteResult(session, notifyWriteResultRequest)

Уведомление о результате записи на Носитель.

Агент сообщает ЦОТТ о результате записи на Носитель.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_paywrite&#x60; или &#x60;process_unwritten&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
val notifyWriteResultRequest : NotifyWriteResultRequest =  // NotifyWriteResultRequest | Запрос.
try {
    val result : NotifyWriteResultResponse = apiInstance.bitmapTicketsPaymentNotifyWriteResult(session, notifyWriteResultRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentNotifyWriteResult")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentNotifyWriteResult")
    e.printStackTrace()
}
```

### Parameters
| **session** | **kotlin.String**| Сессия. | |
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **notifyWriteResultRequest** | [**NotifyWriteResultRequest**](NotifyWriteResultRequest.md)| Запрос. | |

### Return type

[**NotifyWriteResultResponse**](NotifyWriteResultResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="bitmapTicketsPaymentProcessPaywrite"></a>
# **bitmapTicketsPaymentProcessPaywrite**
> ProcessPaywriteResponse bitmapTicketsPaymentProcessPaywrite(session, processPaywriteRequest)

Подтверждение прямого пополнение.

Фиксируется оплачиваемая услуга и выдаются блоки, которые следует записать на Носитель. Завершает транзакцию продажи с записью на Носитель.              Этот запрос можно отправлять только после успешного ответа на запрос &#x60;/request_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.              Атрибут &#x60;status_code&#x60; для соответствующего билета должен быть равен &#x60;OK&#x60;. Если &#x60;status_code&#x60; для соответствующего билета равен &#x60;UNWRITTEN&#x60;, то следует вызывать &#x60;/request_unwritten&#x60;, чтобы выполнить сценарий \&quot;Запись Удаленного пополнения\&quot;.              Для следующих параметров, если агент их указывает в &#x60;agent_params&#x60;, следует использовать предопределенные имена: * &#x60;pay_type&#x60; - способ оплаты:   * **CASH** - наличные;   * **CARD** - банковская карта;   * **PHONE** - мобильный телефон;   * **INTERNET** - электронная платежная система; * &#x60;pos&#x60; - номер точки продаж              Рекомендуется также заполнить: * &#x60;phone&#x60; - номер телефона плательщика, если он известен. 11 цифр без разделителей.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
val processPaywriteRequest : ProcessPaywriteRequest =  // ProcessPaywriteRequest | Запрос.
try {
    val result : ProcessPaywriteResponse = apiInstance.bitmapTicketsPaymentProcessPaywrite(session, processPaywriteRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentProcessPaywrite")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentProcessPaywrite")
    e.printStackTrace()
}
```

### Parameters
| **session** | **kotlin.String**| Сессия. | |
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **processPaywriteRequest** | [**ProcessPaywriteRequest**](ProcessPaywriteRequest.md)| Запрос. | |

### Return type

[**ProcessPaywriteResponse**](ProcessPaywriteResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="bitmapTicketsPaymentProcessUnwritten"></a>
# **bitmapTicketsPaymentProcessUnwritten**
> ProcessUnwrittenResponse bitmapTicketsPaymentProcessUnwritten(session)

Подтверждение записи удалённого пополнения.

Выдаются блоки отложенного ЭБ, которые следует записать на Носитель.              Завершает транзакцию записи отложенного ЭБ.              Этот запрос можно отправлять только после успешного ответа на запрос &#x60;request_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.              При отсутствии билетов, которые можно записать в данный момент, возвращается ошибка &#x60;NO_TICKETS_TO_WRITE&#x60; либо &#x60;NO_ROOM_TO_WRITE_TICKET&#x60;. Эту ошибку ЦОТТ может вернуть, даже если есть ожидающие записи отложенные (например, если текущий остаток на носителе не позволяет записать ожидающий записи отложенный).

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val session : kotlin.String = session_example // kotlin.String | Сессия.
try {
    val result : ProcessUnwrittenResponse = apiInstance.bitmapTicketsPaymentProcessUnwritten(session)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentProcessUnwritten")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentProcessUnwritten")
    e.printStackTrace()
}
```

### Parameters
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **session** | **kotlin.String**| Сессия. | |

### Return type

[**ProcessUnwrittenResponse**](ProcessUnwrittenResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json

<a id="bitmapTicketsPaymentRequestPaywrite"></a>
# **bitmapTicketsPaymentRequestPaywrite**
> RequestPaywriteResponse bitmapTicketsPaymentRequestPaywrite(uid, requestPaywriteRequest)

Запрос на прямое пополнение.

Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.              При наличии Носителя в реестре замененных возвращается ошибка, в поле &#x60;replaced_num&#x60; возвращается печатный номер Носителя, на который был заменен Носитель из запроса.              При успешном выполнении этого запроса, начинается транзакция продажи с записью. В ответе возвращается номер транзакции (&#x60;session&#x60;). Его следует указывать в последующих сообщениях сценария.              Если запрос &#x60;request_paywrite&#x60; сообщает о незаписанных отложенных ЭБ, следует, начав сценарий с начала, выполнить запись отложенных (&#x60;get_keys&#x60;, &#x60;request_unwritten&#x60;, ..), а потом заново начать сценарий продажи с записью.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val uid : kotlin.String = uid_example // kotlin.String | UID Носителя в шестнадцатеричном представлении.
val requestPaywriteRequest : RequestPaywriteRequest =  // RequestPaywriteRequest | Запрос.
try {
    val result : RequestPaywriteResponse = apiInstance.bitmapTicketsPaymentRequestPaywrite(uid, requestPaywriteRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentRequestPaywrite")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentRequestPaywrite")
    e.printStackTrace()
}
```

### Parameters
| **uid** | **kotlin.String**| UID Носителя в шестнадцатеричном представлении. | |
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **requestPaywriteRequest** | [**RequestPaywriteRequest**](RequestPaywriteRequest.md)| Запрос. | |

### Return type

[**RequestPaywriteResponse**](RequestPaywriteResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

<a id="bitmapTicketsPaymentRequestUnwritten"></a>
# **bitmapTicketsPaymentRequestUnwritten**
> RequestUnwrittenResponse bitmapTicketsPaymentRequestUnwritten(uid, requestUnwrittenRequest)

Запрос на запись удалённого пополнения.

Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.              При наличии Носителя в реестре замененных возвращается ошибка, в поле &#x60;replaced_num&#x60; возвращается печатный номер Носителя, на который был заменен Носитель из запроса.              При успешном выполнении этого запроса, начинается транзакция записи отложенного ЭБ. В ответе возвращается номер транзакции (&#x60;session&#x60;). Его следует указывать в последующих сообщениях сценария.

### Example
```kotlin
// Import classes:
//import org.openapitools.client.infrastructure.*
//import ru.metrosoft.sputnikgate.output.nbs.model.*

val apiInstance = BitmapTicketsPaymentApi()
val uid : kotlin.String = uid_example // kotlin.String | UID Носителя в шестнадцатеричном представлении.
val requestUnwrittenRequest : RequestUnwrittenRequest =  // RequestUnwrittenRequest | Запрос.
try {
    val result : RequestUnwrittenResponse = apiInstance.bitmapTicketsPaymentRequestUnwritten(uid, requestUnwrittenRequest)
    println(result)
} catch (e: ClientException) {
    println("4xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentRequestUnwritten")
    e.printStackTrace()
} catch (e: ServerException) {
    println("5xx response calling BitmapTicketsPaymentApi#bitmapTicketsPaymentRequestUnwritten")
    e.printStackTrace()
}
```

### Parameters
| **uid** | **kotlin.String**| UID Носителя в шестнадцатеричном представлении. | |
| Name | Type | Description  | Notes |
| ------------- | ------------- | ------------- | ------------- |
| **requestUnwrittenRequest** | [**RequestUnwrittenRequest**](RequestUnwrittenRequest.md)| Запрос. | |

### Return type

[**RequestUnwrittenResponse**](RequestUnwrittenResponse.md)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json

