plugins {
    java
    kotlin("jvm")
    id("maven-publish")
    id("com.diffplug.spotless") version "7.0.3"
}

group = "ru.metrosoft"
version = "1.0.0"

tasks.test {
    useJUnitPlatform()
}

dependencies {
    implementation(libs.kotlin.stdlib.jdk8)
    implementation(libs.kotlin.reflect)
    implementation(libs.moshi.kotlin)
    implementation(libs.moshi.adapters)
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging.interceptor)

    implementation(libs.simple.pem.keystore)

 //   testImplementation(libs.kotlin.test)

    testImplementation("io.kotlintest:kotlintest-runner-junit5:3.4.2")
}
