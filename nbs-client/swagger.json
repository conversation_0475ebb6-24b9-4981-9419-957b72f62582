{"x-generator": "NSwag v14.0.7.0 (NJsonSchema v11.0.0.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "API продажи билетов контаргентами", "version": "1.0.0"}, "servers": [{"url": "https://api-partners.mosmetro.ru/tks.ticketspayment"}], "paths": {"/v1/get_keys": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Запрос параметров чтения блоков Носителя.", "description": "ЦОТТ сообщает Агенту, какие блоки и какими ключами из SAM следует считать с Носителя, чтобы работать с ЭБ.", "operationId": "BitmapTicketsPayment_GetKeys", "parameters": [{"name": "uid", "in": "query", "required": true, "description": "UID Носителя в шестнадцатеричном представлении.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetKeysResponse"}}}}}}}, "/v1/request_paywrite": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Запрос на прямое пополнение.", "description": "Агент сообщает ЦОТТ считанный с карты битмап.\nЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи\nили просрочены.\n            \nПри наличии Носителя в реестре замененных возвращается ошибка, в поле `replaced_num` возвращается печатный\nномер Носителя, на который был заменен Носитель из запроса.\n            \nПри успешном выполнении этого запроса, начинается транзакция продажи с записью.\nВ ответе возвращается номер транзакции (`session`).\nЕго следует указывать в последующих сообщениях сценария.\n            \nЕсли запрос `request_paywrite` сообщает о незаписанных\nотложенных ЭБ, следует, начав сценарий с начала, выполнить запись отложенных (`get_keys`, `request_unwritten`, ..),\nа потом заново начать сценарий продажи с записью.", "operationId": "BitmapTicketsPayment_RequestPaywrite", "parameters": [{"name": "uid", "in": "query", "required": true, "description": "UID Носителя в шестнадцатеричном представлении.", "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestPaywriteRequest"}}}, "required": true, "x-position": 2}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestPaywriteResponse"}}}}}}}, "/v1/request_unwritten": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Запрос на запись удалённого пополнения.", "description": "Агент сообщает ЦОТТ считанный с карты битмап.\nЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи\nили просрочены.\n            \nПри наличии Носителя в реестре замененных возвращается ошибка, в поле `replaced_num` возвращается печатный\nномер Носителя, на который был заменен Носитель из запроса.\n            \nПри успешном выполнении этого запроса, начинается транзакция записи отложенного ЭБ.\nВ ответе возвращается номер транзакции (`session`).\nЕго следует указывать в последующих сообщениях сценария.", "operationId": "BitmapTicketsPayment_RequestUnwritten", "parameters": [{"name": "uid", "in": "query", "required": true, "description": "UID Носителя в шестнадцатеричном представлении.", "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestUnwrittenRequest"}}}, "required": true, "x-position": 2}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestUnwrittenResponse"}}}}}}}, "/v1/process_paywrite": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Подтверждение прямого пополнение.", "description": "Фиксируется оплачиваемая услуга и выдаются блоки, которые следует записать на Носитель.\nЗавершает транзакцию продажи с записью на Носитель.\n            \nЭтот запрос можно отправлять только после успешного ответа на запрос `/request_paywrite`.\nЗначение `session` должно совпадать со значением `session` из ответа на него.\n            \nАтрибут `status_code` для соответствующего билета должен быть равен `OK`.\nЕсли `status_code` для соответствующего билета равен `UNWRITTEN`, то следует вызывать\n`/request_unwritten`, чтобы выполнить сценарий \"Запись Удаленного пополнения\".\n            \nДля следующих параметров, если агент их указывает в `agent_params`, следует использовать предопределенные имена:\n* `pay_type` - способ оплаты:\n  * **CASH** - наличные;\n  * **CARD** - банковская карта;\n  * **PHONE** - мобильный телефон;\n  * **INTERNET** - электронная платежная система;\n* `pos` - номер точки продаж\n            \nРекомендуется также заполнить:\n* `phone` - номер телефона плательщика, если он известен. 11 цифр без разделителей.", "operationId": "BitmapTicketsPayment_ProcessPaywrite", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessPaywriteRequest"}}}, "required": true, "x-position": 2}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessPaywriteResponse"}}}}}}}, "/v1/process_unwritten": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Подтверждение записи удалённого пополнения.", "description": "Выдаются блоки отложенного ЭБ, которые следует записать на Носитель.\n            \nЗавершает транзакцию записи отложенного ЭБ.\n            \nЭтот запрос можно отправлять только после успешного ответа на запрос `request_paywrite`.\nЗначение `session` должно совпадать со значением `session` из ответа на него.\n            \nПри отсутствии билетов, которые можно записать в данный момент, возвращается ошибка `NO_TICKETS_TO_WRITE` либо\n`NO_ROOM_TO_WRITE_TICKET`.\nЭту ошибку ЦОТТ может вернуть, даже если есть ожидающие записи отложенные\n(например, если текущий остаток на носителе не позволяет записать ожидающий записи отложенный).", "operationId": "BitmapTicketsPayment_ProcessUnwritten", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessUnwrittenResponse"}}}}}}}, "/v1/notify_write_result": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Уведомление о результате записи на Носитель.", "description": "Агент сообщает ЦОТТ о результате записи на Носитель.\n            \nЗапрос должен отправляться только в случае успешного ответа сервера на `process_paywrite`\nили `process_unwritten`.\nЗначение `session` должно совпадать со значением `session` из ответа на него.", "operationId": "BitmapTicketsPayment_NotifyWriteResult", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyWriteResultRequest"}}}, "required": true, "x-position": 2}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotifyWriteResultResponse"}}}}}}}, "/v1/cancel_unwritten": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Отмена записи Удалённого пополненения.", "description": "Отмена транзакции записи отложенного ЭБ, завершает эту транзакцию.\nАгент сообщает ЦОТТ, что не было попыток записи.\n            \nЗапрос должен отправляться только в случае успешного ответа сервера на `process_unwritten`.\nЗначение `session` должно совпадать со значением `session` из ответа на него.", "operationId": "BitmapTicketsPayment_CancelUnwritten", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": ""}}}}, "/v1/cancel_paywrite": {"post": {"tags": ["BitmapTicketsPayment"], "summary": "Отмена записи Прямого пополненения.", "description": "Отмена транзакции записи прямого пополнения ЭБ, завершает эту транзакцию.\nАгент сообщает ЦОТТ, что не было попыток записи.\n            \nЗапрос должен отправляться только в случае успешного ответа сервера на `process_paywrite`.\nЗначение `session` должно совпадать со значением `session` из ответа на него.", "operationId": "BitmapTicketsPayment_CancelPaywrite", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": ""}}}}, "/v1/request_wallet_pass": {"post": {"tags": ["CityService"], "summary": "Запрос на спиcание ЭК.", "operationId": "CityService_RequestWalletPass", "requestBody": {"x-name": "request", "description": "Данные запроса.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRequestWalletPassRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRequestWalletPassResponse"}}}}}}}, "/v1/process_pass": {"post": {"tags": ["CityService"], "summary": "Подтверждение списания.", "operationId": "CityService_ProcessPass", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": ""}}}}, "/v1/cancel_pass": {"post": {"tags": ["CityService"], "summary": "Отмена списания.", "operationId": "CityService_CancelPass", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "204": {"description": ""}}}}, "/v1/cloud/wallet-pass": {"post": {"tags": ["CloudSam"], "summary": "Списание средств с ЭК носителя.", "description": "Данный метод вызывается несколько раз до получения данных об успешном выполнении списания или сообщения\nоб ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать\nданный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.\n            \nВ первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное\nиз ответа на предыдущий запрос, и ответ носителя.\n            \nПример.\n            \n1. Запрос на списание 100 рублей с носителя 04145A9AC25B80:\n            \n       {\n           \"clientSessionId\": \"7F956ACE60F711EEB21DFB803D876A30\",\n           \"state\": 0\n           \"uid\": \"BBRamsJbgA==\",\n           \"amount\": 100.0\n       }\n            \n   Ответ на запрос будет содержать новое значение *state* и APDU-команду, которую необходимо отправить на носитель:\n            \n       {\n           \"state\": 1,\n           \"cardCommand\": \"cBFAAA==\"\n       }\n            \n2. Если в ответе присутствует команда для носителя *cardCommand*, то результат её выполнения необходимо\n   отправить следующим запросом, указав в запросе значение *state* равное полученному значению из ответа с\n   командой:\n            \n       {\n           \"clientSessionId\": \"7F956ACE60F711EEB21DFB803D876A30\",\n           \"uid\": \"BBRamsJbgA==\",\n           \"amount\": 100.0,\n           \"state\": 1,\n           \"cardResponse\": \"kD3WFH/XzaKkpMcvs1RWlCg=\"\n       }\n            \n   Ответ на запрос будет содержать следующую команду и новое значение параметра *state*:\n            \n       {\n           \"state\": 2,\n           \"cardCommand\": \"ctV873dVlOk6pioDN50l7bdfVuxPjx/kfhpKoECt/C+b\"\n       }\n            \n3. После успешного списания на очередной запрос от сервера будет получен ответ без команды и *state=0*:\n            \n       {\n           \"state\": 0,\n           \"balanceOnCard\": 900.0\n       }\n            \n Списание считается успешным, если в ответе от сервера была получена информация об остатке на носителе и\n *state=0*.\n            \n В случае отсутствия средств для списания сервер вернёт ошибку, после получения очередного запроса от Агента:\n            \n    {\n        \"title\": \"Сумма ресурса на ЭК недостаточна для списания в счет использования услуги\",\n        \"status\": 400,\n        \"error\": {\n            \"code\": \"INSUFFICIENT_FUNDS\",\n            \"comment\": \"Сумма ресурса на ЭК недостаточна для списания в счет использования услуги\"\n        },\n        \"traceId\": \"00-69f09c855e5834577ad7f49b23846736-7c5a56beab7900e3-00\"\n    }", "operationId": "CloudSam_WalletPass", "requestBody": {"x-name": "request", "description": "Запрос на списание.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WalletPassRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WalletPassResponse"}}}}}}}, "/v1/cloud/cancel-wallet-pass": {"post": {"tags": ["CloudSam"], "summary": "Отмена списания.", "description": "Если в ответе ConsiderAsWritten содержит true, то транзакция всё равно считается\nуспешной.", "operationId": "CloudSam_CancelWalletPass", "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelWalletPassRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelWalletPassResponse"}}}}}}}, "/v1/cloud/cancel-write-unwritten": {"post": {"tags": ["CloudSam"], "summary": "Отмена записи отложенного.", "description": "Если в ответе ConsiderAsWritten содержит true, то транзакция всё равно считается\nуспешной.", "operationId": "CloudSam_CancelWriteUnwritten", "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelWriteUnwrittenRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelWriteUnwrittenResponse"}}}}}}}, "/v1/cloud/cancel-pay-write": {"post": {"tags": ["CloudSam"], "summary": "Отмена прямой записи.", "description": "Если в ответе ConsiderAsWritten содержит true, то транзакция всё равно считается\nуспешной.", "operationId": "CloudSam_CancelPayWrite", "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelPayWriteRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelPayWriteResponse"}}}}}}}, "/v1/cloud/read": {"post": {"tags": ["CloudSam"], "summary": "Запрос информации о билетах на носителе.", "description": "Запрос read является первым запросом для записи отложенного пополнения и покупки билета с записью\nна носитель.\n            \nДанный метод вызывается несколько раз до получения информации о билетах на носителе или сообщения\nоб ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать\nданный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.\n            \nВ первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное\nиз ответа на предыдущий запрос, и ответ носителя.\n            \nПример.\n            \n1. Запрос информации о носителе 04145A9AC25B80:\n            \n       {\n           \"clientSessionId\": \"7F956ACE60F711EEB21DFB803D876A30\",\n           \"state\": 0\n           \"uid\": \"BBRamsJbgA==\"\n       }\n            \n   Ответ на запрос будет содержать новое значение *state* и APDU-команду, которую необходимо отправить на носитель:\n            \n       {\n           \"state\": 1,\n           \"cardCommand\": \"cBFAAA==\"\n       }\n            \n2. Если в ответе присутствует команда для носителя *cardCommand*, то результат её выполнения необходимо\n   отправить следующим запросом, указав в запросе значение *state* равное полученному значению из ответа с\n   командой:\n            \n       {\n           \"clientSessionId\": \"7F956ACE60F711EEB21DFB803D876A30\",\n           \"uid\": \"BBRamsJbgA==\",\n           \"state\": 1,\n           \"cardResponse\": \"kD3WFH/XzaKkpMcvs1RWlCg=\"\n       }\n            \n   Ответ на запрос будет содержать следующую команду и новое значение параметра *state*:\n            \n       {\n           \"state\": 2,\n           \"cardCommand\": \"ctV873dVlOk6pioDN50l7bdfVuxPjx/kfhpKoECt/C+b\"\n       }\n            \n3. После успешного чтения всех требуемых данных на очередной запрос от сервера будет получен ответ, в котором\n   нет команды для носителя *cardCommand* и *state=0*:\n            \n       {\n         \"state\": 0,\n         \"num\": \"0040149455\",\n         \"tickets\": [\n           {\n             \"lifetime_end\": \"2028-03-06\",\n             \"code\": \"MOSGORTRANS\",\n             \"name\": \"Билет на наземный транспорт\",\n             \"number\": \"0040149455\",\n             \"status\": {\n               \"code\": \"OK\"\n             },\n             \"current_services\": [],\n             \"unwritten_services\": [],\n             \"available_services\": [\n               {\n                 \"id\": 800,\n                 \"name\": \"30 дней ТАТ\",\n                 \"price_min\": 1560.0,\n                 \"price_max\": 1560.0,\n                 \"expiration_period\": 738832,\n                 \"expiration_type\": \"PAYMENT\"\n               },\n               {\n                 \"id\": 801,\n                 \"name\": \"90 дней ТАТ\",\n                 \"price_min\": 4140.0,\n                 \"price_max\": 4140.0,\n                 \"expiration_period\": 738892,\n                 \"expiration_type\": \"PAYMENT\"\n               }\n             ]\n           },\n           {\n             \"lifetime_end\": \"2024-11-07\",\n             \"code\": \"WALLET\",\n             \"name\": \"Электронный кошелек\",\n             \"number\": \"0040149455\",\n             \"status\": {\n               \"code\": \"OK\"\n             },\n             \"current_services\": [\n               {\n                 \"active\": true,\n                 \"id\": 433,\n                 \"name\": \"КОШЕЛЕК\",\n                 \"params\": [\n                   {\n                     \"id\": \"sum\",\n                     \"value\": \"1000.00\"\n                   }\n                 ]\n               }\n             ],\n             \"unwritten_services\": [],\n             \"available_services\": [\n               {\n                 \"id\": 433,\n                 \"name\": \"КОШЕЛЕК\",\n                 \"price_min\": 1.0,\n                 \"price_max\": 9000.0,\n                 \"expiration_period\": 739195,\n                 \"expiration_type\": \"PAYMENT\"\n               }\n             ]\n           },\n           {\n             \"lifetime_end\": \"2028-03-06\",\n             \"code\": \"UNICARD\",\n             \"name\": \"Билет на метро и наземный транспорт\",\n             \"number\": \"0040149455\",\n             \"status\": {\n               \"code\": \"OK\"\n             },\n             \"current_services\": [],\n             \"unwritten_services\": [],\n             \"available_services\": [\n               {\n                 \"id\": 994,\n                 \"name\": \"365 дней\",\n                 \"price_min\": 19500.0,\n                 \"price_max\": 19500.0,\n                 \"expiration_period\": 739167,\n                 \"expiration_type\": \"PAYMENT\"\n               },\n               {\n                 \"id\": 468,\n                 \"name\": \"1 сутки ЕДИНЫЙ ТК\",\n                 \"price_min\": 285.0,\n                 \"price_max\": 285.0,\n                 \"expiration_period\": 738802,\n                 \"expiration_type\": \"PAYMENT\"\n               }\n             ]\n           }\n         ]\n       }\n            \n Чтение носителя считается успешным, если в ответе от сервера была получена информация о билетах на носителе и\n *state=0*.\n            \n После получения ответа можно либо записать незаписанное отложенное, вызвав write-unwritten, либо\n купить и записать новый билет при помощи метода pay-write.", "operationId": "CloudSam_Read", "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReadResponse"}}}}}}}, "/v1/cloud/write-unwritten": {"post": {"tags": ["CloudSam"], "summary": "Запись отложенного пополнения на носитель.", "description": "Записывает отложенное пополнение на носитель.\n            \nЭтот запрос вызывается после получения успешного ответа на запрос read. Хотя бы у одного билета\nв ответе должен быть статус *UNWRITTEN*. В качестве значения параметра *clientSessionId* передаётся то же\nзначение, что было использовано в запросе read.\n            \nДанный метод вызывается несколько раз до получения информации о записанном билете на носителе или сообщения\nоб ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать\nданный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.\n            \nВ первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное\nиз ответа на предыдущий запрос, и ответ носителя.\n            \nПример.\n            \nИнформация о незаписанном билете из ответа на операцию read:\n            \n    {\n      \"lifetime_end\": \"2028-10-01\",\n      \"code\": \"WALLET\",\n      \"name\": \"Электронный кошелек\",\n      \"number\": \"0040149455\",\n      \"status\": {\n        \"code\": \"UNWRITTEN\"\n      },\n      \"current_services\": [\n        {\n          \"active\": true,\n          \"id\": 433,\n          \"name\": \"КОШЕЛЕК\",\n          \"params\": [\n            {\n              \"id\": \"sum\",\n              \"value\": \"1360.00\"\n            },\n            {\n              \"id\": \"valid_interval\",\n              \"value\": \"01.10.2028\"\n            }\n          ]\n        }\n      ],\n      \"unwritten_services\": [\n        {\n          \"id\": 433,\n          \"name\": \"КОШЕЛЕК\",\n          \"session\": \"28531b4c-c0c7-4575-9e94-8818b3293251\",\n          \"pay_time\": \"2023-10-03T11:20:37+03:00\",\n          \"sum\": 120.0,\n          \"params\": [\n            {\n              \"id\": \"valid_interval\",\n              \"value\": \"01.10.2028\"\n            },\n            {\n              \"id\": \"sum\",\n              \"value\": \"120.00\"\n            }\n          ]\n        }\n      ],\n      \"available_services\": []\n    }\n            \nОтвета на последний запрос записи отложенного пополнения ЭК на 120 рублей:\n            \n    {\n        \"state\": 0,\n        \"services\": [\n          {\n            \"id\": 433,\n            \"name\": \"КОШЕЛЕК\",\n            \"session\": \"76071d3c-2779-4cca-9ef9-bf5572158e5a\",\n            \"pay_time\": \"2023-10-03T11:02:53+03:00\",\n            \"sum\": 120.0,\n            \"params\": [\n              {\n                \"id\": \"sum\",\n                \"value\": \"120.00\"\n              },\n              {\n                \"id\": \"valid_interval\",\n                \"value\": \"06.11.2024\"\n              }\n            ]\n          }\n        ]\n    }", "operationId": "CloudSam_WriteUnwritten", "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WriteUnwrittenRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WriteUnwrittenResponse"}}}}}}}, "/v1/cloud/pay-write": {"post": {"tags": ["CloudSam"], "summary": "Покупка и запись билета на носитель.", "description": "Фиксируется оплачиваемая услуга и записывается на носитель.\n            \nЭтот запрос вызывается после получения успешного ответа на запрос read. В качестве значения\nпараметра *clientSessionId* передаётся то же значение, что было использовано в запросе read.\n            \nДанный метод вызывается несколько раз до получения информации о записанном билете на носителе или сообщения\nоб ошибке. Количество запросов зависит от вида носителя и не должно контролироваться на стороне Агента. Вызывать\nданный метод нужно до тех пор, пока в ответе приходит APDU-команда для носителя.\n            \nВ первом запросе передаётся значение состояния 0. Все последующие запросы должны указывать значение, полученное\nиз ответа на предыдущий запрос, и ответ носителя.\n            \nПример.\n            \nЗапрос на пополнение ЭК на 120 рублей:\n            \n    {\n        \"clientSessionId\": \"1b1fb55513e446d69c4c14f2f4d3786c\",\n        \"state\": 0,\n        \"service\": {\n            \"service_id\": 433,\n            \"sum\": 120.0\n        },\n        \"agentParams\": [\n        {\n            \"id\": \"pay_type\",\n            \"value\": \"CARD\"\n        },\n        {\n            \"id\": \"pos\",\n            \"value\": \"14180\"\n        }\n        ]\n    }\n            \nОтвет на последний запрос:\n            \n    {\n        \"state\": 0,\n        \"services\": [\n        {\n            \"id\": 433,\n            \"name\": \"КОШЕЛЕК\",\n            \"session\": \"ee338399-d60a-4224-bdd3-2323aea7c0d6\",\n            \"pay_time\": \"2023-10-03T11:13:33+03:00\",\n            \"sum\": 120.0,\n            \"params\": [\n            {\n                \"id\": \"sum\",\n                \"value\": \"120.00\"\n            },\n            {\n                \"id\": \"valid_interval\",\n                \"value\": \"06.11.2024\"\n            }\n            ]\n        }\n        ]\n    }", "operationId": "CloudSam_PayWrite", "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayWriteRequest"}}}, "required": true, "x-position": 1}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PayWriteResponse"}}}}}}}, "/v1/ping": {"get": {"tags": ["ExternalInfo"], "summary": "Проверка доступности канала связи.", "operationId": "ExternalInfo_Ping", "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}}}, "/v1/get_num": {"post": {"tags": ["ExternalInfo"], "summary": "Запрос печатного номера Носителя по UID.", "operationId": "ExternalInfo_GetNum", "parameters": [{"name": "uid", "in": "query", "required": true, "description": "UID Носителя в шестнадцатеричном представлении.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetNumResponse"}}}}}}}, "/v1/get_uid": {"post": {"tags": ["ExternalInfo"], "summary": "Запрос UID Носителя по его транспортному номеру.", "operationId": "ExternalInfo_GetUid", "parameters": [{"name": "num", "in": "query", "required": true, "description": "Транспортный номер носителя.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "UID носителя.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentGetUidResponse"}}}}}}}, "/v1/get_card_by_uid": {"post": {"tags": ["ExternalInfo"], "summary": "Запрос информации о носителе по его UID.", "operationId": "ExternalInfo_GetCardByUid", "parameters": [{"name": "uid", "in": "query", "required": true, "description": "UID Носителя в шестнадцатеричном представлении.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "Информация о носителе.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCardResponse"}}}}}}}, "/v1/request_pay": {"post": {"tags": ["TicketsPayment"], "summary": "Запрос удаленного пополнения.", "operationId": "TicketsPayment_RequestPay", "parameters": [{"name": "num", "in": "query", "required": true, "description": "Транспортный номер носителя.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRequestPayResponse"}}}}}}}, "/v1/request_pay_by_uid": {"post": {"tags": ["TicketsPayment"], "summary": "Запрос удаленного пополнения по UID.", "operationId": "TicketsPayment_RequestPayByUid", "parameters": [{"name": "uid", "in": "query", "required": true, "description": "UID носителя.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentRequestPayResponse"}}}}}}}, "/v1/process_pay": {"post": {"tags": ["TicketsPayment"], "summary": "Подтверждение удаленного пополнения.", "operationId": "TicketsPayment_ProcessPay", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "requestBody": {"x-name": "request", "description": "Запрос.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentProcessPayRequest"}}}, "required": true, "x-position": 2}, "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AgentProcessPayResponse"}}}}}}}, "/v1/cancel_pay": {"post": {"tags": ["TicketsPayment"], "summary": "Отмена удаленного пополнения.", "operationId": "TicketsPayment_CancelPay", "parameters": [{"name": "session", "in": "query", "required": true, "description": "Сессия.", "schema": {"type": "string"}, "x-position": 1}], "responses": {"400": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": ""}}}}}, "components": {"schemas": {"ErrorResponse": {"type": "object", "description": "Ответ с ошибкой ЦОТТ.", "additionalProperties": false, "properties": {"error": {"description": "Объект ошибки.", "oneOf": [{"$ref": "#/components/schemas/ErrorResponseError"}]}}}, "ErrorResponseError": {"type": "object", "description": "Данные об ошибке.", "additionalProperties": false, "properties": {"code": {"type": "string", "description": "Символьный код ошибки."}, "comment": {"type": "string", "description": "Поясняющее сообщение."}, "replacedNum": {"type": "string", "description": "Печатный номер нового носителя.", "nullable": true}, "sectorErrors": {"type": "array", "description": "Список ошибок по секторам.", "nullable": true, "items": {"$ref": "#/components/schemas/SectorError"}}}}, "SectorError": {"type": "object", "description": "Информация об ошибке в секторе.", "additionalProperties": false, "properties": {"number": {"type": "integer", "description": "Номер сектора.", "format": "int32"}, "code": {"type": "integer", "description": "Код статуса сектора.", "format": "int32"}, "comment": {"type": "string", "description": "Текстовое описание ошибки.", "nullable": true}}}, "GetKeysResponse": {"type": "object", "description": "Ответ на запрос GetKeysRequest.", "additionalProperties": false, "properties": {"map": {"type": "array", "description": "Список информации о ключах для блоков.", "items": {"$ref": "#/components/schemas/TicketBlock"}}}}, "TicketBlock": {"type": "object", "description": "Блок, в котором содержится ЭБ, и ключ для его чтения.", "additionalProperties": false, "properties": {"block": {"type": "integer", "description": "Сквозной номер блока (начиная с нуля).", "format": "int32"}, "read_key_id": {"type": "integer", "description": "Идентификатор ключа чтения.", "format": "int32"}, "read_key_version": {"type": "integer", "description": "Версия ключа чтения.", "format": "int32"}, "read_key_type": {"type": "string", "description": "Тип ключа чтения, A или B. По умолчанию A."}, "read_key_value": {"type": "string", "description": "Значение ключа SL1 для чтения Блока."}}}, "RequestPaywriteResponse": {"type": "object", "description": "Ответ на запрос RequestPaywriteRequest.", "additionalProperties": false, "properties": {"session": {"type": "string", "description": "Идентификатор сессии."}, "num": {"type": "string", "description": "Печатный номер Носителя."}, "tickets": {"type": "array", "description": "Список билетов.", "items": {"$ref": "#/components/schemas/AgentTicket"}}}}, "AgentTicket": {"type": "object", "description": "Бил<PERSON>т.", "additionalProperties": false, "properties": {"lifetime_end": {"type": "string", "description": "Срок годности билета.", "nullable": true}, "code": {"type": "string", "description": "Код."}, "name": {"type": "string", "description": "Название."}, "number": {"type": "string", "description": "Номер."}, "status": {"description": "Статус билета.", "oneOf": [{"$ref": "#/components/schemas/AgentTicketStatus"}]}, "current_services": {"type": "array", "description": "Список текущих услуг транспортного приложения, записанных на Носитель.\nМожет быть пустым.", "items": {"$ref": "#/components/schemas/AgentTicketService"}}, "unwritten_services": {"type": "array", "description": "Список услуг отложенных ЭБ транспортного приложения, ожидающих записи.", "items": {"$ref": "#/components/schemas/AgentTicketService"}}, "available_services": {"type": "array", "description": "Список доступных для покупки ЭБ.", "nullable": true, "items": {"$ref": "#/components/schemas/AgentTicketAvailableService"}}, "expired_services": {"type": "array", "description": "Список просроченных услуг, срок действия которых истек и поэтому они уже никогда не будут записаны.\nПросроченные услуги находятся в этом списке настраиваемый период времени, далее убираются из него.", "nullable": true, "items": {"$ref": "#/components/schemas/AgentTicketService"}}}}, "AgentTicketStatus": {"type": "object", "description": "Статус билета.", "additionalProperties": false, "properties": {"code": {"type": "string", "description": "Код статуса."}, "comment": {"type": "string", "description": "Описание."}}}, "AgentTicketService": {"type": "object", "description": "Сервис билета.", "additionalProperties": false, "properties": {"active": {"type": "boolean", "description": "Признак действующего билета.", "nullable": true}, "id": {"type": "integer", "description": "Идентификатор услуги.", "format": "int32"}, "name": {"type": "string", "description": "Наименование услуги (краткое)."}, "session": {"type": "string", "description": "Идентификатор сессии (транзакции), в рамках которой была приобретена эта услуга.", "nullable": true}, "pay_time": {"type": "string", "description": "Дата и время оплаты.", "format": "date-time", "nullable": true}, "sum": {"type": "number", "description": "Сумма.", "format": "decimal", "nullable": true}, "params": {"type": "array", "description": "Параметры.", "items": {"$ref": "#/components/schemas/AgentTicketPaymentParam"}}, "prolongation": {"type": "boolean", "description": "Признак продлённого билета.", "nullable": true}}}, "AgentTicketPaymentParam": {"type": "object", "description": "Параметр.", "additionalProperties": false, "properties": {"id": {"type": "string", "description": "Идентификатор."}, "name": {"type": "string", "description": "Название.", "nullable": true}, "value": {"type": "string", "description": "Значение.", "nullable": true}}}, "AgentTicketAvailableService": {"type": "object", "description": "Доступный к продаже сервис билета.", "additionalProperties": false, "properties": {"id": {"type": "integer", "description": "Идентификатор.", "format": "int32"}, "name": {"type": "string", "description": "Название."}, "price_min": {"type": "number", "description": "Стоимость услуги, нижняя граница.", "format": "decimal"}, "price_max": {"type": "number", "description": "Стоимость услуги, верхняя граница.", "format": "decimal"}, "expiration_period": {"type": "integer", "description": "Срок действия в днях.", "format": "int32"}, "expiration_type": {"type": "string", "description": "Тип срока действия."}, "lifetime_exceeded": {"type": "string", "description": "Признак, что срок действия услуги выходит за срок действия карты.", "nullable": true}, "remained_days": {"type": "integer", "description": "Кол-во оставшихся (действующих) дней", "format": "int32", "nullable": true}}}, "RequestPaywriteRequest": {"type": "object", "description": "Запрос на прямое пополнение.", "additionalProperties": false, "required": ["bitmap"], "properties": {"bitmap": {"type": "array", "description": "Считанные блоки.", "items": {"$ref": "#/components/schemas/AgentReadBlock"}}}}, "AgentReadBlock": {"type": "object", "description": "Прочитанный блок.", "additionalProperties": false, "required": ["block", "value"], "properties": {"block": {"type": "integer", "description": "Сквозной номер блока (начиная с нуля).", "format": "int32"}, "value": {"type": "string", "description": "Содержимое блока в виде строки в шестнадцатеричном представлении."}}}, "RequestUnwrittenResponse": {"type": "object", "description": "Ответ на запрос RequestUnwrittenRequest.", "additionalProperties": false, "properties": {"session": {"type": "string", "description": "Идентификатор сессии."}, "num": {"type": "string", "description": "Печатный номер носителя."}, "cardSerialNumber": {"type": "integer", "description": "Серийный номер кристала носителя.", "format": "int64"}, "tickets": {"type": "array", "description": "Список билетов.", "items": {"$ref": "#/components/schemas/AgentTicket"}}}}, "RequestUnwrittenRequest": {"type": "object", "description": "Запрос на запись удалённого пополнения.", "additionalProperties": false, "required": ["bitmap"], "properties": {"bitmap": {"type": "array", "description": "Считанные блоки.", "items": {"$ref": "#/components/schemas/AgentReadBlock"}}}}, "ProcessPaywriteResponse": {"type": "object", "description": "Ответ на запрос ProcessPaywriteRequest.", "additionalProperties": false, "properties": {"session": {"type": "string", "description": "Идентификатор сессии."}, "blocks": {"type": "array", "description": "Блоки для записи.", "items": {"$ref": "#/components/schemas/AgentWriteBlock"}}, "service": {"type": "array", "description": "Описание приобретённых билетов.", "items": {"$ref": "#/components/schemas/AgentTicketService"}}}}, "AgentWriteBlock": {"type": "object", "description": "Блок для записи, и адреса в SAM ключей для его записи и контрольного чтения.", "additionalProperties": false, "required": ["block", "value", "write_key_type", "write_key_id", "write_key_version", "write_key_value"], "properties": {"read_key_id": {"type": "integer", "description": "Идентификатор ключа чтения в SAM", "format": "int32", "nullable": true}, "block": {"type": "integer", "description": "Сквозной номер блока (начиная с нуля)", "format": "int32"}, "value": {"type": "string", "description": "Содержимое блока в виде строки в шестнадцатеричном представлении"}, "read_key_version": {"type": "integer", "description": "Версия ключа чтения в SAM. По умолчанию 0", "format": "int32", "nullable": true}, "read_key_value": {"type": "string", "description": "Значение ключа для чтения.", "nullable": true}, "write_key_type": {"type": "string", "description": "Тип ключа записи, A или B. По умолчанию A"}, "read_key_type": {"type": "string", "description": "Тип ключа чтения, A или B. По умолчанию A", "nullable": true}, "write_key_id": {"type": "integer", "description": "Идентификатор ключа записи в SAM", "format": "int32"}, "write_key_version": {"type": "integer", "description": "Версия ключа записи в SAM. По умолчанию 0", "format": "int32"}, "write_key_value": {"type": "string", "description": "Значение ключа для записи."}}}, "ProcessPaywriteRequest": {"type": "object", "description": "Подтверждение прямого пополнение.", "additionalProperties": false, "required": ["service", "agent_params"], "properties": {"service": {"description": "Товар, который покупают.", "oneOf": [{"$ref": "#/components/schemas/ProcessPaywriteService"}]}, "agent_params": {"type": "array", "description": "Параметры платежа агента.", "items": {"$ref": "#/components/schemas/AgentTicketPaymentParamDto"}}}}, "ProcessPaywriteService": {"type": "object", "description": "Информация о товаре.", "additionalProperties": false, "required": ["service_id", "sum"], "properties": {"service_id": {"type": "integer", "description": "Код продукта (GoodCode).", "format": "int32"}, "sum": {"type": "number", "description": "Сумма.", "format": "decimal"}}}, "AgentTicketPaymentParamDto": {"type": "object", "description": "Запрос на подтверждение отложенного платежа.", "additionalProperties": false, "required": ["id", "value"], "properties": {"id": {"type": "string", "description": "Идентификатор."}, "name": {"type": "string", "description": "Название.", "nullable": true}, "value": {"type": "string", "description": "Значение."}}}, "ProcessUnwrittenResponse": {"type": "object", "description": "Ответ на запрос ProcessUnwrittenRequest.", "additionalProperties": false, "properties": {"session": {"type": "string", "description": "Идентификатор сессии."}, "blocks": {"type": "array", "description": "Блоки для записи.", "items": {"$ref": "#/components/schemas/AgentWriteBlock"}}, "service": {"type": "array", "description": "Описание приобретённых билетов.", "items": {"$ref": "#/components/schemas/AgentTicketService"}}}}, "NotifyWriteResultResponse": {"type": "object", "description": "Ответ на запрос NotifyWriteResultRequest.", "additionalProperties": false, "properties": {"time": {"type": "string", "description": "Время регистрации платежа.", "format": "date-time"}}}, "NotifyWriteResultRequest": {"type": "object", "description": "Уведомление о результате записи на Носитель.", "additionalProperties": false, "required": ["write_results"], "properties": {"write_results": {"type": "array", "description": "Результат записи на Носитель.", "items": {"$ref": "#/components/schemas/AgentWriteResult"}}}}, "AgentWriteResult": {"type": "object", "description": "Результат записи блока.", "additionalProperties": false, "required": ["block", "result"], "properties": {"block": {"type": "integer", "description": "Сквозной номер блока (начиная с нуля).", "format": "int32"}, "result": {"type": "string", "description": "Результат записи.\n            \nВозможные значения:\n*  OK\n*  WRITE_ERROR\n*  READ_ERROR\n*  COMPARE_ERROR\n*  CARD_RETURN_ERROR\n*  NO_WRITE\n*  OTHER"}}}, "AgentRequestWalletPassResponse": {"type": "object", "description": "Ответ на запрос AgentRequestWalletPassRequest.", "additionalProperties": false, "required": ["session", "blocks", "pass_time"], "properties": {"session": {"type": "string", "description": "Идентификатор сессии списания."}, "blocks": {"type": "array", "description": "Блоки для записи.", "items": {"$ref": "#/components/schemas/AgentWriteBlock"}}, "pass_time": {"type": "string", "description": "Дата и время списания.", "format": "date-time"}, "balance_on_card": {"type": "number", "description": "Баланс на карте.", "format": "decimal"}}}, "AgentRequestWalletPassRequest": {"type": "object", "description": "Запрос на списание с ЭК.", "additionalProperties": false, "required": ["uid", "bitmap", "amount"], "properties": {"uid": {"type": "string", "description": "UID Носителя в шестнадцатеричном представлении."}, "bitmap": {"type": "array", "description": "Считанные блоки.", "items": {"$ref": "#/components/schemas/AgentReadBlock"}}, "amount": {"type": "number", "description": "Сумма списания.", "format": "decimal"}}}, "WalletPassResponse": {"type": "object", "description": "Ответ на запрос WalletPassRequest.", "additionalProperties": false, "properties": {"state": {"type": "integer", "description": "Текущее состояние сессии.", "format": "int32"}, "cardCommand": {"type": "string", "description": "Команда для отправки на носитель.", "format": "byte", "nullable": true}, "balanceOnCard": {"type": "number", "description": "Баланс на носителе после списания.", "format": "decimal", "nullable": true}}}, "WalletPassRequest": {"type": "object", "description": " ", "additionalProperties": false, "properties": {"clientSessionId": {"type": "string", "description": "Идентификатор сессии. 16 байт в шестнадцатиричном формате."}, "uid": {"type": "string", "description": "UID Носителя в шестнадцатеричном представлении.", "format": "byte"}, "amount": {"type": "number", "description": "Сумма списания.", "format": "decimal"}, "state": {"type": "integer", "description": "Номер состояния запроса", "format": "int32"}, "cardResponse": {"type": "string", "description": "Ответ носителя на команду, отправленную ранее.", "format": "byte", "nullable": true}}}, "CancelWalletPassResponse": {"type": "object", "description": "Ответ на запрос CancelWalletPassRequest.", "additionalProperties": false, "properties": {"considerAsWritten": {"type": "boolean", "description": "Признак успешно выполненной записи, не смотря на отмену операции."}}}, "CancelWalletPassRequest": {"type": "object", "description": "Запрос на отмену списания.", "additionalProperties": false, "properties": {"clientSessionId": {"type": "string", "description": "Идентификатор сессии. 16 байт в шестнадцатиричном формате."}}}, "CancelWriteUnwrittenResponse": {"type": "object", "description": "Ответ на запрос CancelWriteUnwrittenRequest.", "additionalProperties": false, "properties": {"considerAsWritten": {"type": "boolean", "description": "Признак успешно выполненной записи, не смотря на отмену операции."}}}, "CancelWriteUnwrittenRequest": {"type": "object", "description": "Запрос на отмену списания.", "additionalProperties": false, "properties": {"clientSessionId": {"type": "string", "description": "Идентификатор сессии. 16 байт в шестнадцатиричном формате."}}}, "CancelPayWriteResponse": {"type": "object", "description": "Ответ на запрос CancelPayWriteRequest.", "additionalProperties": false, "properties": {"considerAsWritten": {"type": "boolean", "description": "Признак успешно выполненной записи, не смотря на отмену операции."}}}, "CancelPayWriteRequest": {"type": "object", "description": "Запрос на отмену списания.", "additionalProperties": false, "properties": {"clientSessionId": {"type": "string", "description": "Идентификатор сессии. 16 байт в шестнадцатиричном формате."}}}, "ReadResponse": {"type": "object", "description": "Ответ на запрос ReadRequest.", "additionalProperties": false, "properties": {"state": {"type": "integer", "description": "Текущее состояние сессии.", "format": "int32"}, "cardCommand": {"type": "string", "description": "Команда для отправки на носитель.", "format": "byte", "nullable": true}, "num": {"type": "string", "description": "Печатный номер Носителя.", "nullable": true}, "tickets": {"type": "array", "description": "Список билетов.", "nullable": true, "items": {"$ref": "#/components/schemas/AgentTicket"}}}}, "ReadRequest": {"type": "object", "description": "Запрос информации о билетах на носителе.", "additionalProperties": false, "properties": {"clientSessionId": {"type": "string", "description": "Идентификатор сессии. 16 байт в шестнадцатиричном формате."}, "uid": {"type": "string", "description": "UID Носителя в шестнадцатеричном представлении.", "format": "byte"}, "state": {"type": "integer", "description": "Номер состояния запроса", "format": "int32"}, "cardResponse": {"type": "string", "description": "Ответ носителя на команду, отправленную ранее.", "format": "byte", "nullable": true}}}, "WriteUnwrittenResponse": {"type": "object", "description": "Ответ на запрос WriteUnwrittenRequest.", "additionalProperties": false, "properties": {"state": {"type": "integer", "description": "Текущее состояние сессии.", "format": "int32"}, "cardCommand": {"type": "string", "description": "Команда для отправки на носитель.", "format": "byte", "nullable": true}, "services": {"type": "array", "description": "Описание приобретённых билетов.", "items": {"$ref": "#/components/schemas/AgentTicketService"}}}}, "WriteUnwrittenRequest": {"type": "object", "description": "Запрос информации о билетах на носителе.", "additionalProperties": false, "properties": {"clientSessionId": {"type": "string", "description": "Идентификатор сессии, использованный в запросе read."}, "state": {"type": "integer", "description": "Номер состояния запроса.", "format": "int32"}, "cardResponse": {"type": "string", "description": "Ответ носителя на команду, отправленную ранее.", "format": "byte", "nullable": true}}}, "PayWriteResponse": {"type": "object", "description": "Ответ на запрос PayWriteRequest.", "additionalProperties": false, "properties": {"state": {"type": "integer", "description": "Текущее состояние сессии.", "format": "int32"}, "cardCommand": {"type": "string", "description": "Команда для отправки на носитель.", "format": "byte", "nullable": true}, "services": {"type": "array", "description": "Описание приобретённых билетов.", "items": {"$ref": "#/components/schemas/AgentTicketService"}}}}, "PayWriteRequest": {"type": "object", "description": "Запрос информации о билетах на носителе.", "additionalProperties": false, "properties": {"clientSessionId": {"type": "string", "description": "Идентификатор сессии, использованный в запросе read."}, "state": {"type": "integer", "description": "Номер состояния запроса.", "format": "int32"}, "service": {"description": "Товар, который покупают.", "nullable": true, "oneOf": [{"$ref": "#/components/schemas/ProcessPaywriteService"}]}, "agentParams": {"type": "array", "description": "Параметры платежа агента.", "nullable": true, "items": {"$ref": "#/components/schemas/AgentTicketPaymentParamDto"}}, "cardResponse": {"type": "string", "description": "Ответ носителя на команду, отправленную ранее.", "format": "byte", "nullable": true}}}, "GetNumResponse": {"type": "object", "description": "Ответ на запрос GetNumRequest.", "additionalProperties": false, "properties": {"num": {"type": "string", "description": "Печатный номер носителя."}}}, "AgentGetUidResponse": {"type": "object", "description": "Ответ на AgentGetUidRequest>.", "additionalProperties": false, "properties": {"uid": {"type": "string", "description": "UID Носителя в шестнадцатеричном представлении."}}}, "GetCardResponse": {"type": "object", "description": "Ответ с информацией о носителе.", "additionalProperties": false, "properties": {"card": {"description": "Данные о носителе", "oneOf": [{"$ref": "#/components/schemas/Card"}]}, "tickets": {"type": "array", "description": "Список билетов на носителе.", "items": {"$ref": "#/components/schemas/AgentTicket"}}}}, "Card": {"type": "object", "description": "Базовая информация о носителе.", "additionalProperties": false, "properties": {"carrier_type": {"description": "Тип носителя.", "oneOf": [{"$ref": "#/components/schemas/CarrierTypeEnum"}]}, "uid": {"type": "string", "description": "UID Носителя в шестнадцатеричном представлении."}, "num": {"type": "string", "description": "Печатный номер носителя."}, "kind": {"description": "Вид носителя.", "oneOf": [{"$ref": "#/components/schemas/CardKind"}]}, "status": {"type": "string", "description": "Статус носителя."}}}, "CarrierTypeEnum": {"type": "string", "description": "Типы носителя в ответе ЦОТТ.", "x-enumNames": ["Card", "App", "Light", "Dual"], "enum": ["CARD", "APP", "LIGHT", "DUAL"]}, "CardKind": {"type": "object", "description": "Вид носителя.", "additionalProperties": false, "properties": {"code": {"type": "integer", "description": "Код вида носителя.", "format": "int32"}, "name": {"type": "string", "description": "Наименование вида носителя."}, "name_short": {"type": "string", "description": "Краткое наименование носителя."}}}, "AgentRequestPayResponse": {"type": "object", "description": "Ответ на AgentRequestPayRequest.", "additionalProperties": false, "properties": {"session": {"type": "string", "description": "Сессия."}, "cardSerialNumber": {"type": "integer", "description": "Номер кристалла (чипа) карты.", "format": "int64"}, "uid": {"type": "string", "description": "UID носителя в HEX."}, "tickets": {"type": "array", "description": "Список билетов.", "items": {"$ref": "#/components/schemas/AgentTicket"}}}}, "AgentProcessPayResponse": {"type": "object", "description": "Ответ на AgentProcessPayRequest.", "additionalProperties": false, "properties": {"time": {"type": "string", "description": "Дата и время платежа.", "format": "date-time"}, "serverSessionId": {"type": "string", "description": "Идентификатор сессии на стороне сервера."}}}, "AgentProcessPayRequest": {"type": "object", "description": "Запрос на подтверждение отложенного платежа.", "additionalProperties": false, "required": ["service_id", "sum"], "properties": {"service_id": {"type": "integer", "description": "Код продукта (GoodCode).", "format": "int64"}, "sum": {"type": "number", "description": "Сумма.", "format": "decimal"}, "agent_params": {"type": "array", "description": "Список параметров агента.", "nullable": true, "items": {"$ref": "#/components/schemas/AgentTicketPaymentParamDto"}}}}}}}