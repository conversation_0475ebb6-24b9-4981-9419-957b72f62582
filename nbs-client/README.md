# org.openapitools.client - Kotlin client library for API продажи билетов контаргентами

No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)

## Overview
This API client was generated by the [OpenAPI Generator](https://openapi-generator.tech) project.  By using the [openapi-spec](https://github.com/OAI/OpenAPI-Specification) from a remote server, you can easily generate an API client.

- API version: 1.0.0
- Package version: 
- Generator version: 7.7.0
- Build package: org.openapitools.codegen.languages.KotlinClientCodegen

## Requires

* Kotlin 1.7.21
* Gradle 7.5

## Build

First, create the gradle wrapper script:

```
gradle wrapper
```

Then, run:

```
./gradlew check assemble
```

This runs all tests and packages the library.

## Features/Implementation Notes

* Supports JSON inputs/outputs, File inputs, and Form inputs.
* Supports collection formats for query parameters: csv, tsv, ssv, pipes.
* Some Kotlin and Java types are fully qualified to avoid conflicts with types defined in OpenAPI definitions.
* Implementation of ApiClient is intended to reduce method counts, specifically to benefit Android targets.

<a id="documentation-for-api-endpoints"></a>
## Documentation for API Endpoints

All URIs are relative to *https://api-partners.mosmetro.ru/tks.ticketspayment*

| Class | Method | HTTP request | Description |
| ------------ | ------------- | ------------- | ------------- |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentCancelPaywrite**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentcancelpaywrite) | **POST** /v1/cancel_paywrite | Отмена записи Прямого пополненения. |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentCancelUnwritten**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentcancelunwritten) | **POST** /v1/cancel_unwritten | Отмена записи Удалённого пополненения. |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentGetKeys**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentgetkeys) | **POST** /v1/get_keys | Запрос параметров чтения блоков Носителя. |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentNotifyWriteResult**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentnotifywriteresult) | **POST** /v1/notify_write_result | Уведомление о результате записи на Носитель. |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentProcessPaywrite**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentprocesspaywrite) | **POST** /v1/process_paywrite | Подтверждение прямого пополнение. |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentProcessUnwritten**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentprocessunwritten) | **POST** /v1/process_unwritten | Подтверждение записи удалённого пополнения. |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentRequestPaywrite**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentrequestpaywrite) | **POST** /v1/request_paywrite | Запрос на прямое пополнение. |
| *BitmapTicketsPaymentApi* | [**bitmapTicketsPaymentRequestUnwritten**](docs/BitmapTicketsPaymentApi.md#bitmapticketspaymentrequestunwritten) | **POST** /v1/request_unwritten | Запрос на запись удалённого пополнения. |
| *CityServiceApi* | [**cityServiceCancelPass**](docs/CityServiceApi.md#cityservicecancelpass) | **POST** /v1/cancel_pass | Отмена списания. |
| *CityServiceApi* | [**cityServiceProcessPass**](docs/CityServiceApi.md#cityserviceprocesspass) | **POST** /v1/process_pass | Подтверждение списания. |
| *CityServiceApi* | [**cityServiceRequestWalletPass**](docs/CityServiceApi.md#cityservicerequestwalletpass) | **POST** /v1/request_wallet_pass | Запрос на спиcание ЭК. |
| *CloudSamApi* | [**cloudSamCancelPayWrite**](docs/CloudSamApi.md#cloudsamcancelpaywrite) | **POST** /v1/cloud/cancel-pay-write | Отмена прямой записи. |
| *CloudSamApi* | [**cloudSamCancelWalletPass**](docs/CloudSamApi.md#cloudsamcancelwalletpass) | **POST** /v1/cloud/cancel-wallet-pass | Отмена списания. |
| *CloudSamApi* | [**cloudSamCancelWriteUnwritten**](docs/CloudSamApi.md#cloudsamcancelwriteunwritten) | **POST** /v1/cloud/cancel-write-unwritten | Отмена записи отложенного. |
| *CloudSamApi* | [**cloudSamPayWrite**](docs/CloudSamApi.md#cloudsampaywrite) | **POST** /v1/cloud/pay-write | Покупка и запись билета на носитель. |
| *CloudSamApi* | [**cloudSamRead**](docs/CloudSamApi.md#cloudsamread) | **POST** /v1/cloud/read | Запрос информации о билетах на носителе. |
| *CloudSamApi* | [**cloudSamWalletPass**](docs/CloudSamApi.md#cloudsamwalletpass) | **POST** /v1/cloud/wallet-pass | Списание средств с ЭК носителя. |
| *CloudSamApi* | [**cloudSamWriteUnwritten**](docs/CloudSamApi.md#cloudsamwriteunwritten) | **POST** /v1/cloud/write-unwritten | Запись отложенного пополнения на носитель. |
| *ExternalInfoApi* | [**externalInfoGetCardByUid**](docs/ExternalInfoApi.md#externalinfogetcardbyuid) | **POST** /v1/get_card_by_uid | Запрос информации о носителе по его UID. |
| *ExternalInfoApi* | [**externalInfoGetNum**](docs/ExternalInfoApi.md#externalinfogetnum) | **POST** /v1/get_num | Запрос печатного номера Носителя по UID. |
| *ExternalInfoApi* | [**externalInfoGetUid**](docs/ExternalInfoApi.md#externalinfogetuid) | **POST** /v1/get_uid | Запрос UID Носителя по его транспортному номеру. |
| *ExternalInfoApi* | [**externalInfoPing**](docs/ExternalInfoApi.md#externalinfoping) | **GET** /v1/ping | Проверка доступности канала связи. |
| *TicketsPaymentApi* | [**ticketsPaymentCancelPay**](docs/TicketsPaymentApi.md#ticketspaymentcancelpay) | **POST** /v1/cancel_pay | Отмена удаленного пополнения. |
| *TicketsPaymentApi* | [**ticketsPaymentProcessPay**](docs/TicketsPaymentApi.md#ticketspaymentprocesspay) | **POST** /v1/process_pay | Подтверждение удаленного пополнения. |
| *TicketsPaymentApi* | [**ticketsPaymentRequestPay**](docs/TicketsPaymentApi.md#ticketspaymentrequestpay) | **POST** /v1/request_pay | Запрос удаленного пополнения. |
| *TicketsPaymentApi* | [**ticketsPaymentRequestPayByUid**](docs/TicketsPaymentApi.md#ticketspaymentrequestpaybyuid) | **POST** /v1/request_pay_by_uid | Запрос удаленного пополнения по UID. |


<a id="documentation-for-models"></a>
## Documentation for Models

 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentGetUidResponse](docs/AgentGetUidResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentProcessPayRequest](docs/AgentProcessPayRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentProcessPayResponse](docs/AgentProcessPayResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentReadBlock](docs/AgentReadBlock.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentRequestPayResponse](docs/AgentRequestPayResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentRequestWalletPassRequest](docs/AgentRequestWalletPassRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentRequestWalletPassResponse](docs/AgentRequestWalletPassResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket](docs/AgentTicket.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketAvailableService](docs/AgentTicketAvailableService.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParam](docs/AgentTicketPaymentParam.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParamDto](docs/AgentTicketPaymentParamDto.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketService](docs/AgentTicketService.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketStatus](docs/AgentTicketStatus.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteBlock](docs/AgentWriteBlock.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteResult](docs/AgentWriteResult.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CancelPayWriteRequest](docs/CancelPayWriteRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CancelPayWriteResponse](docs/CancelPayWriteResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CancelWalletPassRequest](docs/CancelWalletPassRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CancelWalletPassResponse](docs/CancelWalletPassResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CancelWriteUnwrittenRequest](docs/CancelWriteUnwrittenRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CancelWriteUnwrittenResponse](docs/CancelWriteUnwrittenResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.Card](docs/Card.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CardCarrierType](docs/CardCarrierType.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CardKind](docs/CardKind.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.CarrierTypeEnum](docs/CarrierTypeEnum.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ErrorResponse](docs/ErrorResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ErrorResponseError](docs/ErrorResponseError.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.GetCardResponse](docs/GetCardResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.GetCardResponseCard](docs/GetCardResponseCard.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.GetKeysResponse](docs/GetKeysResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.GetNumResponse](docs/GetNumResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.NotifyWriteResultRequest](docs/NotifyWriteResultRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.NotifyWriteResultResponse](docs/NotifyWriteResultResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.PayWriteRequest](docs/PayWriteRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.PayWriteRequestService](docs/PayWriteRequestService.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.PayWriteResponse](docs/PayWriteResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteRequest](docs/ProcessPaywriteRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteRequestService](docs/ProcessPaywriteRequestService.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteResponse](docs/ProcessPaywriteResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteService](docs/ProcessPaywriteService.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ProcessUnwrittenResponse](docs/ProcessUnwrittenResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ReadRequest](docs/ReadRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.ReadResponse](docs/ReadResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.RequestPaywriteRequest](docs/RequestPaywriteRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.RequestPaywriteResponse](docs/RequestPaywriteResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.RequestUnwrittenRequest](docs/RequestUnwrittenRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.RequestUnwrittenResponse](docs/RequestUnwrittenResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.SectorError](docs/SectorError.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.TicketBlock](docs/TicketBlock.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.WalletPassRequest](docs/WalletPassRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.WalletPassResponse](docs/WalletPassResponse.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.WriteUnwrittenRequest](docs/WriteUnwrittenRequest.md)
 - [ru.metrosoft.sputnikgate.output.nbs.model.WriteUnwrittenResponse](docs/WriteUnwrittenResponse.md)


<a id="documentation-for-authorization"></a>
## Documentation for Authorization

Endpoints do not require authorization.

