package org.openapitools.client.infrastructure

import com.squareup.moshi.adapter
import io.r2.simplepemkeystore.SimplePemKeyStoreProvider
import okhttp3.*
import okhttp3.Headers.Companion.toHeaders
import okhttp3.HttpUrl.Companion.toHttpUrlOrNull
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.RequestBody.Companion.asRequestBody
import okhttp3.RequestBody.Companion.toRequestBody
import java.io.ByteArrayInputStream
import java.io.File
import java.io.FileInputStream
import java.net.URLConnection
import java.security.KeyStore
import java.security.Security
import java.security.cert.X509Certificate
import java.time.*
import java.util.*
import java.util.concurrent.TimeUnit
import javax.net.ssl.*


val EMPTY_REQUEST: RequestBody = ByteArray(0).toRequestBody()

open class ApiClient(val baseUrl: String, val client: OkHttpClient) {
    companion object {
        protected const val ContentType = "Content-Type"
        protected const val Accept = "Accept"
        protected const val Authorization = "Authorization"
        protected const val JsonMediaType = "application/json"
        protected const val FormDataMediaType = "multipart/form-data"
        protected const val FormUrlEncMediaType = "application/x-www-form-urlencoded"
        protected const val XmlMediaType = "application/xml"
        protected const val OctetMediaType = "application/octet-stream"

        const val BASE_URL_KEY = "org.openapitools.client.baseUrl"

        @JvmStatic
        lateinit var trustManager: X509TrustManager

        private fun createCertificate(certificate: String): SSLSocketFactory {
            if (certificate.isEmpty()) {
                throw NullPointerException(
                    "Certificate is empty, check ApiClient.kt"
                )
            }

            Security.addProvider(SimplePemKeyStoreProvider())

            val trustManagers = arrayOf(
                object : X509TrustManager {
                    override fun checkClientTrusted(p0: Array<out X509Certificate>?, p1: String?) {}
                    override fun checkServerTrusted(p0: Array<out X509Certificate>?, p1: String?) {}
                    override fun getAcceptedIssuers(): Array<X509Certificate> {
                        return emptyArray()
                    }
                }
            )
            trustManager = trustManagers[0] as X509TrustManager

            val sslContext = SSLContext.getInstance("TLS")
            sslContext.init(arrayOf(createKeyManager(certificate)), trustManagers, null)
            return sslContext.socketFactory
        }

        @JvmStatic
        fun defaultClient(
            interceptors: List<Interceptor> = listOf(),
            key: String,
            certificate: String,
        ): OkHttpClient {
            val builder = OkHttpClient.Builder()

            interceptors.forEach {
                builder.addInterceptor(it)
            }
            val sslSocketFactory = createCertificate(certificate)
            return builder
                .addInterceptor { chain ->
                    val request = chain.request()
                    val newRequest = request.newBuilder()
                        .addHeader(
                            Authorization,
                            "ApiKey ${key}"
                        )
                        .build()

                    return@addInterceptor chain.proceed(newRequest)
                }
                .sslSocketFactory(sslSocketFactory = sslSocketFactory, trustManager = trustManager)
                .connectTimeout(120, TimeUnit.SECONDS)
                .readTimeout(120, TimeUnit.SECONDS)
                .writeTimeout(120, TimeUnit.SECONDS)
                .callTimeout(120, TimeUnit.SECONDS)
                .build()
        }

        private fun createKeyManager(certificate: String?): KeyManager {
            val ks = KeyStore.getInstance("simplepem")

            val certStream = ByteArrayInputStream(certificate!!.toByteArray(Charsets.UTF_8))
            ks.load(certStream, CharArray(0))

            val kmf = KeyManagerFactory.getInstance("SunX509")
            kmf.init(ks, CharArray(0))

            return kmf.keyManagers[0] as KeyManager
        }
    }

    /**
     * Guess Content-Type header from the given file (defaults to "application/octet-stream").
     *
     * @param file The given file
     * @return The guessed Content-Type
     */
    protected fun guessContentTypeFromFile(file: File): String {
        val contentType = URLConnection.guessContentTypeFromName(file.name)
        return contentType ?: "application/octet-stream"
    }

    protected inline fun <reified T> requestBody(content: T, mediaType: String?): RequestBody =
        when {
            content is File -> content.asRequestBody(
                (mediaType ?: guessContentTypeFromFile(content)).toMediaTypeOrNull()
            )

            mediaType == FormDataMediaType ->
                MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .apply {
                        // content's type *must* be Map<String, PartConfig<*>>
                        @Suppress("UNCHECKED_CAST")
                        (content as Map<String, PartConfig<*>>).forEach { (name, part) ->
                            if (part.body is File) {
                                val partHeaders = part.headers.toMutableMap() +
                                        ("Content-Disposition" to "form-data; name=\"$name\"; filename=\"${part.body.name}\"")
                                val fileMediaType = guessContentTypeFromFile(part.body).toMediaTypeOrNull()
                                addPart(
                                    partHeaders.toHeaders(),
                                    part.body.asRequestBody(fileMediaType)
                                )
                            } else {
                                val partHeaders = part.headers.toMutableMap() +
                                        ("Content-Disposition" to "form-data; name=\"$name\"")
                                addPart(
                                    partHeaders.toHeaders(),
                                    parameterToString(part.body).toRequestBody(null)
                                )
                            }
                        }
                    }.build()

            mediaType == FormUrlEncMediaType -> {
                FormBody.Builder().apply {
                    // content's type *must* be Map<String, PartConfig<*>>
                    @Suppress("UNCHECKED_CAST")
                    (content as Map<String, PartConfig<*>>).forEach { (name, part) ->
                        add(name, parameterToString(part.body))
                    }
                }.build()
            }

            mediaType == null || mediaType.startsWith("application/") && mediaType.endsWith("json") ->
                if (content == null) {
                    EMPTY_REQUEST
                } else {
                    Serializer.moshi.adapter(T::class.java).toJson(content)
                        .toRequestBody((mediaType ?: JsonMediaType).toMediaTypeOrNull())
                }

            mediaType == XmlMediaType -> throw UnsupportedOperationException("xml not currently supported.")
            mediaType == OctetMediaType && content is ByteArray ->
                content.toRequestBody(OctetMediaType.toMediaTypeOrNull())
            // TODO: this should be extended with other serializers
            else -> throw UnsupportedOperationException("requestBody currently only supports JSON body, byte body and File body.")
        }

    @OptIn(ExperimentalStdlibApi::class)
    protected inline fun <reified T : Any?> responseBody(body: ResponseBody?, mediaType: String? = JsonMediaType): T? {
        if (body == null) {
            return null
        }
        if (T::class.java == File::class.java) {
            // return tempFile
            // Attention: if you are developing an android app that supports API Level 25 and bellow, please check flag supportAndroidApiLevel25AndBelow in https://openapi-generator.tech/docs/generators/kotlin#config-options
            val tempFile = java.nio.file.Files.createTempFile("tmp.org.openapitools.client", null).toFile()
            tempFile.deleteOnExit()
            body.byteStream().use { inputStream ->
                tempFile.outputStream().use { tempFileOutputStream ->
                    inputStream.copyTo(tempFileOutputStream)
                }
            }
            return tempFile as T
        }

        return when {
            mediaType == null || (mediaType.startsWith("application/") && mediaType.endsWith("json")) -> {
                val bodyContent = body.string()
                if (bodyContent.isEmpty()) {
                    return null
                }
                Serializer.moshi.adapter<T>().fromJson(bodyContent)
            }

            mediaType == OctetMediaType -> body.bytes() as? T
            else -> throw UnsupportedOperationException("responseBody currently only supports JSON body.")
        }
    }

    protected inline fun <reified I, reified T : Any?> request(requestConfig: RequestConfig<I>): ApiResponse<T?> {
        val httpUrl = baseUrl.toHttpUrlOrNull() ?: throw IllegalStateException("baseUrl is invalid.")

        val url = httpUrl.newBuilder()
            .addEncodedPathSegments(requestConfig.path.trimStart('/'))
            .apply {
                requestConfig.query.forEach { query ->
                    query.value.forEach { queryValue ->
                        addQueryParameter(query.key, queryValue)
                    }
                }
            }.build()

        // take content-type/accept from spec or set to default (application/json) if not defined
        if (requestConfig.body != null && requestConfig.headers[ContentType].isNullOrEmpty()) {
            requestConfig.headers[ContentType] = JsonMediaType
        }
        if (requestConfig.headers[Accept].isNullOrEmpty()) {
            requestConfig.headers[Accept] = JsonMediaType
        }
        val headers = requestConfig.headers

        if (headers[Accept].isNullOrEmpty()) {
            throw kotlin.IllegalStateException("Missing Accept header. This is required.")
        }

        val contentType = if (headers[ContentType] != null) {
            // TODO: support multiple contentType options here.
            (headers[ContentType] as String).substringBefore(";").lowercase(Locale.US)
        } else {
            null
        }

        val request = when (requestConfig.method) {
            RequestMethod.DELETE -> Request.Builder().url(url).delete(requestBody(requestConfig.body, contentType))
            RequestMethod.GET -> Request.Builder().url(url)
            RequestMethod.HEAD -> Request.Builder().url(url).head()
            RequestMethod.PATCH -> Request.Builder().url(url).patch(requestBody(requestConfig.body, contentType))
            RequestMethod.PUT -> Request.Builder().url(url).put(requestBody(requestConfig.body, contentType))
            RequestMethod.POST -> Request.Builder().url(url).post(requestBody(requestConfig.body, contentType))
            RequestMethod.OPTIONS -> Request.Builder().url(url).method("OPTIONS", null)
        }.apply {
            headers.forEach { header -> addHeader(header.key, header.value) }
        }.build()

        val response = client.newCall(request).execute()

        val accept = response.header(ContentType)?.substringBefore(";")?.lowercase(Locale.US)

        // TODO: handle specific mapping types. e.g. Map<int, Class<?>>
        @Suppress("UNNECESSARY_SAFE_CALL")
        return response.use {
            when {
                it.isRedirect -> Redirection(
                    it.code,
                    it.headers.toMultimap()
                )

                it.isInformational -> Informational(
                    it.message,
                    it.code,
                    it.headers.toMultimap()
                )

                it.isSuccessful -> Success(
                    responseBody(it.body, accept),
                    it.code,
                    it.headers.toMultimap()
                )

                it.isClientError -> ClientError(
                    it.message,
                    responseBody(it.body, accept),
                    it.code,
                    it.headers.toMultimap()
                )

                else -> ServerError(
                    it.message,
                    it.body?.string(),
                    it.code,
                    it.headers.toMultimap()
                )
            }
        }
    }

    protected fun parameterToString(value: Any?): String = when (value) {
        null -> ""
        is Array<*> -> toMultiValue(value, "csv").toString()
        is Iterable<*> -> toMultiValue(value, "csv").toString()
        is OffsetDateTime, is OffsetTime, is LocalDateTime, is LocalDate, is LocalTime ->
            parseDateToQueryString(value)

        else -> value.toString()
    }

    protected inline fun <reified T : Any> parseDateToQueryString(value: T): String {
        /*
        .replace("\"", "") converts the json object string to an actual string for the query parameter.
        The moshi or gson adapter allows a more generic solution instead of trying to use a native
        formatter. It also easily allows to provide a simple way to define a custom date format pattern
        inside a gson/moshi adapter.
        */
        return Serializer.moshi.adapter(T::class.java).toJson(value).replace("\"", "")
    }
}
