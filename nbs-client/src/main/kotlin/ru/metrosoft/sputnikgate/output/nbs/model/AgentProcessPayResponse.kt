/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на AgentProcessPayRequest.
 *
 * @param time Дата и время платежа.
 * @param serverSessionId Идентификатор сессии на стороне сервера.
 */


data class AgentProcessPayResponse (

    /* Дата и время платежа. */
    @Json(name = "time")
    val time: java.time.OffsetDateTime,

    /* Идентификатор сессии на стороне сервера. */
    @Json(name = "serverSessionId")
    val serverSessionId: kotlin.String

) {


}

