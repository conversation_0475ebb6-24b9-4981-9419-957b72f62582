/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Параметр.
 *
 * @param id Идентификатор.
 * @param name Название.
 * @param `value` Значение.
 */


data class AgentTicketPaymentParam (

    /* Идентификатор. */
    @<PERSON><PERSON>(name = "id")
    val id: kotlin.String? = null,

    /* Название. */
    @<PERSON><PERSON>(name = "name")
    val name: kotlin.String? = null,

    /* Значение. */
    @<PERSON><PERSON>(name = "value")
    val `value`: kotlin.String? = null

) {


}

