/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос WalletPassRequest.
 *
 * @param state Текущее состояние сессии.
 * @param cardCommand Команда для отправки на носитель.
 * @param balanceOnCard Баланс на носителе после списания.
 */


data class WalletPassResponse (

    /* Текущее состояние сессии. */
    @Json(name = "state")
    val state: kotlin.Int? = null,

    /* Команда для отправки на носитель. */
    @J<PERSON>(name = "cardCommand")
    val cardCommand: kotlin.ByteArray? = null,

    /* Баланс на носителе после списания. */
    @Json(name = "balanceOnCard")
    val balanceOnCard: java.math.BigDecimal? = null

) {


}

