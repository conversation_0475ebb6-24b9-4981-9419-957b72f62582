/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Запрос на подтверждение отложенного платежа.
 *
 * @param id Идентификатор.
 * @param `value` Значение.
 * @param name Название.
 */


data class AgentTicketPaymentParamDto (

    /* Идентификатор. */
    @Json(name = "id")
    val id: kotlin.String,

    /* Значение. */
    @<PERSON><PERSON>(name = "value")
    val `value`: kotlin.String,

    /* Название. */
    @Json(name = "name")
    val name: kotlin.String? = null

) {


}

