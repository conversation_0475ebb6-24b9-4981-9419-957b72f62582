/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteResult

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Уведомление о результате записи на Носитель.
 *
 * @param writeResults Результат записи на Носитель.
 */


data class NotifyWriteResultRequest (

    /* Результат записи на Носитель. */
    @Json(name = "write_results")
    @JsonProperty("write_results")
    @JsonAlias("writeResults")
    val writeResults: kotlin.collections.List<AgentWriteResult>

) {


}

