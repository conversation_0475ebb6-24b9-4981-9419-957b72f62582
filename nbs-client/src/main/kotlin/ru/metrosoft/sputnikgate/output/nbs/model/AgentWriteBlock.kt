/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Блок для записи, и адреса в SAM ключей для его записи и контрольного чтения.
 *
 * @param block Сквозной номер блока (начиная с нуля)
 * @param `value` Содержимое блока в виде строки в шестнадцатеричном представлении
 * @param writeKeyType Тип ключа записи, A или B. По умолчанию A
 * @param writeKeyId Идентификатор ключа записи в SAM
 * @param writeKeyVersion Версия ключа записи в SAM. По умолчанию 0
 * @param writeKeyValue Значение ключа для записи.
 * @param readKeyId Идентификатор ключа чтения в SAM
 * @param readKeyVersion Версия ключа чтения в SAM. По умолчанию 0
 * @param readKeyValue Значение ключа для чтения.
 * @param readKeyType Тип ключа чтения, A или B. По умолчанию A
 */


data class AgentWriteBlock (

    /* Сквозной номер блока (начиная с нуля) */
    @Json(name = "block")
    val block: kotlin.Int,

    /* Содержимое блока в виде строки в шестнадцатеричном представлении */
    @Json(name = "value")
    val `value`: kotlin.String,

    /* Тип ключа записи, A или B. По умолчанию A */
    @Json(name = "write_key_type")
    val writeKeyType: kotlin.String,

    /* Идентификатор ключа записи в SAM */
    @Json(name = "write_key_id")
    val writeKeyId: kotlin.Int,

    /* Версия ключа записи в SAM. По умолчанию 0 */
    @Json(name = "write_key_version")
    val writeKeyVersion: kotlin.Int,

    /* Значение ключа для записи. */
    @Json(name = "write_key_value")
    val writeKeyValue: kotlin.String,

    /* Идентификатор ключа чтения в SAM */
    @Json(name = "read_key_id")
    val readKeyId: kotlin.Int? = null,

    /* Версия ключа чтения в SAM. По умолчанию 0 */
    @Json(name = "read_key_version")
    val readKeyVersion: kotlin.Int? = null,

    /* Значение ключа для чтения. */
    @Json(name = "read_key_value")
    val readKeyValue: kotlin.String? = null,

    /* Тип ключа чтения, A или B. По умолчанию A */
    @Json(name = "read_key_type")
    val readKeyType: kotlin.String = "A"

) {


}

