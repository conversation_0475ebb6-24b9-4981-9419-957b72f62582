/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос RequestUnwrittenRequest.
 *
 * @param session Идентификатор сессии.
 * @param num Печатный номер носителя.
 * @param cardSerialNumber Серийный номер кристала носителя.
 * @param tickets Список билетов.
 */


data class RequestUnwrittenResponse (

    /* Идентификатор сессии. */
    @Json(name = "session")
    val session: kotlin.String,

    /* Печатный номер носителя. */
    @Json(name = "num")
    val num: kotlin.String,

    /* Серийный номер кристала носителя. */
    @Json(name = "cardSerialNumber")
    val cardSerialNumber: kotlin.Long? = null,

    /* Список билетов. */
    @Json(name = "tickets")
    val tickets: kotlin.collections.List<AgentTicket>? = null

) {


}

