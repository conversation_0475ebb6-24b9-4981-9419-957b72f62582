/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Запрос информации о билетах на носителе.
 *
 * @param clientSessionId Идентификатор сессии. 16 байт в шестнадцатиричном формате.
 * @param uid UID Носителя в шестнадцатеричном представлении.
 * @param state Номер состояния запроса
 * @param cardResponse Ответ носителя на команду, отправленную ранее.
 */


data class ReadRequest (

    /* Идентификатор сессии. 16 байт в шестнадцатиричном формате. */
    @Json(name = "clientSessionId")
    @JsonProperty("clientSessionId")
    @JsonAlias("clientSessionId")
    val clientSessionId: kotlin.String? = null,

    /* UID Носителя в шестнадцатеричном представлении. */
    @Json(name = "uid")
    val uid: kotlin.ByteArray? = null,

    /* Номер состояния запроса */
    @Json(name = "state")
    val state: kotlin.Int? = null,

    /* Ответ носителя на команду, отправленную ранее. */
    @Json(name = "cardResponse")
    @JsonProperty("cardResponse")
    @JsonAlias("cardResponse")
    val cardResponse: kotlin.ByteArray? = null

) {


}

