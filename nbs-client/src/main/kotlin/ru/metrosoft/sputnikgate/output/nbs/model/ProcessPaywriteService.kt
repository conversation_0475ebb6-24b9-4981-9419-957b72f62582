/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Информация о товаре.
 *
 * @param serviceId Код продукта (GoodCode).
 * @param sum Сумма.
 */


data class ProcessPaywriteService (

    /* Код продукта (GoodCode). */
    @Json(name = "service_id")
    val serviceId: kotlin.Int,

    /* Сумма. */
    @Json(name = "sum")
    val sum: java.math.BigDecimal

) {


}

