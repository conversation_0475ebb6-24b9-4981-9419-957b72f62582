/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.CarrierTypeEnum

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Тип носителя.
 *
 */


enum class CardCarrierType {
    CARD, APP, LIGHT, DUAL
}

