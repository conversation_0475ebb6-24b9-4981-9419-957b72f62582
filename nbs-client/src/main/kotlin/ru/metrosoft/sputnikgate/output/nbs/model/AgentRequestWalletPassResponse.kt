/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteBlock

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос AgentRequestWalletPassRequest.
 *
 * @param session Идентификатор сессии списания.
 * @param blocks Блоки для записи.
 * @param passTime Дата и время списания.
 * @param balanceOnCard Баланс на карте.
 */


data class AgentRequestWalletPassResponse (

    /* Идентификатор сессии списания. */
    @Json(name = "session")
    val session: kotlin.String,

    /* Блоки для записи. */
    @Json(name = "blocks")
    val blocks: kotlin.collections.List<AgentWriteBlock>,

    /* Дата и время списания. */
    @Json(name = "pass_time")
    val passTime: java.time.OffsetDateTime,

    /* Баланс на карте. */
    @Json(name = "balance_on_card")
    val balanceOnCard: java.math.BigDecimal? = null

) {


}

