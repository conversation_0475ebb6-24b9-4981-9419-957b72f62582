/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Прочитанный блок.
 *
 * @param block Сквозной номер блока (начиная с нуля).
 * @param `value` Содержимое блока в виде строки в шестнадцатеричном представлении.
 */


data class AgentReadBlock (

    /* Сквозной номер блока (начиная с нуля). */
    @Json(name = "block")
    val block: kotlin.Int,

    /* Содержимое блока в виде строки в шестнадцатеричном представлении. */
    @Json(name = "value")
    val `value`: kotlin.String

) {


}

