/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на AgentGetUidRequest>.
 *
 * @param uid UID Носителя в шестнадцатеричном представлении.
 */


data class AgentGetUidResponse (

    /* UID Носителя в шестнадцатеричном представлении. */
    @Json(name = "uid")
    val uid: kotlin.String? = null

) {


}

