/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentReadBlock

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Запрос на прямое пополнение.
 *
 * @param bitmap Считанные блоки.
 */


data class RequestPaywriteRequest (

    /* Считанные блоки. */
    @Json(name = "bitmap")
    val bitmap: kotlin.collections.List<AgentReadBlock>

) {


}

