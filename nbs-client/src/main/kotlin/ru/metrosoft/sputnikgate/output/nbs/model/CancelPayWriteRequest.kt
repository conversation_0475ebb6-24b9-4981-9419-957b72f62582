/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Запрос на отмену списания.
 *
 * @param clientSessionId Идентификатор сессии. 16 байт в шестнадцатиричном формате.
 */


data class CancelPayWriteRequest (

    /* Идентификатор сессии. 16 байт в шестнадцатиричном формате. */
    @Json(name = "clientSessionId")
    @JsonProperty("clientSessionId")
    @JsonAlias("clientSessionId")
    val clientSessionId: kotlin.String? = null

) {


}

