/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParam

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Сервис билета.
 *
 * @param active Признак действующего билета.
 * @param id Идентификатор услуги.
 * @param name Наименование услуги (краткое).
 * @param session Идентификатор сессии (транзакции), в рамках которой была приобретена эта услуга.
 * @param payTime Дата и время оплаты.
 * @param sum Сумма.
 * @param params Параметры.
 * @param prolongation Признак продлённого билета.
 */


data class AgentTicketService (

    /* Признак действующего билета. */
    @Json(name = "active")
    val active: kotlin.Boolean? = null,

    /* Идентификатор услуги. */
    @Json(name = "id")
    val id: kotlin.Int? = null,

    /* Наименование услуги (краткое). */
    @Json(name = "name")
    val name: kotlin.String? = null,

    /* Идентификатор сессии (транзакции), в рамках которой была приобретена эта услуга. */
    @Json(name = "session")
    val session: kotlin.String? = null,

    /* Дата и время оплаты. */
    @Json(name = "pay_time")
    val payTime: java.time.OffsetDateTime? = null,

    /* Сумма. */
    @Json(name = "sum")
    val sum: java.math.BigDecimal? = null,

    /* Параметры. */
    @Json(name = "params")
    val params: kotlin.collections.List<AgentTicketPaymentParam>? = null,

    /* Признак продлённого билета. */
    @Json(name = "prolongation")
    val prolongation: kotlin.Boolean? = null

) {


}

