/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на AgentRequestPayRequest.
 *
 * @param session Сессия.
 * @param cardSerialNumber Номер кристалла (чипа) карты.
 * @param uid UID носителя в HEX.
 * @param tickets Список билетов.
 */


data class AgentRequestPayResponse (

    /* Сессия. */
    @Json(name = "session")
    val session: kotlin.String,

    /* Номер кристалла (чипа) карты. */
    @Json(name = "cardSerialNumber")
    val cardSerialNumber: kotlin.Long? = null,

    /* UID носителя в HEX. */
    @Json(name = "uid")
    val uid: kotlin.String? = null,

    /* Список билетов. */
    @Json(name = "tickets")
    val tickets: kotlin.collections.List<AgentTicket>

) {


}

