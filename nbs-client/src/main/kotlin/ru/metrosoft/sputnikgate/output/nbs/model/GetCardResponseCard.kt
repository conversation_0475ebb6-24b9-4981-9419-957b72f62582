/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.Card
import ru.metrosoft.sputnikgate.output.nbs.model.CardCarrierType
import ru.metrosoft.sputnikgate.output.nbs.model.CardKind

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Данные о носителе
 *
 * @param carrierType 
 * @param uid UID Носителя в шестнадцатеричном представлении.
 * @param num Печатный номер носителя.
 * @param kind 
 * @param status Статус носителя.
 */


data class GetCardResponseCard (

    @Json(name = "carrier_type")
    val carrierType: CardCarrierType? = null,

    /* UID Носителя в шестнадцатеричном представлении. */
    @Json(name = "uid")
    val uid: kotlin.String? = null,

    /* Печатный номер носителя. */
    @Json(name = "num")
    val num: kotlin.String? = null,

    @Json(name = "kind")
    val kind: CardKind? = null,

    /* Статус носителя. */
    @Json(name = "status")
    val status: kotlin.String? = null

) {


}

