/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос RequestPaywriteRequest.
 *
 * @param session Идентификатор сессии.
 * @param num Печатный номер Носителя.
 * @param tickets Список билетов.
 */


data class RequestPaywriteResponse (

    /* Идентификатор сессии. */
    @<PERSON>son(name = "session")
    val session: kotlin.String,

    /* Печатный номер Носителя. */
    @Json(name = "num")
    val num: kotlin.String? = null,

    /* Список билетов. */
    @Json(name = "tickets")
    val tickets: kotlin.collections.List<AgentTicket>? = null

) {


}

