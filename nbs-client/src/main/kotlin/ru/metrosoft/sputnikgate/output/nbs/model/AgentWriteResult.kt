/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Результат записи блока.
 *
 * @param block Сквозной номер блока (начиная с нуля).
 * @param result Результат записи.              Возможные значения: *  OK *  WRITE_ERROR *  READ_ERROR *  COMPARE_ERROR *  CARD_RETURN_ERROR *  NO_WRITE *  OTHER
 */


data class AgentWriteResult (

    /* Сквозной номер блока (начиная с нуля). */
    @Json(name = "block")
    val block: kotlin.Int,

    /* Результат записи.              Возможные значения: *  OK *  WRITE_ERROR *  READ_ERROR *  COMPARE_ERROR *  CARD_RETURN_ERROR *  NO_WRITE *  OTHER */
    @Json(name = "result")
    val result: kotlin.String

) {


}

