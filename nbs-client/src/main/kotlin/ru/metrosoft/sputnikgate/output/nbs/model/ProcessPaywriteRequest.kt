/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParamDto
import ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteRequestService

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Подтверждение прямого пополнение.
 *
 * @param service
 * @param agentParams Параметры платежа агента.
 */


data class ProcessPaywriteRequest(
    @Json(name = "service")
    val service: ProcessPaywriteRequestService,

    /* Параметры платежа агента. */
    @Json(name = "agent_params")
    @JsonProperty("agent_params")
    @JsonAlias("agentParams")
    val agentParams: kotlin.collections.List<AgentTicketPaymentParamDto>
)

