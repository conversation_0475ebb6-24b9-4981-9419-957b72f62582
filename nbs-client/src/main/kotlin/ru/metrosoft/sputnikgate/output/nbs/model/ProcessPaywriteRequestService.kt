/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteService

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Товар, который покупают.
 *
 * @param serviceId Код продукта (GoodCode).
 * @param sum Сумма.
 */


data class ProcessPaywriteRequestService (

    /* Код продукта (GoodCode). */
    @<PERSON><PERSON>(name = "service_id")
    @JsonProperty("service_id")
    @JsonAlias("serviceId")
    val serviceId: kotlin.Int,

    /* Сумма. */
    @Json(name = "sum")
    val sum: java.math.BigDecimal

) {


}

