/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Информация об ошибке в секторе.
 *
 * @param number Номер сектора.
 * @param code Код статуса сектора.
 * @param comment Текстовое описание ошибки.
 */


data class SectorError (

    /* Номер сектора. */
    @J<PERSON>(name = "number")
    val number: kotlin.Int? = null,

    /* Код статуса сектора. */
    @Json(name = "code")
    val code: kotlin.Int? = null,

    /* Текстовое описание ошибки. */
    @Json(name = "comment")
    val comment: kotlin.String? = null

) {


}

