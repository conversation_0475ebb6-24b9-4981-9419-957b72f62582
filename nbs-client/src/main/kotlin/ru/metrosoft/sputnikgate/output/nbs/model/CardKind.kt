/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.CardKind

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Вид носителя.
 *
 * @param code Код вида носителя.
 * @param name Наименование вида носителя.
 * @param nameShort Краткое наименование носителя.
 */


data class CardKind (

    /* Код вида носителя. */
    @Json(name = "code")
    val code: kotlin.Int? = null,

    /* Наименование вида носителя. */
    @Json(name = "name")
    val name: kotlin.String? = null,

    /* Краткое наименование носителя. */
    @Json(name = "name_short")
    val nameShort: kotlin.String? = null

) {


}

