/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketStatus

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Статус билета.
 *
 * @param code Код статуса.
 * @param comment Описание.
 */


data class AgentTicketStatus (

    /* Код статуса. */
    @Json(name = "code")
    val code: kotlin.String? = null,

    /* Описание. */
    @Json(name = "comment")
    val comment: kotlin.String? = null

) {


}

