/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.api

import okhttp3.HttpUrl
import okhttp3.OkHttpClient
import org.openapitools.client.infrastructure.*
import org.openapitools.client.infrastructure.ClientException
import org.openapitools.client.infrastructure.ServerException
import ru.metrosoft.sputnikgate.output.nbs.model.*
import java.io.IOException

class BitmapTicketsPaymentApi(
    basePath: kotlin.String = defaultBasePath,
    client: OkHttpClient
) : ApiClient(basePath, client) {
    companion object {
        @JvmStatic
        val defaultBasePath: String by lazy {
            System.getProperties().getProperty(ApiClient.BASE_URL_KEY, "https://device.dev3-mm.srvdev.ru/tks.ticketspayment.webapi.contractor")
        }
    }

    /**
     * Отмена записи Прямого пополненения.
     * Отмена транзакции записи прямого пополнения ЭБ, завершает эту транзакцию. Агент сообщает ЦОТТ, что не было попыток записи.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.
     * @param session Сессия.
     * @return void
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentCancelPaywrite(session: kotlin.String) : Unit {
        val localVarResponse = bitmapTicketsPaymentCancelPaywriteWithHttpInfo(session = session)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> Unit
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Отмена записи Прямого пополненения.
     * Отмена транзакции записи прямого пополнения ЭБ, завершает эту транзакцию. Агент сообщает ЦОТТ, что не было попыток записи.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.
     * @param session Сессия.
     * @return ApiResponse<Unit?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentCancelPaywriteWithHttpInfo(session: kotlin.String) : ApiResponse<Unit?> {
        val localVariableConfig = bitmapTicketsPaymentCancelPaywriteRequestConfig(session = session)

        return request<Unit, Unit>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentCancelPaywrite
     *
     * @param session Сессия.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentCancelPaywriteRequestConfig(session: kotlin.String) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("session", listOf(session.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/cancel_paywrite",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Отмена записи Удалённого пополненения.
     * Отмена транзакции записи отложенного ЭБ, завершает эту транзакцию. Агент сообщает ЦОТТ, что не было попыток записи.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_unwritten&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.
     * @param session Сессия.
     * @return void
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentCancelUnwritten(session: kotlin.String) : Unit {
        val localVarResponse = bitmapTicketsPaymentCancelUnwrittenWithHttpInfo(session = session)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> Unit
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Отмена записи Удалённого пополненения.
     * Отмена транзакции записи отложенного ЭБ, завершает эту транзакцию. Агент сообщает ЦОТТ, что не было попыток записи.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_unwritten&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.
     * @param session Сессия.
     * @return ApiResponse<Unit?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentCancelUnwrittenWithHttpInfo(session: kotlin.String) : ApiResponse<Unit?> {
        val localVariableConfig = bitmapTicketsPaymentCancelUnwrittenRequestConfig(session = session)

        return request<Unit, Unit>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentCancelUnwritten
     *
     * @param session Сессия.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentCancelUnwrittenRequestConfig(session: kotlin.String) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("session", listOf(session.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/cancel_unwritten",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Запрос параметров чтения блоков Носителя.
     * ЦОТТ сообщает Агенту, какие блоки и какими ключами из SAM следует считать с Носителя, чтобы работать с ЭБ.
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @return GetKeysResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentGetKeys(uid: kotlin.String) : GetKeysResponse {
        val localVarResponse = bitmapTicketsPaymentGetKeysWithHttpInfo(uid = uid)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as GetKeysResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Запрос параметров чтения блоков Носителя.
     * ЦОТТ сообщает Агенту, какие блоки и какими ключами из SAM следует считать с Носителя, чтобы работать с ЭБ.
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @return ApiResponse<GetKeysResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentGetKeysWithHttpInfo(uid: kotlin.String) : ApiResponse<GetKeysResponse?> {
        val localVariableConfig = bitmapTicketsPaymentGetKeysRequestConfig(uid = uid)

        return request<Unit, GetKeysResponse>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentGetKeys
     *
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentGetKeysRequestConfig(uid: kotlin.String) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("uid", listOf(uid.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/get_keys",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Уведомление о результате записи на Носитель.
     * Агент сообщает ЦОТТ о результате записи на Носитель.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_paywrite&#x60; или &#x60;process_unwritten&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.
     * @param session Сессия.
     * @param notifyWriteResultRequest Запрос.
     * @return NotifyWriteResultResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentNotifyWriteResult(session: kotlin.String, notifyWriteResultRequest: NotifyWriteResultRequest) : NotifyWriteResultResponse {
        val localVarResponse = bitmapTicketsPaymentNotifyWriteResultWithHttpInfo(session = session, notifyWriteResultRequest = notifyWriteResultRequest)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as NotifyWriteResultResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Уведомление о результате записи на Носитель.
     * Агент сообщает ЦОТТ о результате записи на Носитель.              Запрос должен отправляться только в случае успешного ответа сервера на &#x60;process_paywrite&#x60; или &#x60;process_unwritten&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.
     * @param session Сессия.
     * @param notifyWriteResultRequest Запрос.
     * @return ApiResponse<NotifyWriteResultResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentNotifyWriteResultWithHttpInfo(session: kotlin.String, notifyWriteResultRequest: NotifyWriteResultRequest) : ApiResponse<NotifyWriteResultResponse?> {
        val localVariableConfig = bitmapTicketsPaymentNotifyWriteResultRequestConfig(session = session, notifyWriteResultRequest = notifyWriteResultRequest)

        return request<NotifyWriteResultRequest, NotifyWriteResultResponse>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentNotifyWriteResult
     *
     * @param session Сессия.
     * @param notifyWriteResultRequest Запрос.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentNotifyWriteResultRequestConfig(session: kotlin.String, notifyWriteResultRequest: NotifyWriteResultRequest) : RequestConfig<NotifyWriteResultRequest> {
        val localVariableBody = notifyWriteResultRequest
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("session", listOf(session.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/notify_write_result",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Подтверждение прямого пополнение.
     * Фиксируется оплачиваемая услуга и выдаются блоки, которые следует записать на Носитель. Завершает транзакцию продажи с записью на Носитель.              Этот запрос можно отправлять только после успешного ответа на запрос &#x60;/request_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.              Атрибут &#x60;status_code&#x60; для соответствующего билета должен быть равен &#x60;OK&#x60;. Если &#x60;status_code&#x60; для соответствующего билета равен &#x60;UNWRITTEN&#x60;, то следует вызывать &#x60;/request_unwritten&#x60;, чтобы выполнить сценарий \&quot;Запись Удаленного пополнения\&quot;.              Для следующих параметров, если агент их указывает в &#x60;agent_params&#x60;, следует использовать предопределенные имена: * &#x60;pay_type&#x60; - способ оплаты:   * **CASH** - наличные;   * **CARD** - банковская карта;   * **PHONE** - мобильный телефон;   * **INTERNET** - электронная платежная система; * &#x60;pos&#x60; - номер точки продаж              Рекомендуется также заполнить: * &#x60;phone&#x60; - номер телефона плательщика, если он известен. 11 цифр без разделителей.
     * @param session Сессия.
     * @param processPaywriteRequest Запрос.
     * @return ProcessPaywriteResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentProcessPaywrite(session: kotlin.String, processPaywriteRequest: ProcessPaywriteRequest) : ProcessPaywriteResponse {
        val localVarResponse = bitmapTicketsPaymentProcessPaywriteWithHttpInfo(session = session, processPaywriteRequest = processPaywriteRequest)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as ProcessPaywriteResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Подтверждение прямого пополнение.
     * Фиксируется оплачиваемая услуга и выдаются блоки, которые следует записать на Носитель. Завершает транзакцию продажи с записью на Носитель.              Этот запрос можно отправлять только после успешного ответа на запрос &#x60;/request_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.              Атрибут &#x60;status_code&#x60; для соответствующего билета должен быть равен &#x60;OK&#x60;. Если &#x60;status_code&#x60; для соответствующего билета равен &#x60;UNWRITTEN&#x60;, то следует вызывать &#x60;/request_unwritten&#x60;, чтобы выполнить сценарий \&quot;Запись Удаленного пополнения\&quot;.              Для следующих параметров, если агент их указывает в &#x60;agent_params&#x60;, следует использовать предопределенные имена: * &#x60;pay_type&#x60; - способ оплаты:   * **CASH** - наличные;   * **CARD** - банковская карта;   * **PHONE** - мобильный телефон;   * **INTERNET** - электронная платежная система; * &#x60;pos&#x60; - номер точки продаж              Рекомендуется также заполнить: * &#x60;phone&#x60; - номер телефона плательщика, если он известен. 11 цифр без разделителей.
     * @param session Сессия.
     * @param processPaywriteRequest Запрос.
     * @return ApiResponse<ProcessPaywriteResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentProcessPaywriteWithHttpInfo(session: kotlin.String, processPaywriteRequest: ProcessPaywriteRequest) : ApiResponse<ProcessPaywriteResponse?> {
        val localVariableConfig = bitmapTicketsPaymentProcessPaywriteRequestConfig(session = session, processPaywriteRequest = processPaywriteRequest)

        return request<ProcessPaywriteRequest, ProcessPaywriteResponse>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentProcessPaywrite
     *
     * @param session Сессия.
     * @param processPaywriteRequest Запрос.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentProcessPaywriteRequestConfig(session: kotlin.String, processPaywriteRequest: ProcessPaywriteRequest) : RequestConfig<ProcessPaywriteRequest> {
        val localVariableBody = processPaywriteRequest
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("session", listOf(session.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/process_paywrite",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Подтверждение записи удалённого пополнения.
     * Выдаются блоки отложенного ЭБ, которые следует записать на Носитель.              Завершает транзакцию записи отложенного ЭБ.              Этот запрос можно отправлять только после успешного ответа на запрос &#x60;request_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.              При отсутствии билетов, которые можно записать в данный момент, возвращается ошибка &#x60;NO_TICKETS_TO_WRITE&#x60; либо &#x60;NO_ROOM_TO_WRITE_TICKET&#x60;. Эту ошибку ЦОТТ может вернуть, даже если есть ожидающие записи отложенные (например, если текущий остаток на носителе не позволяет записать ожидающий записи отложенный).
     * @param session Сессия.
     * @return ProcessUnwrittenResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentProcessUnwritten(session: kotlin.String) : ProcessUnwrittenResponse {
        val localVarResponse = bitmapTicketsPaymentProcessUnwrittenWithHttpInfo(session = session)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as ProcessUnwrittenResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Подтверждение записи удалённого пополнения.
     * Выдаются блоки отложенного ЭБ, которые следует записать на Носитель.              Завершает транзакцию записи отложенного ЭБ.              Этот запрос можно отправлять только после успешного ответа на запрос &#x60;request_paywrite&#x60;. Значение &#x60;session&#x60; должно совпадать со значением &#x60;session&#x60; из ответа на него.              При отсутствии билетов, которые можно записать в данный момент, возвращается ошибка &#x60;NO_TICKETS_TO_WRITE&#x60; либо &#x60;NO_ROOM_TO_WRITE_TICKET&#x60;. Эту ошибку ЦОТТ может вернуть, даже если есть ожидающие записи отложенные (например, если текущий остаток на носителе не позволяет записать ожидающий записи отложенный).
     * @param session Сессия.
     * @return ApiResponse<ProcessUnwrittenResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentProcessUnwrittenWithHttpInfo(session: kotlin.String) : ApiResponse<ProcessUnwrittenResponse?> {
        val localVariableConfig = bitmapTicketsPaymentProcessUnwrittenRequestConfig(session = session)

        return request<Unit, ProcessUnwrittenResponse>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentProcessUnwritten
     *
     * @param session Сессия.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentProcessUnwrittenRequestConfig(session: kotlin.String) : RequestConfig<Unit> {
        val localVariableBody = null
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("session", listOf(session.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/process_unwritten",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Запрос на прямое пополнение.
     * Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.              При наличии Носителя в реестре замененных возвращается ошибка, в поле &#x60;replaced_num&#x60; возвращается печатный номер Носителя, на который был заменен Носитель из запроса.              При успешном выполнении этого запроса, начинается транзакция продажи с записью. В ответе возвращается номер транзакции (&#x60;session&#x60;). Его следует указывать в последующих сообщениях сценария.              Если запрос &#x60;request_paywrite&#x60; сообщает о незаписанных отложенных ЭБ, следует, начав сценарий с начала, выполнить запись отложенных (&#x60;get_keys&#x60;, &#x60;request_unwritten&#x60;, ..), а потом заново начать сценарий продажи с записью.
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @param requestPaywriteRequest Запрос.
     * @return RequestPaywriteResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentRequestPaywrite(uid: kotlin.String, requestPaywriteRequest: RequestPaywriteRequest) : RequestPaywriteResponse {
        val localVarResponse = bitmapTicketsPaymentRequestPaywriteWithHttpInfo(uid = uid, requestPaywriteRequest = requestPaywriteRequest)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as RequestPaywriteResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.body?.status} ${localVarError.body?.title.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Запрос на прямое пополнение.
     * Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.              При наличии Носителя в реестре замененных возвращается ошибка, в поле &#x60;replaced_num&#x60; возвращается печатный номер Носителя, на который был заменен Носитель из запроса.              При успешном выполнении этого запроса, начинается транзакция продажи с записью. В ответе возвращается номер транзакции (&#x60;session&#x60;). Его следует указывать в последующих сообщениях сценария.              Если запрос &#x60;request_paywrite&#x60; сообщает о незаписанных отложенных ЭБ, следует, начав сценарий с начала, выполнить запись отложенных (&#x60;get_keys&#x60;, &#x60;request_unwritten&#x60;, ..), а потом заново начать сценарий продажи с записью.
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @param requestPaywriteRequest Запрос.
     * @return ApiResponse<RequestPaywriteResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentRequestPaywriteWithHttpInfo(uid: kotlin.String, requestPaywriteRequest: RequestPaywriteRequest) : ApiResponse<RequestPaywriteResponse?> {
        val localVariableConfig = bitmapTicketsPaymentRequestPaywriteRequestConfig(uid = uid, requestPaywriteRequest = requestPaywriteRequest)

        return request<RequestPaywriteRequest, RequestPaywriteResponse>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentRequestPaywrite
     *
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @param requestPaywriteRequest Запрос.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentRequestPaywriteRequestConfig(uid: kotlin.String, requestPaywriteRequest: RequestPaywriteRequest) : RequestConfig<RequestPaywriteRequest> {
        val localVariableBody = requestPaywriteRequest
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("uid", listOf(uid.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/request_paywrite",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }

    /**
     * Запрос на запись удалённого пополнения.
     * Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.              При наличии Носителя в реестре замененных возвращается ошибка, в поле &#x60;replaced_num&#x60; возвращается печатный номер Носителя, на который был заменен Носитель из запроса.              При успешном выполнении этого запроса, начинается транзакция записи отложенного ЭБ. В ответе возвращается номер транзакции (&#x60;session&#x60;). Его следует указывать в последующих сообщениях сценария.
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @param requestUnwrittenRequest Запрос.
     * @return RequestUnwrittenResponse
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     * @throws UnsupportedOperationException If the API returns an informational or redirection response
     * @throws ClientException If the API returns a client error response
     * @throws ServerException If the API returns a server error response
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class, UnsupportedOperationException::class, ClientException::class, ServerException::class)
    fun bitmapTicketsPaymentRequestUnwritten(uid: kotlin.String, requestUnwrittenRequest: RequestUnwrittenRequest) : RequestUnwrittenResponse {
        val localVarResponse = bitmapTicketsPaymentRequestUnwrittenWithHttpInfo(uid = uid, requestUnwrittenRequest = requestUnwrittenRequest)

        return when (localVarResponse.responseType) {
            ResponseType.Success -> (localVarResponse as Success<*>).data as RequestUnwrittenResponse
            ResponseType.Informational -> throw UnsupportedOperationException("Client does not support Informational responses.")
            ResponseType.Redirection -> throw UnsupportedOperationException("Client does not support Redirection responses.")
            ResponseType.ClientError -> {
                val localVarError = localVarResponse as ClientError<*>
                throw ClientException("Client error : ${localVarError.statusCode} ${localVarError.message.orEmpty()}", localVarError.statusCode, localVarResponse)
            }
            ResponseType.ServerError -> {
                val localVarError = localVarResponse as ServerError<*>
                throw ServerException("Server error : ${localVarError.statusCode} ${localVarError.message.orEmpty()} ${localVarError.body}", localVarError.statusCode, localVarResponse)
            }
        }
    }

    /**
     * Запрос на запись удалённого пополнения.
     * Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.              При наличии Носителя в реестре замененных возвращается ошибка, в поле &#x60;replaced_num&#x60; возвращается печатный номер Носителя, на который был заменен Носитель из запроса.              При успешном выполнении этого запроса, начинается транзакция записи отложенного ЭБ. В ответе возвращается номер транзакции (&#x60;session&#x60;). Его следует указывать в последующих сообщениях сценария.
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @param requestUnwrittenRequest Запрос.
     * @return ApiResponse<RequestUnwrittenResponse?>
     * @throws IllegalStateException If the request is not correctly configured
     * @throws IOException Rethrows the OkHttp execute method exception
     */
    @Suppress("UNCHECKED_CAST")
    @Throws(IllegalStateException::class, IOException::class)
    fun bitmapTicketsPaymentRequestUnwrittenWithHttpInfo(uid: kotlin.String, requestUnwrittenRequest: RequestUnwrittenRequest) : ApiResponse<RequestUnwrittenResponse?> {
        val localVariableConfig = bitmapTicketsPaymentRequestUnwrittenRequestConfig(uid = uid, requestUnwrittenRequest = requestUnwrittenRequest)

        return request<RequestUnwrittenRequest, RequestUnwrittenResponse>(
            localVariableConfig
        )
    }

    /**
     * To obtain the request config of the operation bitmapTicketsPaymentRequestUnwritten
     *
     * @param uid UID Носителя в шестнадцатеричном представлении.
     * @param requestUnwrittenRequest Запрос.
     * @return RequestConfig
     */
    fun bitmapTicketsPaymentRequestUnwrittenRequestConfig(uid: kotlin.String, requestUnwrittenRequest: RequestUnwrittenRequest) : RequestConfig<RequestUnwrittenRequest> {
        val localVariableBody = requestUnwrittenRequest
        val localVariableQuery: MultiValueMap = mutableMapOf<kotlin.String, kotlin.collections.List<kotlin.String>>()
            .apply {
                put("uid", listOf(uid.toString()))
            }
        val localVariableHeaders: MutableMap<String, String> = mutableMapOf()
        localVariableHeaders["Content-Type"] = "application/json"
        localVariableHeaders["Accept"] = "application/json"

        return RequestConfig(
            method = RequestMethod.POST,
            path = "/v1/request_unwritten",
            query = localVariableQuery,
            headers = localVariableHeaders,
            requiresAuthentication = false,
            body = localVariableBody
        )
    }


    private fun encodeURIComponent(uriComponent: kotlin.String): kotlin.String =
        HttpUrl.Builder().scheme("http").host("localhost").addPathSegment(uriComponent).build().encodedPathSegments[0]
}
