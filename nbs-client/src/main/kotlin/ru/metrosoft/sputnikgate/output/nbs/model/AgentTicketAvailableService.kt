/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Доступный к продаже сервис билета.
 *
 * @param id Идентификатор.
 * @param name Название.
 * @param priceMin Стоимость услуги, нижняя граница.
 * @param priceMax Стоимость услуги, верхняя граница.
 * @param expirationPeriod Срок действия в днях.
 * @param expirationType Тип срока действия.
 * @param lifetimeExceeded Признак, что срок действия услуги выходит за срок действия карты.
 * @param remainedDays Кол-во оставшихся (действующих) дней
 */


data class AgentTicketAvailableService (

    /* Идентификатор. */
    @Json(name = "id")
    val id: kotlin.Int,

    /* Название. */
    @Json(name = "name")
    val name: kotlin.String? = null,

    /* Стоимость услуги, нижняя граница. */
    @Json(name = "price_min")
    val priceMin: java.math.BigDecimal? = null,

    /* Стоимость услуги, верхняя граница. */
    @Json(name = "price_max")
    val priceMax: java.math.BigDecimal? = null,

    /* Срок действия в днях. */
    @Json(name = "expiration_period")
    val expirationPeriod: kotlin.Int? = null,

    /* Тип срока действия. */
    @Json(name = "expiration_type")
    val expirationType: kotlin.String? = null,

    /* Признак, что срок действия услуги выходит за срок действия карты. */
    @Json(name = "lifetime_exceeded")
    val lifetimeExceeded: kotlin.String? = null,

    /* Кол-во оставшихся (действующих) дней */
    @Json(name = "remained_days")
    val remainedDays: kotlin.Int? = null

) {


}

