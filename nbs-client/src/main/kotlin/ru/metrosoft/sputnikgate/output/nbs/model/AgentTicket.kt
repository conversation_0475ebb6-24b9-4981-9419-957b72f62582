/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketAvailableService
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketService
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketStatus

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Билет.
 *
 * @param lifetimeEnd Срок годности билета.
 * @param code Код.
 * @param name Название.
 * @param number Номер.
 * @param status 
 * @param currentServices Список текущих услуг транспортного приложения, записанных на Носитель. Может быть пустым.
 * @param unwrittenServices Список услуг отложенных ЭБ транспортного приложения, ожидающих записи.
 * @param availableServices Список доступных для покупки ЭБ.
 * @param expiredServices Список просроченных услуг, срок действия которых истек и поэтому они уже никогда не будут записаны. Просроченные услуги находятся в этом списке настраиваемый период времени, далее убираются из него.
 */


data class AgentTicket (

    /* Срок годности билета. */
    @Json(name = "lifetime_end")
    val lifetimeEnd: kotlin.String? = null,

    /* Код. */
    @Json(name = "code")
    val code: kotlin.String? = null,

    /* Название. */
    @Json(name = "name")
    val name: kotlin.String? = null,

    /* Номер. */
    @Json(name = "number")
    val number: kotlin.String? = null,

    @Json(name = "status")
    val status: AgentTicketStatus? = null,

    /* Список текущих услуг транспортного приложения, записанных на Носитель. Может быть пустым. */
    @Json(name = "current_services")
    val currentServices: kotlin.collections.List<AgentTicketService> = emptyList(),

    /* Список услуг отложенных ЭБ транспортного приложения, ожидающих записи. */
    @Json(name = "unwritten_services")
    val unwrittenServices: kotlin.collections.List<AgentTicketService>? = null,

    /* Список доступных для покупки ЭБ. */
    @Json(name = "available_services")
    val availableServices: kotlin.collections.List<AgentTicketAvailableService>? = null,

    /* Список просроченных услуг, срок действия которых истек и поэтому они уже никогда не будут записаны. Просроченные услуги находятся в этом списке настраиваемый период времени, далее убираются из него. */
    @Json(name = "expired_services")
    val expiredServices: kotlin.collections.List<AgentTicketService>? = null

) {


}

