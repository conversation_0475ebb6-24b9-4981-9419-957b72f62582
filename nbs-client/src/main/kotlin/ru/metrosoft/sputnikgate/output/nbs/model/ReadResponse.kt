/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос ReadRequest.
 *
 * @param state Текущее состояние сессии.
 * @param cardCommand Команда для отправки на носитель.
 * @param num Печатный номер Носителя.
 * @param tickets Список билетов.
 */


data class ReadResponse (

    /* Текущее состояние сессии. */
    @Json(name = "state")
    val state: kotlin.Int? = null,

    /* Команда для отправки на носитель. */
    @Json(name = "cardCommand")
    val cardCommand: kotlin.ByteArray? = null,

    /* Печатный номер Носителя. */
    @Json(name = "num")
    val num: kotlin.String? = null,

    /* Список билетов. */
    @Json(name = "tickets")
    val tickets: kotlin.collections.List<AgentTicket>? = null

) {


}

