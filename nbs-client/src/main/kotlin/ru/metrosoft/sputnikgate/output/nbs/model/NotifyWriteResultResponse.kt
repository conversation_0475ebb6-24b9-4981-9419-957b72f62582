/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос NotifyWriteResultRequest.
 *
 * @param time Время регистрации платежа.
 */


data class NotifyWriteResultResponse (

    /* Время регистрации платежа. */
    @Json(name = "time")
    val time: java.time.OffsetDateTime? = null

) {


}

