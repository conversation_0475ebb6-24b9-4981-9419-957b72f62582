/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentReadBlock

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Запрос на списание с ЭК.
 *
 * @param uid UID Носителя в шестнадцатеричном представлении.
 * @param bitmap Считанные блоки.
 * @param amount Сумма списания.
 */


data class AgentRequestWalletPassRequest (

    /* UID Носителя в шестнадцатеричном представлении. */
    @Json(name = "uid")
    val uid: kotlin.String,

    /* Считанные блоки. */
    @Json(name = "bitmap")
    val bitmap: kotlin.collections.List<AgentReadBlock>,

    /* Сумма списания. */
    @Json(name = "amount")
    val amount: java.math.BigDecimal

) {


}

