/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import com.squareup.moshi.Json

/**
 * Ответ на запрос ProcessUnwrittenRequest.
 *
 * @param session Идентификатор сессии.
 * @param blocks Блоки для записи.
 * @param service Описание приобретённых билетов.
 */


data class ProcessUnwrittenResponse (

    /* Идентификатор сессии. */
    @Json(name = "session")
    val session: kotlin.String,

    /* Блоки для записи. */
    @<PERSON><PERSON>(name = "blocks")
    val blocks: kotlin.collections.List<AgentWriteBlock>,

    /* Описание приобретённых билетов. */
    @Json(name = "service")
    val service: kotlin.collections.List<AgentTicketService>? = null,

    /* Дата и время оплаты. */
    @Json(name = "time")
    val time: java.time.OffsetDateTime? = null,

) {


}

