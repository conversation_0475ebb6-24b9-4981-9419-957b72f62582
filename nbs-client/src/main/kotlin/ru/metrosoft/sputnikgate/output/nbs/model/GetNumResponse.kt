/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос GetNumRequest.
 *
 * @param num Печатный номер носителя.
 */


data class GetNumResponse (

    /* Печатный номер носителя. */
    @Json(name = "num")
    val num: kotlin.String? = null

) {


}

