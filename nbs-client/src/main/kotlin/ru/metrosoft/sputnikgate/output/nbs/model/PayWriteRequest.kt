/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParamDto
import ru.metrosoft.sputnikgate.output.nbs.model.PayWriteRequestService

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Запрос информации о билетах на носителе.
 *
 * @param clientSessionId Идентификатор сессии, использованный в запросе read.
 * @param state Номер состояния запроса.
 * @param service 
 * @param agentParams Параметры платежа агента.
 * @param cardResponse Ответ носителя на команду, отправленную ранее.
 */


data class PayWriteRequest (

    /* Идентификатор сессии, использованный в запросе read. */
    @Json(name = "clientSessionId")
    @JsonProperty("clientSessionId")
    @JsonAlias("clientSessionId")
    val clientSessionId: kotlin.String? = null,

    /* Номер состояния запроса. */
    @Json(name = "state")
    val state: kotlin.Int? = null,

    @Json(name = "service")
    val service: PayWriteRequestService? = null,

    /* Параметры платежа агента. */
    @Json(name = "agentParams")
    @JsonProperty("agentParams")
    @JsonAlias("agentParams")
    val agentParams: kotlin.collections.List<AgentTicketPaymentParamDto>? = null,

    /* Ответ носителя на команду, отправленную ранее. */
    @Json(name = "cardResponse")
    @JsonProperty("cardResponse")
    @JsonAlias("cardResponse")
    val cardResponse: kotlin.ByteArray? = null

) {


}

