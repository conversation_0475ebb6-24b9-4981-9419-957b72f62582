/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Блок, в котором содержится ЭБ, и ключ для его чтения.
 *
 * @param block Сквозной номер блока (начиная с нуля).
 * @param readKeyId Идентификатор ключа чтения.
 * @param readKeyVersion Версия ключа чтения.
 * @param readKeyType Тип ключа чтения, A или B. По умолчанию A.
 * @param readKeyValue Значение ключа SL1 для чтения Блока.
 */


data class TicketBlock (

    /* Сквозной номер блока (начиная с нуля). */
    @Json(name = "block")
    val block: kotlin.Int? = null,

    /* Идентификатор ключа чтения. */
    @Json(name = "read_key_id")
    val readKeyId: kotlin.Int? = null,

    /* Версия ключа чтения. */
    @Json(name = "read_key_version")
    val readKeyVersion: kotlin.Int? = null,

    /* Тип ключа чтения, A или B. По умолчанию A. */
    @Json(name = "read_key_type")
    val readKeyType: kotlin.String? = null,

    /* Значение ключа SL1 для чтения Блока. */
    @Json(name = "read_key_value")
    val readKeyValue: kotlin.String? = null

) {


}

