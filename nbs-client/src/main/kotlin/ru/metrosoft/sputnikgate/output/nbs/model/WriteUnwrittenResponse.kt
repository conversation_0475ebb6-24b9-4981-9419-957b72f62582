/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketService

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос WriteUnwrittenRequest.
 *
 * @param state Текущее состояние сессии.
 * @param cardCommand Команда для отправки на носитель.
 * @param services Описание приобретённых билетов.
 */


data class WriteUnwrittenResponse (

    /* Текущее состояние сессии. */
    @Json(name = "state")
    val state: kotlin.Int? = null,

    /* Команда для отправки на носитель. */
    @Json(name = "cardCommand")
    val cardCommand: kotlin.ByteArray? = null,

    /* Описание приобретённых билетов. */
    @Json(name = "services")
    val services: kotlin.collections.List<AgentTicketService>? = null

) {


}

