/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonProperty
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParamDto

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Запрос на подтверждение отложенного платежа.
 *
 * @param serviceId Код продукта (GoodCode).
 * @param sum Сумма.
 * @param agentParams Список параметров агента.
 */


data class AgentProcessPayRequest (

    /* Код продукта (GoodCode). */
    @Json(name = "service_id")
    @JsonProperty("service_id")
    @JsonAlias("serviceId")
    val serviceId: kotlin.Long,

    /* Сумма. */
    @Json(name = "sum")
    val sum: java.math.BigDecimal,

    /* Список параметров агента. */
    @Json(name = "agent_params")
    @JsonProperty("agent_params")
    @JsonAlias("agentParams")
    val agentParams: kotlin.collections.List<AgentTicketPaymentParamDto>? = null

) {


}

