/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Типы носителя в ответе ЦОТТ.
 *
 * Values: CARD,APP,LIGHT,DUAL
 */

@JsonClass(generateAdapter = false)
enum class CarrierTypeEnum(val value: kotlin.String) {

    @Json(name = "CARD")
    CARD("CARD"),

    @Json(name = "APP")
    APP("APP"),

    @<PERSON><PERSON>(name = "LIGHT")
    LIGHT("LIGHT"),

    @<PERSON>son(name = "DUAL")
    DUAL("DUAL");

    /**
     * Override [toString()] to avoid using the enum variable name as the value, and instead use
     * the actual value defined in the API spec file.
     *
     * This solves a problem when the variable name and its value are different, and ensures that
     * the client sends the correct enum values to the server always.
     */
    override fun toString(): kotlin.String = value

    companion object {
        /**
         * Converts the provided [data] to a [String] on success, null otherwise.
         */
        fun encode(data: kotlin.Any?): kotlin.String? = if (data is CarrierTypeEnum) "$data" else null

        /**
         * Returns a valid [CarrierTypeEnum] for [data], null otherwise.
         */
        fun decode(data: kotlin.Any?): CarrierTypeEnum? = data?.let {
          val normalizedData = "$it".lowercase()
          values().firstOrNull { value ->
            it == value || normalizedData == "$value".lowercase()
          }
        }
    }
}

