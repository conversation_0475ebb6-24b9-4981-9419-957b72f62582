/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.ErrorResponseError

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ с ошибкой ЦОТТ.
 *
 * @param error 
 */


data class ErrorResponse (

    @Json(name = "title")
    val title: String? = null,

    @<PERSON><PERSON>(name = "error")
    val error: ErrorResponseError? = null,

    @Json(name = "status")
    val status: Int? = null,

    @<PERSON><PERSON>(name = "traceId")
    val traceId: String? = null,

) {


}

