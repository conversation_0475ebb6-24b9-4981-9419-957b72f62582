/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket
import ru.metrosoft.sputnikgate.output.nbs.model.GetCardResponseCard

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ с информацией о носителе.
 *
 * @param card 
 * @param tickets Список билетов на носителе.
 */


data class GetCardResponse (

    @Json(name = "card")
    val card: GetCardResponseCard? = null,

    /* Список билетов на носителе. */
    @Json(name = "tickets")
    val tickets: kotlin.collections.List<AgentTicket>? = null

) {


}

