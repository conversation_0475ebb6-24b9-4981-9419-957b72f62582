/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.ErrorResponseError
import ru.metrosoft.sputnikgate.output.nbs.model.SectorError

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Объект ошибки.
 *
 * @param code Символьный код ошибки.
 * @param comment Поясняющее сообщение.
 * @param replacedNum Печатный номер нового носителя.
 * @param sectorErrors Список ошибок по секторам.
 */


data class ErrorResponseError (

    /* Символьный код ошибки. */
    @Json(name = "code")
    val code: kotlin.String? = null,

    /* Поясняющее сообщение. */
    @Json(name = "comment")
    val comment: kotlin.String? = null,

    /* Печатный номер нового носителя. */
    @Json(name = "replacedNum")
    val replacedNum: kotlin.String? = null,

    /* Список ошибок по секторам. */
    @Json(name = "sectorErrors")
    val sectorErrors: kotlin.collections.List<SectorError>? = null

) {


}

