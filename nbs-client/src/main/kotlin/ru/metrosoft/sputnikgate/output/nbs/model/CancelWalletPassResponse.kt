/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model


import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос CancelWalletPassRequest.
 *
 * @param considerAsWritten Признак успешно выполненной записи, не смотря на отмену операции.
 */


data class CancelWalletPassResponse (

    /* Признак успешно выполненной записи, не смотря на отмену операции. */
    @<PERSON><PERSON>(name = "considerAsWritten")
    val considerAsWritten: kotlin.Boolean? = null

) {


}

