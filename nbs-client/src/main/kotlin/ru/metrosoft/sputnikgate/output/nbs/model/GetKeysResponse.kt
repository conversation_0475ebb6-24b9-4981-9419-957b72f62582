/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import ru.metrosoft.sputnikgate.output.nbs.model.TicketBlock

import com.squareup.moshi.Json
import com.squareup.moshi.JsonClass

/**
 * Ответ на запрос GetKeysRequest.
 *
 * @param map Список информации о ключах для блоков.
 */


data class GetKeysResponse (

    /* Список информации о ключах для блоков. */
    @Json(name = "map")
    val map: kotlin.collections.List<TicketBlock>? = null

) {


}

