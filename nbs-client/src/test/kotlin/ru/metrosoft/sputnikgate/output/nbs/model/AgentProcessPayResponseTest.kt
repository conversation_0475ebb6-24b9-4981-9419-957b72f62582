/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentProcessPayResponse

class AgentProcessPayResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentProcessPayResponse
        //val modelInstance = AgentProcessPayResponse()

        // to test the property `time` - Дата и время платежа.
        should("test time") {
            // uncomment below to test the property
            //modelInstance.time shouldBe ("TODO")
        }

        // to test the property `serverSessionId` - Идентификатор сессии на стороне сервера.
        should("test serverSessionId") {
            // uncomment below to test the property
            //modelInstance.serverSessionId shouldBe ("TODO")
        }

    }
}
