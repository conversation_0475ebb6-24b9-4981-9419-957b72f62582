/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.TicketBlock

class TicketBlockTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of TicketBlock
        //val modelInstance = TicketBlock()

        // to test the property `block` - Сквозной номер блока (начиная с нуля).
        should("test block") {
            // uncomment below to test the property
            //modelInstance.block shouldBe ("TODO")
        }

        // to test the property `readKeyId` - Идентификатор ключа чтения.
        should("test readKeyId") {
            // uncomment below to test the property
            //modelInstance.readKeyId shouldBe ("TODO")
        }

        // to test the property `readKeyVersion` - Версия ключа чтения.
        should("test readKeyVersion") {
            // uncomment below to test the property
            //modelInstance.readKeyVersion shouldBe ("TODO")
        }

        // to test the property `readKeyType` - Тип ключа чтения, A или B. По умолчанию A.
        should("test readKeyType") {
            // uncomment below to test the property
            //modelInstance.readKeyType shouldBe ("TODO")
        }

        // to test the property `readKeyValue` - Значение ключа SL1 для чтения Блока.
        should("test readKeyValue") {
            // uncomment below to test the property
            //modelInstance.readKeyValue shouldBe ("TODO")
        }

    }
}
