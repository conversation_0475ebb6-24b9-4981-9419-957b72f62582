/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.CancelWriteUnwrittenResponse

class CancelWriteUnwrittenResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of CancelWriteUnwrittenResponse
        //val modelInstance = CancelWriteUnwrittenResponse()

        // to test the property `considerAsWritten` - Признак успешно выполненной записи, не смотря на отмену операции.
        should("test considerAsWritten") {
            // uncomment below to test the property
            //modelInstance.considerAsWritten shouldBe ("TODO")
        }

    }
}
