/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentRequestPayResponse
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket

class AgentRequestPayResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentRequestPayResponse
        //val modelInstance = AgentRequestPayResponse()

        // to test the property `session` - Сессия.
        should("test session") {
            // uncomment below to test the property
            //modelInstance.session shouldBe ("TODO")
        }

        // to test the property `cardSerialNumber` - Номер кристалла (чипа) карты.
        should("test cardSerialNumber") {
            // uncomment below to test the property
            //modelInstance.cardSerialNumber shouldBe ("TODO")
        }

        // to test the property `uid` - UID носителя в HEX.
        should("test uid") {
            // uncomment below to test the property
            //modelInstance.uid shouldBe ("TODO")
        }

        // to test the property `tickets` - Список билетов.
        should("test tickets") {
            // uncomment below to test the property
            //modelInstance.tickets shouldBe ("TODO")
        }

    }
}
