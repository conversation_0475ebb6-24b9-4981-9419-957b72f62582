/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.WalletPassResponse

class WalletPassResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of WalletPassResponse
        //val modelInstance = WalletPassResponse()

        // to test the property `state` - Текущее состояние сессии.
        should("test state") {
            // uncomment below to test the property
            //modelInstance.state shouldBe ("TODO")
        }

        // to test the property `cardCommand` - Команда для отправки на носитель.
        should("test cardCommand") {
            // uncomment below to test the property
            //modelInstance.cardCommand shouldBe ("TODO")
        }

        // to test the property `balanceOnCard` - Баланс на носителе после списания.
        should("test balanceOnCard") {
            // uncomment below to test the property
            //modelInstance.balanceOnCard shouldBe ("TODO")
        }

    }
}
