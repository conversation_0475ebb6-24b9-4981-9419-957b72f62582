/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.GetKeysResponse
import ru.metrosoft.sputnikgate.output.nbs.model.TicketBlock

class GetKeysResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of GetKeysResponse
        //val modelInstance = GetKeysResponse()

        // to test the property `map` - Список информации о ключах для блоков.
        should("test map") {
            // uncomment below to test the property
            //modelInstance.map shouldBe ("TODO")
        }

    }
}
