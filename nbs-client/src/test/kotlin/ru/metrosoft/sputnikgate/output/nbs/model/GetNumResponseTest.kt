/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.GetNumResponse

class GetNumResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of GetNumResponse
        //val modelInstance = GetNumResponse()

        // to test the property `num` - Печатный номер носителя.
        should("test num") {
            // uncomment below to test the property
            //modelInstance.num shouldBe ("TODO")
        }

    }
}
