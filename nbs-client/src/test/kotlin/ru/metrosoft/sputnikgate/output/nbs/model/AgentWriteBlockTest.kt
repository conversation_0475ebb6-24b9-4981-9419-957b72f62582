/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteBlock

class AgentWriteBlockTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentWriteBlock
        //val modelInstance = AgentWriteBlock()

        // to test the property `block` - Сквозной номер блока (начиная с нуля)
        should("test block") {
            // uncomment below to test the property
            //modelInstance.block shouldBe ("TODO")
        }

        // to test the property ``value`` - Содержимое блока в виде строки в шестнадцатеричном представлении
        should("test `value`") {
            // uncomment below to test the property
            //modelInstance.`value` shouldBe ("TODO")
        }

        // to test the property `writeKeyType` - Тип ключа записи, A или B. По умолчанию A
        should("test writeKeyType") {
            // uncomment below to test the property
            //modelInstance.writeKeyType shouldBe ("TODO")
        }

        // to test the property `writeKeyId` - Идентификатор ключа записи в SAM
        should("test writeKeyId") {
            // uncomment below to test the property
            //modelInstance.writeKeyId shouldBe ("TODO")
        }

        // to test the property `writeKeyVersion` - Версия ключа записи в SAM. По умолчанию 0
        should("test writeKeyVersion") {
            // uncomment below to test the property
            //modelInstance.writeKeyVersion shouldBe ("TODO")
        }

        // to test the property `writeKeyValue` - Значение ключа для записи.
        should("test writeKeyValue") {
            // uncomment below to test the property
            //modelInstance.writeKeyValue shouldBe ("TODO")
        }

        // to test the property `readKeyId` - Идентификатор ключа чтения в SAM
        should("test readKeyId") {
            // uncomment below to test the property
            //modelInstance.readKeyId shouldBe ("TODO")
        }

        // to test the property `readKeyVersion` - Версия ключа чтения в SAM. По умолчанию 0
        should("test readKeyVersion") {
            // uncomment below to test the property
            //modelInstance.readKeyVersion shouldBe ("TODO")
        }

        // to test the property `readKeyValue` - Значение ключа для чтения.
        should("test readKeyValue") {
            // uncomment below to test the property
            //modelInstance.readKeyValue shouldBe ("TODO")
        }

        // to test the property `readKeyType` - Тип ключа чтения, A или B. По умолчанию A
        should("test readKeyType") {
            // uncomment below to test the property
            //modelInstance.readKeyType shouldBe ("TODO")
        }

    }
}
