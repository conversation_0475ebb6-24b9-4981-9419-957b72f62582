/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.NotifyWriteResultRequest
import ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteResult

class NotifyWriteResultRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of NotifyWriteResultRequest
        //val modelInstance = NotifyWriteResultRequest()

        // to test the property `writeResults` - Результат записи на Носитель.
        should("test writeResults") {
            // uncomment below to test the property
            //modelInstance.writeResults shouldBe ("TODO")
        }

    }
}
