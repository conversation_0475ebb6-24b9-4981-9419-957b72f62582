/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentRequestWalletPassResponse
import ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteBlock

class AgentRequestWalletPassResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentRequestWalletPassResponse
        //val modelInstance = AgentRequestWalletPassResponse()

        // to test the property `session` - Идентификатор сессии списания.
        should("test session") {
            // uncomment below to test the property
            //modelInstance.session shouldBe ("TODO")
        }

        // to test the property `blocks` - Блоки для записи.
        should("test blocks") {
            // uncomment below to test the property
            //modelInstance.blocks shouldBe ("TODO")
        }

        // to test the property `passTime` - Дата и время списания.
        should("test passTime") {
            // uncomment below to test the property
            //modelInstance.passTime shouldBe ("TODO")
        }

        // to test the property `balanceOnCard` - Баланс на карте.
        should("test balanceOnCard") {
            // uncomment below to test the property
            //modelInstance.balanceOnCard shouldBe ("TODO")
        }

    }
}
