/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteResult

class AgentWriteResultTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentWriteResult
        //val modelInstance = AgentWriteResult()

        // to test the property `block` - Сквозной номер блока (начиная с нуля).
        should("test block") {
            // uncomment below to test the property
            //modelInstance.block shouldBe ("TODO")
        }

        // to test the property `result` - Результат записи.              Возможные значения: *  OK *  WRITE_ERROR *  READ_ERROR *  COMPARE_ERROR *  CARD_RETURN_ERROR *  NO_WRITE *  OTHER
        should("test result") {
            // uncomment below to test the property
            //modelInstance.result shouldBe ("TODO")
        }

    }
}
