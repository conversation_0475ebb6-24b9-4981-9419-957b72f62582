/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentReadBlock

class AgentReadBlockTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentReadBlock
        //val modelInstance = AgentReadBlock()

        // to test the property `block` - Сквозной номер блока (начиная с нуля).
        should("test block") {
            // uncomment below to test the property
            //modelInstance.block shouldBe ("TODO")
        }

        // to test the property ``value`` - Содержимое блока в виде строки в шестнадцатеричном представлении.
        should("test `value`") {
            // uncomment below to test the property
            //modelInstance.`value` shouldBe ("TODO")
        }

    }
}
