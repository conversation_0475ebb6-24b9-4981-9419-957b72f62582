/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentProcessPayRequest
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParamDto

class AgentProcessPayRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentProcessPayRequest
        //val modelInstance = AgentProcessPayRequest()

        // to test the property `serviceId` - Код продукта (GoodCode).
        should("test serviceId") {
            // uncomment below to test the property
            //modelInstance.serviceId shouldBe ("TODO")
        }

        // to test the property `sum` - Сумма.
        should("test sum") {
            // uncomment below to test the property
            //modelInstance.sum shouldBe ("TODO")
        }

        // to test the property `agentParams` - Список параметров агента.
        should("test agentParams") {
            // uncomment below to test the property
            //modelInstance.agentParams shouldBe ("TODO")
        }

    }
}
