/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.ErrorResponseError
import ru.metrosoft.sputnikgate.output.nbs.model.SectorError

class ErrorResponseErrorTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of ErrorResponseError
        //val modelInstance = ErrorResponseError()

        // to test the property `code` - Символьный код ошибки.
        should("test code") {
            // uncomment below to test the property
            //modelInstance.code shouldBe ("TODO")
        }

        // to test the property `comment` - Поясняющее сообщение.
        should("test comment") {
            // uncomment below to test the property
            //modelInstance.comment shouldBe ("TODO")
        }

        // to test the property `replacedNum` - Печатный номер нового носителя.
        should("test replacedNum") {
            // uncomment below to test the property
            //modelInstance.replacedNum shouldBe ("TODO")
        }

        // to test the property `sectorErrors` - Список ошибок по секторам.
        should("test sectorErrors") {
            // uncomment below to test the property
            //modelInstance.sectorErrors shouldBe ("TODO")
        }

    }
}
