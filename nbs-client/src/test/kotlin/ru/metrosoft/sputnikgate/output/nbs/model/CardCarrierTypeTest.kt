/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.CardCarrierType
import ru.metrosoft.sputnikgate.output.nbs.model.CarrierTypeEnum

class CardCarrierTypeTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of CardCarrierType
        //val modelInstance = CardCarrierType()

    }
}
