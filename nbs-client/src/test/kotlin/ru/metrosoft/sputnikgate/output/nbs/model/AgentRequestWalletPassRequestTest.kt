/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentRequestWalletPassRequest
import ru.metrosoft.sputnikgate.output.nbs.model.AgentReadBlock

class AgentRequestWalletPassRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentRequestWalletPassRequest
        //val modelInstance = AgentRequestWalletPassRequest()

        // to test the property `uid` - UID Носителя в шестнадцатеричном представлении.
        should("test uid") {
            // uncomment below to test the property
            //modelInstance.uid shouldBe ("TODO")
        }

        // to test the property `bitmap` - Считанные блоки.
        should("test bitmap") {
            // uncomment below to test the property
            //modelInstance.bitmap shouldBe ("TODO")
        }

        // to test the property `amount` - Сумма списания.
        should("test amount") {
            // uncomment below to test the property
            //modelInstance.amount shouldBe ("TODO")
        }

    }
}
