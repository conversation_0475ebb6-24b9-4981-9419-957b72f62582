/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.RequestUnwrittenResponse
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket

class RequestUnwrittenResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of RequestUnwrittenResponse
        //val modelInstance = RequestUnwrittenResponse()

        // to test the property `session` - Идентификатор сессии.
        should("test session") {
            // uncomment below to test the property
            //modelInstance.session shouldBe ("TODO")
        }

        // to test the property `num` - Печатный номер носителя.
        should("test num") {
            // uncomment below to test the property
            //modelInstance.num shouldBe ("TODO")
        }

        // to test the property `cardSerialNumber` - Серийный номер кристала носителя.
        should("test cardSerialNumber") {
            // uncomment below to test the property
            //modelInstance.cardSerialNumber shouldBe ("TODO")
        }

        // to test the property `tickets` - Список билетов.
        should("test tickets") {
            // uncomment below to test the property
            //modelInstance.tickets shouldBe ("TODO")
        }

    }
}
