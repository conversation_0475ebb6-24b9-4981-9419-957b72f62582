/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketAvailableService
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketService
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketStatus

class AgentTicketTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentTicket
        //val modelInstance = AgentTicket()

        // to test the property `lifetimeEnd` - Срок годности билета.
        should("test lifetimeEnd") {
            // uncomment below to test the property
            //modelInstance.lifetimeEnd shouldBe ("TODO")
        }

        // to test the property `code` - Код.
        should("test code") {
            // uncomment below to test the property
            //modelInstance.code shouldBe ("TODO")
        }

        // to test the property `name` - Название.
        should("test name") {
            // uncomment below to test the property
            //modelInstance.name shouldBe ("TODO")
        }

        // to test the property `number` - Номер.
        should("test number") {
            // uncomment below to test the property
            //modelInstance.number shouldBe ("TODO")
        }

        // to test the property `status`
        should("test status") {
            // uncomment below to test the property
            //modelInstance.status shouldBe ("TODO")
        }

        // to test the property `currentServices` - Список текущих услуг транспортного приложения, записанных на Носитель. Может быть пустым.
        should("test currentServices") {
            // uncomment below to test the property
            //modelInstance.currentServices shouldBe ("TODO")
        }

        // to test the property `unwrittenServices` - Список услуг отложенных ЭБ транспортного приложения, ожидающих записи.
        should("test unwrittenServices") {
            // uncomment below to test the property
            //modelInstance.unwrittenServices shouldBe ("TODO")
        }

        // to test the property `availableServices` - Список доступных для покупки ЭБ.
        should("test availableServices") {
            // uncomment below to test the property
            //modelInstance.availableServices shouldBe ("TODO")
        }

        // to test the property `expiredServices` - Список просроченных услуг, срок действия которых истек и поэтому они уже никогда не будут записаны. Просроченные услуги находятся в этом списке настраиваемый период времени, далее убираются из него.
        should("test expiredServices") {
            // uncomment below to test the property
            //modelInstance.expiredServices shouldBe ("TODO")
        }

    }
}
