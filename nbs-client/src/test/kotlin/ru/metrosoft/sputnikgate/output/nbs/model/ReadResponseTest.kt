/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.ReadResponse
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket

class ReadResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of ReadResponse
        //val modelInstance = ReadResponse()

        // to test the property `state` - Текущее состояние сессии.
        should("test state") {
            // uncomment below to test the property
            //modelInstance.state shouldBe ("TODO")
        }

        // to test the property `cardCommand` - Команда для отправки на носитель.
        should("test cardCommand") {
            // uncomment below to test the property
            //modelInstance.cardCommand shouldBe ("TODO")
        }

        // to test the property `num` - Печатный номер Носителя.
        should("test num") {
            // uncomment below to test the property
            //modelInstance.num shouldBe ("TODO")
        }

        // to test the property `tickets` - Список билетов.
        should("test tickets") {
            // uncomment below to test the property
            //modelInstance.tickets shouldBe ("TODO")
        }

    }
}
