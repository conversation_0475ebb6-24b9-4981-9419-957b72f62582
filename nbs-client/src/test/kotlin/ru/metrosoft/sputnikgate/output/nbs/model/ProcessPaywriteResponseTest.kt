/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteResponse
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketService
import ru.metrosoft.sputnikgate.output.nbs.model.AgentWriteBlock

class ProcessPaywriteResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of ProcessPaywriteResponse
        //val modelInstance = ProcessPaywriteResponse()

        // to test the property `session` - Идентификатор сессии.
        should("test session") {
            // uncomment below to test the property
            //modelInstance.session shouldBe ("TODO")
        }

        // to test the property `blocks` - Блоки для записи.
        should("test blocks") {
            // uncomment below to test the property
            //modelInstance.blocks shouldBe ("TODO")
        }

        // to test the property `service` - Описание приобретённых билетов.
        should("test service") {
            // uncomment below to test the property
            //modelInstance.service shouldBe ("TODO")
        }

    }
}
