/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.CancelPayWriteResponse

class CancelPayWriteResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of CancelPayWriteResponse
        //val modelInstance = CancelPayWriteResponse()

        // to test the property `considerAsWritten` - Признак успешно выполненной записи, не смотря на отмену операции.
        should("test considerAsWritten") {
            // uncomment below to test the property
            //modelInstance.considerAsWritten shouldBe ("TODO")
        }

    }
}
