/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentGetUidResponse

class AgentGetUidResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentGetUidResponse
        //val modelInstance = AgentGetUidResponse()

        // to test the property `uid` - UID Носителя в шестнадцатеричном представлении.
        should("test uid") {
            // uncomment below to test the property
            //modelInstance.uid shouldBe ("TODO")
        }

    }
}
