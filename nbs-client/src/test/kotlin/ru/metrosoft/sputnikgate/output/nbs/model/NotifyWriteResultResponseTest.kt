/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.NotifyWriteResultResponse

class NotifyWriteResultResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of NotifyWriteResultResponse
        //val modelInstance = NotifyWriteResultResponse()

        // to test the property `time` - Время регистрации платежа.
        should("test time") {
            // uncomment below to test the property
            //modelInstance.time shouldBe ("TODO")
        }

    }
}
