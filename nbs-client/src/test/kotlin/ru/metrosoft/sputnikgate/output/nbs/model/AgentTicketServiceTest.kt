/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketService
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParam

class AgentTicketServiceTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentTicketService
        //val modelInstance = AgentTicketService()

        // to test the property `active` - Признак действующего билета.
        should("test active") {
            // uncomment below to test the property
            //modelInstance.active shouldBe ("TODO")
        }

        // to test the property `id` - Идентификатор услуги.
        should("test id") {
            // uncomment below to test the property
            //modelInstance.id shouldBe ("TODO")
        }

        // to test the property `name` - Наименование услуги (краткое).
        should("test name") {
            // uncomment below to test the property
            //modelInstance.name shouldBe ("TODO")
        }

        // to test the property `session` - Идентификатор сессии (транзакции), в рамках которой была приобретена эта услуга.
        should("test session") {
            // uncomment below to test the property
            //modelInstance.session shouldBe ("TODO")
        }

        // to test the property `payTime` - Дата и время оплаты.
        should("test payTime") {
            // uncomment below to test the property
            //modelInstance.payTime shouldBe ("TODO")
        }

        // to test the property `sum` - Сумма.
        should("test sum") {
            // uncomment below to test the property
            //modelInstance.sum shouldBe ("TODO")
        }

        // to test the property `params` - Параметры.
        should("test params") {
            // uncomment below to test the property
            //modelInstance.params shouldBe ("TODO")
        }

        // to test the property `prolongation` - Признак продлённого билета.
        should("test prolongation") {
            // uncomment below to test the property
            //modelInstance.prolongation shouldBe ("TODO")
        }

    }
}
