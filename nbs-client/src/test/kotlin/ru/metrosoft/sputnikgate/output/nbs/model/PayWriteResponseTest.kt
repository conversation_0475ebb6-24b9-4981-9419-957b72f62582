/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.PayWriteResponse
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketService

class PayWriteResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of PayWriteResponse
        //val modelInstance = PayWriteResponse()

        // to test the property `state` - Текущее состояние сессии.
        should("test state") {
            // uncomment below to test the property
            //modelInstance.state shouldBe ("TODO")
        }

        // to test the property `cardCommand` - Команда для отправки на носитель.
        should("test cardCommand") {
            // uncomment below to test the property
            //modelInstance.cardCommand shouldBe ("TODO")
        }

        // to test the property `services` - Описание приобретённых билетов.
        should("test services") {
            // uncomment below to test the property
            //modelInstance.services shouldBe ("TODO")
        }

    }
}
