/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.GetCardResponseCard
import ru.metrosoft.sputnikgate.output.nbs.model.Card
import ru.metrosoft.sputnikgate.output.nbs.model.CardCarrierType
import ru.metrosoft.sputnikgate.output.nbs.model.CardKind

class GetCardResponseCardTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of GetCardResponseCard
        //val modelInstance = GetCardResponseCard()

        // to test the property `carrierType`
        should("test carrierType") {
            // uncomment below to test the property
            //modelInstance.carrierType shouldBe ("TODO")
        }

        // to test the property `uid` - UID Носителя в шестнадцатеричном представлении.
        should("test uid") {
            // uncomment below to test the property
            //modelInstance.uid shouldBe ("TODO")
        }

        // to test the property `num` - Печатный номер носителя.
        should("test num") {
            // uncomment below to test the property
            //modelInstance.num shouldBe ("TODO")
        }

        // to test the property `kind`
        should("test kind") {
            // uncomment below to test the property
            //modelInstance.kind shouldBe ("TODO")
        }

        // to test the property `status` - Статус носителя.
        should("test status") {
            // uncomment below to test the property
            //modelInstance.status shouldBe ("TODO")
        }

    }
}
