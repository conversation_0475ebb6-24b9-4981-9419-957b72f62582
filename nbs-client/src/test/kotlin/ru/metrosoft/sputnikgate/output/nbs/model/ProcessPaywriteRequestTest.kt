/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteRequest
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketPaymentParamDto
import ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteRequestService

class ProcessPaywriteRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of ProcessPaywriteRequest
        //val modelInstance = ProcessPaywriteRequest()

        // to test the property `service`
        should("test service") {
            // uncomment below to test the property
            //modelInstance.service shouldBe ("TODO")
        }

        // to test the property `agentParams` - Параметры платежа агента.
        should("test agentParams") {
            // uncomment below to test the property
            //modelInstance.agentParams shouldBe ("TODO")
        }

    }
}
