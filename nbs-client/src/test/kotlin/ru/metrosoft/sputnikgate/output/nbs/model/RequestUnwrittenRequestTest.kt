/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.RequestUnwrittenRequest
import ru.metrosoft.sputnikgate.output.nbs.model.AgentReadBlock

class RequestUnwrittenRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of RequestUnwrittenRequest
        //val modelInstance = RequestUnwrittenRequest()

        // to test the property `bitmap` - Считанные блоки.
        should("test bitmap") {
            // uncomment below to test the property
            //modelInstance.bitmap shouldBe ("TODO")
        }

    }
}
