/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.ProcessPaywriteService

class ProcessPaywriteServiceTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of ProcessPaywriteService
        //val modelInstance = ProcessPaywriteService()

        // to test the property `serviceId` - Код продукта (GoodCode).
        should("test serviceId") {
            // uncomment below to test the property
            //modelInstance.serviceId shouldBe ("TODO")
        }

        // to test the property `sum` - Сумма.
        should("test sum") {
            // uncomment below to test the property
            //modelInstance.sum shouldBe ("TODO")
        }

    }
}
