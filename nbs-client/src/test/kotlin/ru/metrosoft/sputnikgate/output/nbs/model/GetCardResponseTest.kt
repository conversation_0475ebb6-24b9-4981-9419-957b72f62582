/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.GetCardResponse
import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicket
import ru.metrosoft.sputnikgate.output.nbs.model.GetCardResponseCard

class GetCardResponseTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of GetCardResponse
        //val modelInstance = GetCardResponse()

        // to test the property `card`
        should("test card") {
            // uncomment below to test the property
            //modelInstance.card shouldBe ("TODO")
        }

        // to test the property `tickets` - Список билетов на носителе.
        should("test tickets") {
            // uncomment below to test the property
            //modelInstance.tickets shouldBe ("TODO")
        }

    }
}
