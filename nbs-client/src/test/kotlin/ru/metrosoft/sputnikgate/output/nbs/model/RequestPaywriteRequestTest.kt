/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.RequestPaywriteRequest
import ru.metrosoft.sputnikgate.output.nbs.model.AgentReadBlock

class RequestPaywriteRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of RequestPaywriteRequest
        //val modelInstance = RequestPaywriteRequest()

        // to test the property `bitmap` - Считанные блоки.
        should("test bitmap") {
            // uncomment below to test the property
            //modelInstance.bitmap shouldBe ("TODO")
        }

    }
}
