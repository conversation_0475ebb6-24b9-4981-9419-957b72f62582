/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.AgentTicketAvailableService

class AgentTicketAvailableServiceTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of AgentTicketAvailableService
        //val modelInstance = AgentTicketAvailableService()

        // to test the property `id` - Идентификатор.
        should("test id") {
            // uncomment below to test the property
            //modelInstance.id shouldBe ("TODO")
        }

        // to test the property `name` - Название.
        should("test name") {
            // uncomment below to test the property
            //modelInstance.name shouldBe ("TODO")
        }

        // to test the property `priceMin` - Стоимость услуги, нижняя граница.
        should("test priceMin") {
            // uncomment below to test the property
            //modelInstance.priceMin shouldBe ("TODO")
        }

        // to test the property `priceMax` - Стоимость услуги, верхняя граница.
        should("test priceMax") {
            // uncomment below to test the property
            //modelInstance.priceMax shouldBe ("TODO")
        }

        // to test the property `expirationPeriod` - Срок действия в днях.
        should("test expirationPeriod") {
            // uncomment below to test the property
            //modelInstance.expirationPeriod shouldBe ("TODO")
        }

        // to test the property `expirationType` - Тип срока действия.
        should("test expirationType") {
            // uncomment below to test the property
            //modelInstance.expirationType shouldBe ("TODO")
        }

        // to test the property `lifetimeExceeded` - Признак, что срок действия услуги выходит за срок действия карты.
        should("test lifetimeExceeded") {
            // uncomment below to test the property
            //modelInstance.lifetimeExceeded shouldBe ("TODO")
        }

        // to test the property `remainedDays` - Кол-во оставшихся (действующих) дней
        should("test remainedDays") {
            // uncomment below to test the property
            //modelInstance.remainedDays shouldBe ("TODO")
        }

    }
}
