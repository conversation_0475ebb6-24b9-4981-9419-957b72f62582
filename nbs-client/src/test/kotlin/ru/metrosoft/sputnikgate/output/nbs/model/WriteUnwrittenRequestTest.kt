/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.WriteUnwrittenRequest

class WriteUnwrittenRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of WriteUnwrittenRequest
        //val modelInstance = WriteUnwrittenRequest()

        // to test the property `clientSessionId` - Идентификатор сессии, использованный в запросе read.
        should("test clientSessionId") {
            // uncomment below to test the property
            //modelInstance.clientSessionId shouldBe ("TODO")
        }

        // to test the property `state` - Номер состояния запроса.
        should("test state") {
            // uncomment below to test the property
            //modelInstance.state shouldBe ("TODO")
        }

        // to test the property `cardResponse` - Ответ носителя на команду, отправленную ранее.
        should("test cardResponse") {
            // uncomment below to test the property
            //modelInstance.cardResponse shouldBe ("TODO")
        }

    }
}
