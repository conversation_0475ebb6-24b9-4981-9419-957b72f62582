/**
 *
 * Please note:
 * This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * Do not edit this file manually.
 *
 */

@file:Suppress(
    "ArrayInDataClass",
    "EnumEntryName",
    "RemoveRedundantQualifierName",
    "UnusedImport"
)

package ru.metrosoft.sputnikgate.output.nbs.model

import io.kotlintest.shouldBe
import io.kotlintest.specs.ShouldSpec

import ru.metrosoft.sputnikgate.output.nbs.model.CancelPayWriteRequest

class CancelPayWriteRequestTest : ShouldSpec() {
    init {
        // uncomment below to create an instance of CancelPayWriteRequest
        //val modelInstance = CancelPayWriteRequest()

        // to test the property `clientSessionId` - Идентификатор сессии. 16 байт в шестнадцатиричном формате.
        should("test clientSessionId") {
            // uncomment below to test the property
            //modelInstance.clientSessionId shouldBe ("TODO")
        }

    }
}
