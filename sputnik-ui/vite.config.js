import { fileURLToPath, URL } from 'node:url'

import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

const env = loadEnv('', process.cwd(), '');

// https://vitejs.dev/config/
export default defineConfig({
  define: {
    'process.env.BASE_API_URL': JSON.stringify(env.BASE_API_URL),
    'process.env.KEYCLOAK_URL': JSON.stringify(env.KEYCLOAK_URL),
  },
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
      proxy: {
        '/api': {
          target: 'http://localhost:8082/',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    }
})
