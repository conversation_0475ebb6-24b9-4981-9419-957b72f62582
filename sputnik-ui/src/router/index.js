import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import AgentNbsView from '../views/AgentNbsView.vue'
import PaymentsView from '../views/PaymentsView.vue'
import PaymentListView from '../views/PaymentList.vue'
import AgentDetailView from '../views/AgentDetailView.vue'
import RegistryView from '../views/RegistryView.vue'
import DebtView from '../views/DebtView.vue'
import AgentNbsDetailView from "@/views/AgentNbsDetailView.vue";

const routes = [
  {
    path: '/',
    name: 'agents',
    component: HomeView
  },
  {
    path: '/agents-nbs',
    name: 'agents-nbs',
    component: AgentNbsView
  },
  {
    path: '/agents-nbs/:agentId',
    name: 'agents-nbs-detail',
    component: AgentNbsDetailView
  },
  {
    path: '/agent/:agentId',
    name: 'agent-detail',
    component: AgentDetailView
  },
  {
    path: '/payments',
    name: 'payments',
    component: PaymentsView
  },
  {
    path: '/trxs',
    name: 'payment-list',
    component: PaymentListView
  },
  {
    path: '/debt',
    name: 'debt',
    component: DebtView
  },
  {
    path: '/regisrty',
    name: 'registry',
    component: RegistryView
  }
    
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
 