import axios from "axios";

export async function getAllAgentsNBS() {
    try {
        const { data }  = await axios.post(process.env.BASE_API_URL + '/agents-nbs/')

        return data;
    } catch (err) {
        console.log(err);
    }
}

export async function getAgentNBSById(agentNBSId) {
    try {
        const { data } = await  axios.get(process.env.BASE_API_URL + '/agents-nbs/' + agentNBSId)

        return data;
    } catch (err) {
        console.log(err);
        return null;
    }
}

export async function createAgentNBS(agentNBSData) {
    try {
        const { data } = await axios.post(process.env.BASE_API_URL + '/agents-nbs/newAgent', agentNBSData)

        return data;
    } catch (err) {
        console.log(err);
        return err;
    }
}