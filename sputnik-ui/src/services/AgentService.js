import axios from "axios";

export async function getAllAgents() {
    try {
        const { data } = await axios.post(process.env.BASE_API_URL + '/agent/')

        return data;
    } catch (err) {
        console.log(err);
        return null
    }
}

export async function getAgentById(id) {
    try {
     const { data } = await axios.get(process.env.BASE_API_URL + '/agent/' + id);

     return data;
    } catch (err) {
        console.log(err);
        return null;
    }
}

export async function createNewAgent(agentData) {
    try {
        const { data } = await axios.post(process.env.BASE_API_URL + '/agent/newAgent', agentData);

        return data
    } catch (err) {
        console.log(err);
        return err
    }
}

export async function getAgentContractsByAgentId(id) {
    try {
        const { data } = await axios.post(process.env.BASE_API_URL + '/agent/' + id + '/contract/');

        return data;
    } catch (err) {
        console.log(err);
        return null;
    }
}

export async function getAgentPaymentDataById(id, { sort, filters, page, size }) {
    try {
        const { data } = await axios.post(process.env.BASE_API_URL + '/agent/' + id + '/payments/', {
            sort,
            filters,
            page,
            size
        });

        return data;
    } catch (err) {
        console.log(err);
        return null;
    }
}

export async function getAgentRegistriesDataById(id, { sort, filters, page, size }) {
    try {
        const { data } = await axios.post(process.env.BASE_API_URL + '/agent/' + id + '/registries/', {
            sort,
            filters,
            page,
            size
        });

        return data;
    } catch (err) {
        console.log(err);
        return null;
    }
}

export async function createNewContractForAgentId(agentId, contractData) {
    try {
        const { data } =  await axios.post(process.env.BASE_API_URL + '/agent/' + agentId + '/contract/newContract', contractData)

        return data;
    } catch (err) {
        console.log(err);
        return null;
    }
}