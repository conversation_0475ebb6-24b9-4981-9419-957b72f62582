import axios from "axios";

export async function generateRegistry(registryData) {
  try {
    const {data} = await axios.post(process.env.BASE_API_URL + '/registry/newRegistry', registryData)

    return data;
  } catch (err) {
    console.log(err)
    return err;
  }
}

export async function searchRegistry({sort, filters, page, size}) {
  try {
    const {data} = await axios.post(process.env.BASE_API_URL + '/registry/', {
      sort,
      filters,
      page,
      size
    })

    return data;
  } catch (err) {
    console.log(err);
    return err;
  }
}

export async function downloadRegistry(registryId) {
  try {
    const response = await axios.get(process.env.BASE_API_URL + '/registry/' + registryId, {
      responseType: 'blob'
    })

    // Создаем URL для скачивания файла
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;

    // Получаем имя файла из заголовков ответа
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'registry.csv';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename=(.+)/);
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/"/g, '');
      }
    }

    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    return true;
  } catch (err) {
    console.log(err);
    return err;
  }
}

export async function downloadRegistryDiff(registryId) {
  try {
    const response = await axios.get(process.env.BASE_API_URL + '/registry/diff/' + registryId, {
      responseType: 'blob'
    })

    // Создаем URL для скачивания файла
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;

    // Получаем имя файла из заголовков ответа
    const contentDisposition = response.headers['content-disposition'];
    let filename = 'registry_diff.csv';
    if (contentDisposition) {
      const filenameMatch = contentDisposition.match(/filename=(.+)/);
      if (filenameMatch) {
        filename = filenameMatch[1].replace(/"/g, '');
      }
    }

    link.setAttribute('download', filename);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

    return true;
  } catch (err) {
    console.log(err);
    return err;
  }
}