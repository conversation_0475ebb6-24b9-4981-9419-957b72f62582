import { reactive } from 'vue'
import { keycloak } from '@/plugins/keycloak'

const state = reactive({
  user: {
    agentId: null,
    isAdmin: false
  }
})

const updateUserInfo = () => {
  if (keycloak.tokenParsed) {
    const roles = keycloak.tokenParsed.realm_access?.roles || []
    state.user = {
      agentId: keycloak.tokenParsed.sub,
      isAdmin: roles.includes('ADMINISTRATOR')
    }
    console.log('Current user info:', state.user)
  } else {
    state.user = {
      agentId: null,
      isAdmin: false
    }
  }
}

export default {
  state,
  updateUserInfo
} 