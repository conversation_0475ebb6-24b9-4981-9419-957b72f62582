<template>
  <el-menu
    @open="handleOpen"
    @close="handleClose"
    default-active="Агенты"
    router=true
    class="el-menu-vertical-demo"
  >
    <template v-if="isAdmin">
      <el-menu-item 
        v-for="i in adminRoutes"
        :route='{ name: i.route }'
        :index="i.to"
      >
        {{ i.text }}
      </el-menu-item>
    </template>
    <template v-else>
      <el-menu-item 
        v-for="i in agentRoutes"
        :route='{ name: i.route }'
        :index="i.to"
      >
        {{ i.text }}
      </el-menu-item>
    </template>
  </el-menu>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { keycloak } from '@/plugins/keycloak'

const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

const isAdmin = computed(() => {
  const roles = keycloak.tokenParsed?.realm_access?.roles || []
  return roles.includes('ADMINISTRATOR')
})

const adminRoutes = [
  { to: "/", text: "Агенты", route: 'agents' },
  { to: "/agents-nbs", text: "Агенты НБС", route: 'agents-nbs' },
  { to: "/trxs", text: "Платежи", route: 'payment-list' },
  { to: "/payments", text: "API", route: 'payments' },
  { to: "/registry", text: "Реестры НБС", route: 'registry' },
  { to: "/debt", text: "Финансы", route: 'debt' }
]

const agentRoutes = [
  { to: "/", text: "Агенты", route: 'agents' },
  { to: "/trxs", text: "Платежи", route: 'payment-list' }
]
</script>