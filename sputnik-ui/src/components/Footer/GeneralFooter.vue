<template lang="">
  <div
    class="GeneralFooter"
    :class="types[type]"
    v-bind="$attrs"
  >
    <div class="Details">

      <div
        class="detailBlock"
        style=""
      >
        <div class="detailItem">
          <h5>Общие вопросы: </h5>
          <p><EMAIL></p>
        </div>
        <div class="detailItem">
          <h5>Техническая поддержка:</h5>
          <p><EMAIL></p>
        </div>
      </div>
    </div>
    <div class="blocksDivederWrapper">
      <hr>
    </div>
    <div class="Contacts">
      <div class="ContactItemsWrapper">
        <div class="ContactItem">
          <span>ООО «СберТройка»</span>
          <hr>
          <a
            class="link"
            href=""
          >sbertroika.ru</a>
        </div>
      </div>
    </div>
  </div>
</template>
<script>


export default {
  name:"GeneralFooter",
  components:{
        
  },
  props:{
    type:{default:1}
  },
  data() {
    return {
      types:{
      }
    }
  }
}
</script>
