
<template>
  <div class="common-layout">
    <el-container>
      
      <el-container>
        <el-aside width="200px"><SidePanel /></el-aside>
        <el-main><router-view /></el-main>
        
      </el-container>
      <el-footer>
          <!-- <GeneralFooter /> -->
        </el-footer>
    </el-container>
  </div>
</template>

<script>
import SidePanel from "./components/menu/SidePanel.vue";
import GeneralFooter from '@/components/Footer/GeneralFooter.vue'

export default {
  components: {
    SidePanel,
    GeneralFooter
  },
};
</script>

<style lang="scss">
#app {
  font-family: "Moscow Sans";
}

@font-face {
  font-family: "Moscow Sans";
  src: url("./assets/fonts/moscow\ sans.ttf");
}

.common-layout {
}

</style>