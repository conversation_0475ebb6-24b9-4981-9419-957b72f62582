<template>
  <el-container>
    <el-header>
      <h1>Управление агентами НБС</h1>
    </el-header>
    <el-main>
      <el-button type="primary" @click="dialogCreateFormVisible = true">Новый агент НБС</el-button>
      <el-divider/>
      <el-table :data="agents" style="width: 100%" max-height="500">
        <el-table-column fixed prop="id" label="Идентификатор" width="350"/>
        <el-table-column prop="name" label="Наименование"/>
        <el-table-column fixed="right" label="Операции" min-width="120">
          <template #default="scope">
            <router-link :to="('/agents-nbs/' + scope.row.id)">
              <el-button link type="primary" size="small">Детали</el-button>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
    </el-main>
  </el-container>

  <el-dialog v-model="dialogCreateFormVisible" title="Новый агент НБС" width="500">
    <el-form :model="form">
      <el-form-item label="Наименование" required>
        <el-input v-model="form.name" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Путь до API НБС" required>
        <el-input v-model="form.openApiBaseUrl" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="API-ключ" required>
        <el-input v-model="form.openApiKey" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Имя пользователя для сверки реестров" required>
        <el-input v-model="form.SFTPUsername" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Пароль пользователя для сверки реестров" required>
        <el-input v-model="form.SFTPPassword" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Адрес сервера для сверки реестров" required>
        <el-input v-model="form.SFTPUrl" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Порт сервера для сверки реестров" required>
        <el-input v-model="form.SFTPPort" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Префикс реестра">
         <el-input v-model="form.registryPrefix" autocomplete="off"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogCreateFormVisible = false">Отмена</el-button>
        <el-button type="primary" @click="onSubmit">
          Создать
        </el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script>
import {reactive, ref} from 'vue'
import {AgentNBSService} from '@/services';

export default {
  name: 'AgentNbsView',
  components: {},
  data() {
    return {
      agents: []
    }
  },
  setup() {
    const dialogCreateFormVisible = ref(false)

    const agents = reactive([])

    const form = reactive({
      name: '',
      openApiBaseUrl: '',
      openApiKey: '',
      SFTPUsername: '',
      SFTPPassword: '',
      SFTPUrl: '',
      SFTPPort: 0,
      registryPrefix: '',
    })

    const fetchAgents = async function () {
      try {
        const data = await AgentNBSService.getAllAgentsNBS()
        if (this && this.agents) {
          this.agents = data
        } else {
          agents.value = data
        }
      } catch (err) {
        console.log(err);
      }
    }

    const onSubmit = async function (a, b, c) {
      try {
        await AgentNBSService.createAgentNBS(form);

        form.text = ''
        dialogCreateFormVisible.value = false

        return fetchAgents()
      } catch (err) {
        return err;
      }
    }

    return {
      dialogCreateFormVisible,
      form,
      onSubmit,
      fetchAgents
    }

  },
  created() {
    this.fetchAgents()
  }
}
</script>