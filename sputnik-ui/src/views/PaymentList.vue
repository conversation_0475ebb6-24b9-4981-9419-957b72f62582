<template>
  <el-container>
    <el-header>
      <h1>Список платежей</h1>
    </el-header>
    <el-main>

      <el-button type="primary" disabled="true">
        Экспорт
      </el-button>


      <h1>Платежи</h1>
      <el-form :inline="true" :model="paymentFilter">
        <el-form-item label="Идентификатор платежа в НБС">
          <el-input v-model="paymentFilter.sessionId" placeholder="Идентификатор платежа в НБС" clearable/>
        </el-form-item>
        <el-form-item label="Агент">
          <el-select v-model="paymentFilter.agentId" style="width: 240px">
            <el-option
                v-for="item in agents"
                :key="item.id"
                :label="item.text"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Агент НБС">
          <el-select v-model="paymentFilter.agentNBSId" style="width: 240px">
            <el-option
                v-for="item in agentsNBS"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Дата и время (с)">
          <el-date-picker
              v-model="paymentFilter.ts_start"
              type="datetime"
              placeholder="Select date and time"
          />
        </el-form-item>
        <el-form-item label="Дата и время (по)">
          <el-date-picker
              v-model="paymentFilter.ts_end"
              type="datetime"
              placeholder="Select date and time"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmitPaymentFilter">Фильтрация</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="paymentsTable" :data="payments" :default-sort="{ prop: 'ts', order: 'descending' }"
                @sort-change="sortPayments" style="width: 100%" max-height="500">
        <el-table-column fixed prop="sessionId" label="Идентификатор платежа в НБС" width="350"/>
        <el-table-column fixed prop="agent" label="Агент" width="200"/>
        <el-table-column fixed prop="agentNBS" label="Агент НБС" width="200"/>
        <el-table-column fixed prop="type" label="Тип" width="220"/>
        <el-table-column fixed prop="status" label="Успех" width="100" :formatter="statusFormatter"/>
        <el-table-column fixed prop="ts" label="Дата и время платежа" sortable width="250"
                         :formatter="dateTimeFormatter"/>
        <el-table-column fixed prop="notifyTime" label="Дата и время записи" sortable width="250"
                         :formatter="notifyTimeFormatter"/>
        <el-table-column fixed prop="cardNum" label="Печатный номер носителя" width="200"/>
        <el-table-column fixed prop="amount" label="Сумма (руб)" width="150"/>
        <el-table-column fixed prop="paymentSuccess" label="Успех платежа" width="120" :formatter="payStatusFormatter"/>
        <el-table-column fixed prop="writeSuccess" label="Успех записи" width="120" :formatter="writeStatusFormatter"/>
        <el-table-column fixed prop="errorCode" label="Код ошибки"/>
        <el-table-column fixed prop="errorMessage" label="Сообщение об ошибке"/>
        <el-table-column fixed prop="traceId" label="ИД трейса"/>
      </el-table>
      <el-pagination ref="paymentTablePagger" layout="prev, pager, next" :page-size="paymentSize" :total="paymentTotal"
                     @current-change="handleCurrentChange"/>

    </el-main>
  </el-container>
</template>


<script>
import {AgentNBSService, AgentService, PaymentService} from "@/services";

export default {
  name: "PaymentListView",
  components: {},
  data() {
    return {
      agents: [],
      agentsNBS: [],
      payments: [],
      paymentPage: 1,
      paymentSize: 20,
      paymentTotal: 20,
      paymentFilter: {
        sessionId: '',
        agentId: '',
        ts_start: null,
        ts_end: null
      },
      loading: false,
    }
  },
  mounted() {
    this.fetchAgents()
    this.fetchAgentsNBS()
    this.fetchPayments()
  },
  methods: {

    fetchAgents: async function () {
      try {
        this.agents = await AgentService.getAllAgents()
      } catch (err) {
        console.log(err);
      }
    },
    fetchAgentsNBS: async function () {
      try {
        this.agentsNBS = await AgentNBSService.getAllAgentsNBS()
      } catch (err) {
        console.log(err);
      }
    },
    async fetchPayments() {
      try {
        this.loading = true;
        const {list, total} = await PaymentService.searchPayments({
          sort: this.sortPayments(),
          filter: this.paymentFilter,
          page: this.paymentPage - 1,
          size: this.paymentSize
        });
        this.payments = list;
        this.paymentTotal = total;
      } catch (error) {
        console.error('Error fetching payments:', error);
        this.$message.error('Ошибка при загрузке платежей. Пожалуйста, попробуйте позже.');
      } finally {
        this.loading = false;
      }
    },

    sortPayments() {
      if (!this.$refs.paymentsTable) {
        return [];
      }
      const { prop, order } = this.$refs.paymentsTable.sort;
      if (!prop || !order) {
        return [];
      }
      return [{
        property: prop,
        direction: order === 'ascending' ? 'ASC' : 'DESC'
      }];
    },

    handleSortChange() {
      this.fetchPayments();
    },

    handleFilterChange() {
      this.paymentPage = 1;
      this.fetchPayments();
    },

    handlePageChange(page) {
      this.paymentPage = page;
      this.fetchPayments();
    },

    handleSizeChange(size) {
      this.paymentSize = size;
      this.paymentPage = 1;
      this.fetchPayments();
    },

    onSubmitPaymentFilter: function (a, b, c) {
      this.fetchPayments()
    },

    handleCurrentChange: function (cp) {
      this.paymentPage = cp
      this.fetchPayments()
    },

    notifyTimeFormatter: (row, column) => {
      if (!row.notifyTime) {
        return ""
      }

      let d = new Date(row.notifyTime)
      return d.toLocaleDateString() + " " + d.toLocaleTimeString()
    },

    dateTimeFormatter: (row, column) => {
      let d = new Date(row.ts)
      return d.toLocaleDateString() + " " + d.toLocaleTimeString()
    },

    payStatusFormatter: (row, column) => {
      let s = row.paymentSuccess
      return s ? "Да" : "Нет"
    },
    writeStatusFormatter: (row, column) => {
      let s = row.writeSuccess
      return s ? "Да" : "Нет"
    },
    statusFormatter: (row, column) => {
      let s = row.status
      return s ? "Успех" : "Ошибка"
    }

  },

}

</script>