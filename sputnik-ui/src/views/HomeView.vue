<template>
  <el-container>
    <el-header>
      <h1>Управление агентами</h1>
    </el-header>
    <el-main>
      <el-button type="primary" @click="dialogCreateFormVisible = true">Новый агент</el-button>
      <el-divider/>
      <el-table :data="agents" style="width: 100%" max-height="500">
        <el-table-column fixed prop="id" label="Идентификатор" width="350"/>
        <el-table-column prop="text" label="Описание"/>
        <el-table-column fixed="right" label="Операции" min-width="120">
          <template #default="scope">
            <router-link :to="('/agent/' + scope.row.id)">
              <el-button link type="primary" size="small">Детали</el-button>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
    </el-main>
  </el-container>

  <el-dialog v-model="dialogCreateFormVisible" title="Новый агент" width="500">
    <el-form :model="form">
      <el-form-item label="Наименование">
        <el-input v-model="form.text" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Агент НБС">
        <el-select v-model="form.agentNbsId" placeholder="Выберите Агента НБС">
          <template v-for="an in agentsNbs" >
            <el-option :label="an.name" :value="an.id" />
          </template>
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogCreateFormVisible = false">Отмена</el-button>
        <el-button type="primary" @click="onSubmit">
          Создать
        </el-button>
      </div>
    </template>
  </el-dialog>

</template>

<script>
import {reactive, ref} from 'vue'
import {AgentService, AgentNBSService} from "@/services";

export default {
  name: 'HomeView',
  components: {},
  data() {
    return {
      agents: [],
      agentsNbs: []
    }
  },
  setup() {

    const dialogCreateFormVisible = ref(false)

    const agents = reactive([])
    const agentsNbs = reactive([])

    const form = reactive({
      text: '',
      agentNbsId: ''
    })

    const fetchAgents = async function () {
      try {
        if (this && this.agents) {
          this.agents = await AgentService.getAllAgents()
        } else {
          agents.value = await AgentService.getAllAgents()
        }
      } catch (err) {
        console.log(err);
      }
    }

    const fetchAgentsNbs = async function () {
          try {
            if (this && this.agentsNbs) {
              this.agentsNbs = await AgentNBSService.getAllAgentsNBS()
            } else {
              agentsNbs.value = await AgentNBSService.getAllAgentsNBS()
            }
          } catch (err) {
            console.log(err);
          }
        }

    const onSubmit = async function (a, b, c) {
      try {
        await AgentService.createNewAgent(form);

        form.text = ''
        dialogCreateFormVisible.value = false
        return fetchAgents()
      } catch (err) {
        console.log(err);
        return err
      }
    }

    return {

      dialogCreateFormVisible,
      form,
      onSubmit,
      fetchAgents,
      fetchAgentsNbs
    }

  },
  created() {
    this.fetchAgents()
    this.fetchAgentsNbs()
  }
}
</script>