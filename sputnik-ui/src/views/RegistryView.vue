<template>
  <el-container>
    <el-header>
      <h1>
        Реестры НБС
      </h1>
    </el-header>
    <el-main>
      <el-button type="primary" @click="dialogCreateContractVisible = true">
        Сформировать реестр
      </el-button>
      <el-divider/>
      <el-table ref="regTable" :data="registry" :default-sort="{ prop: 'date', order: 'descending' }"
                @sort-change="sortRegs" style="width: 100%">
        <el-table-column fixed prop="date" label="Дата реестра" :formatter="dateFormatter" width="150"/>
        <el-table-column fixed prop="type" label="Тип реестра" width="150"/>
        <el-table-column fixed prop="agentNBS" label="Агент НБС" width="150"/>
        <el-table-column fixed prop="name" label="Имя реестра" width="150"/>
        <el-table-column fixed prop="creatingDate" label="Дата/время формирование" :formatter="creatingDFormatter"
                         width="250"/>
        <el-table-column fixed prop="transferDate" label="Дата/время передачи в НБС" :formatter="tarnsferDFormatter"
                         width="250"/>
        <el-table-column fixed prop="processingDate" label="Дата/время получения ответа"
                         :formatter="processingDFormatter" width="250"/>
        <el-table-column fixed prop="status" label="Статус реестра" width="150" :formatter="statusFormatter"/>
        <el-table-column fixed="right" label="Операции" min-width="120">
          <template #default="scope">
            <el-button link type="primary" size="small" @click="downloadRegistry(scope.row.id)">
              Скачать реестр
            </el-button>
            <el-button link type="primary" size="small" @click="downloadRegistryDiff(scope.row.id)" v-if="scope.row.status === 'DIFF'">
              Скачать реестр расхождений
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination ref="regTablePagger" layout="prev, pager, next" :page-size="regSize" :total="regTotal"
                     @current-change="handleCurrentChange"/>

    </el-main>
  </el-container>

  <el-dialog v-model="dialogCreateContractVisible" title="Сформировать реестр" width="500">
    <el-form :model="newRegistry">
      <el-form-item label="Дата формирования">
        <el-date-picker
            v-model="newRegistry.date"
            type="date"
            placeholder="Pick a day"
        />
      </el-form-item>
      <el-form-item label="Тип">

        <el-select v-model="newRegistry.type" style="width: 240px">
          <el-option
              v-for="item in regTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="Агент НБС">

        <el-select v-model="newRegistry.agentNBSId" style="width: 240px">
          <el-option
                          v-for="item in agentsNBS"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                      />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogCreateContractVisible = false">Отмена</el-button>
        <el-button type="primary" @click="onRegistrySubmit">
          Создать
        </el-button>
      </div>
    </template>
  </el-dialog>


</template>

<script>
import {AgentNBSService, RegistryService} from "@/services";

export default {
  name: "RegistryView",
  components: {},
  data() {
    return {
      dialogCreateContractVisible: false,
      regTypes: [
        {value: 'SALEREG', label: 'Реестр продаж'},
        {value: 'WRITEREG', label: 'Реестр записей на носитель билетов'},
        {value: 'AGENT', label: 'Реестр платежей'},
      ],
      regPage: 1,
      regSize: 20,
      regTotal: 20,
      newRegistry: {
        date: '',
        type: '',
        agentNBSId: ''
      },
      registry: [],
      agentsNBS: []
    };
  },
  mounted() {
    this.fetchRegistry();
    this.fetchAgentsNBS();
  },
  methods: {
    onRegistrySubmit: async function () {
      try {
        await RegistryService.generateRegistry(this.newRegistry);

        this.dialogCreateContractVisible = false
        this.fetchRegistry();
      } catch (err) {
        console.log(err)
      }
    },

    fetchAgentsNBS: async function () {
      try {
        this.agentsNBS = await AgentNBSService.getAllAgentsNBS()
      } catch (err) {
        console.log(err);
      }
    },

    fetchRegistry: async function () {
      let regF = []

      if (this.paymentFilter) {
        if (this.paymentFilter.sessionId) {
          regF.push({field: 'sessionId', value: this.paymentFilter.sessionId})
        }
        if (this.paymentFilter.ts_start) {
          regF.push({field: 'ts_start', value: this.paymentFilter.ts_start})
        }
        if (this.paymentFilter.ts_end) {
          regF.push({field: 'ts_end', value: this.paymentFilter.ts_end})
        }
      }

      let regS = this.$refs.regTable.columns.filter(e => e.order != '').map(e => {
        return {field: e.property, direction: e.order}
      })


      try {
        const {total, list} = await RegistryService.searchRegistry({
          sort: regS,
          filters: regF,
          page: this.regPage,
          size: this.regSize
        })

        this.regTotal = total
        this.registry = list
      } catch (err) {
        console.log(err)
      }
    },

    sortRegs: function () {
      this.fetchRegistry()
    },

    handleCurrentChange: function (cp) {
      this.regPage = cp
      this.fetchRegistry()
    },

    dateFormatter: (row, column) => {
      let d = new Date(row.date)
      return d.toLocaleDateString()
    },

    creatingDFormatter: (row, column) => {
      let d = new Date(row.createdAt)
      return d.toLocaleDateString() + " " + d.toLocaleTimeString()
    },

    tarnsferDFormatter: (row, column) => {
      if (!row.transferedAt) {
        return ""
      }

      let d = new Date(row.transferedAt)
      return d.toLocaleDateString() + " " + d.toLocaleTimeString()
    },

    processingDFormatter: (row, column) => {
      if (!row.processedAt) {
        return ""
      }

      let d = new Date(row.processedAt)
      return d.toLocaleDateString() + " " + d.toLocaleTimeString()
    },

    statusFormatter: (row, column) => {
      switch (row.status) {
        case 'CREATED':
          return 'Создан'
        case 'TRANSFERED':
          return 'Передан'
        case 'PROCESSED':
          return 'Обработан'
        case 'DIFF':
          return 'Обработан с расхождениями'
      }
    },

    downloadRegistry: async function (registryId) {
      try {
        await RegistryService.downloadRegistry(registryId);
      } catch (err) {
        console.log('Ошибка при скачивании реестра:', err);
        // Можно добавить уведомление пользователю об ошибке
      }
    },

    downloadRegistryDiff: async function (registryId) {
      try {
        await RegistryService.downloadRegistryDiff(registryId);
      } catch (err) {
        console.log('Ошибка при скачивании реестра расхождений:', err);
        // Можно добавить уведомление пользователю об ошибке
      }
    },

  }

}

</script>
