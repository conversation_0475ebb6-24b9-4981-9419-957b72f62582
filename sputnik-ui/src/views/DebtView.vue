<template>
  <el-container>
    <el-header>
      <h1>
        Финансы
      </h1>
    </el-header>
    <el-main>
      <el-form :inline="true" :model="debtFilter">
        <el-form-item label="Агент">
          <el-select v-model="debtFilter.agentId" style="width: 240px">
            <el-option
                v-for="item in agents"
                :key="item.id"
                :label="item.text"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Агент НБС">
          <el-select v-model="debtFilter.agentNBSId" style="width: 240px">
            <el-option
                v-for="item in agentsNBS"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Дата (с)">
          <el-date-picker
              v-model="debtFilter.ts_start"
              type="date"
          />
        </el-form-item>
        <el-form-item label="Дата (по)">
          <el-date-picker
              v-model="debtFilter.ts_end"
              type="date"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmitDebtFilter">Фильтрация</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="debtTable" :data="registry" style="width: 100%">
        <el-table-column fixed prop="date" label="Дата расчета" :formatter="dateFormatter" width="150"/>
        <el-table-column fixed prop="agent" label="Агент" width="250"/>
        <el-table-column fixed prop="trxAmount" label="Транзакций всего" width="150"/>
        <el-table-column fixed prop="trxBalance" label="Транзакций на сумму, руб" width="250"/>
        <el-table-column fixed prop="paymentBalance" label="Вознаграждение за платежи, руб" width="250"/>
        <el-table-column prop="writeBalance" label="Вознаграждение за запись, руб" width="250"/>
      </el-table>
      <!-- <el-pagination ref="debtTablePagger" layout="prev, pager, next" :page-size="debtSize" :total="debtTotal" @current-change="handleCurrentChange"/> -->

    </el-main>
  </el-container>

</template>

<script>
import {AgentNBSService, AgentService, DebtService} from "@/services";

export default {
  name: "DebtView",
  components: {},
  data() {
    return {
      agents: [],
      agentsNBS: [],
      debtCurrentPage: 1,
      debtTotal: 20,
      debtSize: 20,
      debtFilter: {
        agentId: '',
        ts_start: null,
        ts_end: null
      },
      registry: []
    };
  },
  mounted() {
    this.fetchAgents();
    this.fetchAgentsNBS();
    this.fetchDebts();
  },
  methods: {
    onSubmitDebtFilter: function () {
      this.fetchDebts();
    },

    fetchAgents: async function () {
      try {
        this.agents = await AgentService.getAllAgents()
      } catch (err) {
        console.log(err);
      }
    },
    fetchAgentsNBS: async function () {
      try {
        this.agentsNBS = await AgentNBSService.getAllAgentsNBS()
      } catch (err) {
        console.log(err);
      }
    },

    fetchDebts: async function () {
      let debtF = []

      if (this.debtFilter) {
        if (this.debtFilter.agentId) {
          debtF.push({field: 'agentId', value: this.debtFilter.agentId})
        }
        if (this.debtFilter) {
          debtF.push({field: 'agentNBSId', value: this.debtFilter.agentId})
        }
        if (this.debtFilter.ts_start) {
          debtF.push({field: 'ts_start', value: this.debtFilter.ts_start})
        }
        if (this.debtFilter.ts_end) {
          debtF.push({field: 'ts_end', value: this.debtFilter.ts_end})
        }
      }

      let debtS = this.$refs.debtTable.columns.filter(e => e.order != '').map(e => {
        return {field: e.property, direction: e.order}
      })


      try {
        const {list} = await DebtService.searchDebts({
          sort: debtS,
          filters: debtF,
          page: this.paymentPage,
          size: this.paymentSize
        })

        this.registry = list;
      } catch (err) {
        console.log(err);
      }
    },

    handleCurrentChange: function (cp) {
      this.debtCurrentPage = cp
      this.fetchDebts()
    },

    dateFormatter: (row, column) => {
      let d = new Date(row.date)
      return d.toLocaleDateString()
    }
  },

}

</script>
