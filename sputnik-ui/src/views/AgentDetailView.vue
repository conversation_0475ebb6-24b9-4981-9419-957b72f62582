<template>
  <el-container>
    <el-header>
      <h1>Агент {{ agent.text }}</h1>
    </el-header>
    <el-main>
      <!-- <router-link to="/">
          <el-button>Список агентов</el-button>
      </router-link> -->
      <el-button type="primary" @click="dialogCreateContractVisible = true">
        Добавить договор
      </el-button>
      <el-divider/>
      <h1>Общая информация</h1>
      <el-form :model="agent">
        <el-form-item label="Идентификатор">
          <el-input v-model="agent.id" readonly="true"/>
        </el-form-item>
        <el-form-item label="Наименование">
          <el-input v-model="agent.text" readonly="true"/>
        </el-form-item>
        <el-form-item label="Агент НБС">
          <el-select v-model="agent.agentNBSId" style="width: 240px">
            <el-option
                v-for="item in agentsNBS"
                :key="item.id"
                :label="item.name"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <el-divider/>

      <h1>Договоры</h1>
      <el-table :data="contracts" :default-sort="{ prop: 'startDate', order: 'descending' }"
                @sort-change="sortContracts" style="width: 100%" max-height="250">
        <el-table-column fixed prop="name" label="Наименование" width="150"/>
        <el-table-column fixed prop="startDate" label="Дата начала" sortable width="150" :formatter="dateSFormatter"/>
        <el-table-column fixed prop="endDate" label="Дата окончания" sortable width="150" :formatter="dateEFormatter"/>
        <el-table-column prop="paymentRate" label="Ставка за платеж, %" width="120"/>
        <el-table-column prop="writeRate" label="Ставка за запись, %" width="120"/>
        <!-- <el-table-column fixed="right" label="Operations" min-width="120">
        <template #default="scope">
            <el-button
            link
            type="primary"
            size="small"
            @click.prevent="deleteRow(scope.$index)"
            >
            Удалить
            </el-button>
        </template>
        </el-table-column> -->
      </el-table>

      <el-divider/>

      <h1>Платежи</h1>
      <el-form :inline="true" :model="paymentFilter">
        <el-form-item label="Идентификатор платежа в НБС">
          <el-input v-model="paymentFilter.sessionId" placeholder="Идентификатор платежа в НБС" clearable/>
        </el-form-item>
        <el-form-item label="Дата и время (с)">
          <el-date-picker
              v-model="paymentFilter.ts_start"
              type="datetime"
              placeholder="Select date and time"
          />
        </el-form-item>
        <el-form-item label="Дата и время (по)">
          <el-date-picker
              v-model="paymentFilter.ts_end"
              type="datetime"
              placeholder="Select date and time"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmitPaymentFilter">Фильтрация</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="paymentsTable" :data="payments" :default-sort="{ prop: 'ts', order: 'descending' }"
                @sort-change="sortPayments" style="width: 100%" max-height="500">
        <el-table-column fixed prop="sessionId" label="Идентификатор платежа в НБС" width="350"/>
        <el-table-column fixed prop="type" label="Тип" width="220"/>
        <el-table-column fixed prop="status" label="Успех" width="100" :formatter="statusFormatter"/>
        <el-table-column fixed prop="ts" label="Дата и время платежа" sortable width="250"
                         :formatter="dateTimeFormatter"/>
        <el-table-column fixed prop="notifyTime" label="Дата и время записи" sortable width="250"
                         :formatter="notifyTimeFormatter"/>
        <el-table-column fixed prop="cardNum" label="Печатный номер носителя" width="200"/>
        <el-table-column fixed prop="amount" label="Сумма (руб)" width="150"/>
        <el-table-column fixed prop="paymentSuccess" label="Успех платежа" width="120" :formatter="payStatusFormatter"/>
        <el-table-column fixed prop="writeSuccess" label="Успех записи" width="120" :formatter="writeStatusFormatter"/>
        <el-table-column fixed prop="errorCode" label="Код ошибки"/>
        <el-table-column fixed prop="errorMessage" label="Сообщение об ошибке"/>
        <el-table-column fixed prop="traceId" label="ИД трейса"/>
      </el-table>
      <el-pagination ref="paymentTablePagger" layout="prev, pager, next" :page-size="paymentSize" :total="paymentTotal"
                     @current-change="handleCurrentPaymentPageChange"/>

      <el-divider/>

      <h1>Реестры</h1>
      <el-form :inline="true" :model="registryFilter">
        <el-form-item label="Дата (с)">
          <el-date-picker
              v-model="registryFilter.ts_start"
              type="date"
              placeholder="Select date"
          />
        </el-form-item>
        <el-form-item label="Дата (по)">
          <el-date-picker
              v-model="registryFilter.ts_end"
              type="date"
              placeholder="Select date"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmitRegistryFilter">Фильтрация</el-button>
        </el-form-item>
      </el-form>
      <el-table ref="registryTable" :data="registries" :default-sort="{ prop: 'createdAt', order: 'descending' }"
                @sort-change="sortRegistries" style="width: 100%" max-height="500">
        <el-table-column fixed prop="status" label="Успех" width="100" :formatter="statusFormatter"/>
        <el-table-column fixed prop="createAt" label="Дата и время платежа" sortable width="250"
                         :formatter="dateTimeFormatter"/>
        <el-table-column fixed prop="fileName" label="Имя файла" width="200"/>
      </el-table>
      <el-pagination ref="registriesTablePagger" layout="prev, pager, next" :page-size="registriesSize" :total="registriesTotal"
                     @current-change="handleCurrentRegistriesPageChange"/>
    </el-main>
  </el-container>

  <el-dialog v-model="dialogCreateContractVisible" title="Новый договор" width="500">
    <el-form :model="newContract">
      <el-form-item label="Наименование">
        <el-input v-model="newContract.name" autocomplete="off"/>
      </el-form-item>
      <el-form-item label="Дата начала">
        <el-date-picker
            v-model="newContract.startDate"
            type="date"
            placeholder="Pick a day"
        />
      </el-form-item>
      <el-form-item label="Дата окончания">
        <el-date-picker
            v-model="newContract.endDate"
            type="date"
            placeholder="Pick a day"
        />
      </el-form-item>
      <el-form-item label="Ставка за платеж">
        <el-input-number v-model="newContract.paymentRate" :precision="2" :step="0.01" :max="100"/>
      </el-form-item>
      <el-form-item label="Ставка за запись">
        <el-input-number v-model="newContract.writeRate" :precision="2" :step="0.01" :max="100"/>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogCreateContractVisible = false">Отмена</el-button>
        <el-button type="primary" @click="onContractSubmit">
          Создать
        </el-button>
      </div>
    </template>
  </el-dialog>


</template>


<script>
import {useRoute} from 'vue-router'
import {AgentNBSService, AgentService} from "@/services";

export default {
  name: "AgentDetailView",
  components: {},
  data() {
    return {
      agentsNBS: [],
      dialogCreateContractVisible: false,
      agentId: '',
      editMode: false,
      agent: {},
      contracts: [],
      payments: [],
      registries: [],
      contractsSorting: [],
      paymentPage: 1,
      paymentSize: 20,
      paymentTotal: 20,
      registriesPage: 1,
      registriesSize: 20,
      registriesTotal: 20,
      paymentFilter: {
        sessionId: '',
        ts_start: null,
        ts_end: null
      },
      registryFilter: {
        ts_start: null,
        ts_end: null
      },
      newContract: {
        name: '',
        agentId: '',
        dateStart: '',
        dateEnd: '',
        paymentRate: 0.0,
        writeRate: 0.0
      }
    }
  },
  mounted() {
    this.agentId = useRoute().params.agentId
    this.fetchAgent(this.agentId)
    this.fetchAgentsNBS()
  },
  methods: {
    deleteRow: function (index) {
      console.log('Contract delete' + index)
    },

    fetchAgent: async function (id) {

      let paymentF = []

      if (this.paymentFilter) {
        if (this.paymentFilter.sessionId) {
          paymentF.push({field: 'sessionId', value: this.paymentFilter.sessionId})
        }
        if (this.paymentFilter.ts_start) {
          paymentF.push({field: 'ts_start', value: this.paymentFilter.ts_start})
        }
        if (this.paymentFilter.ts_end) {
          paymentF.push({field: 'ts_end', value: this.paymentFilter.ts_end})
        }
      }

      let paymentS = this.$refs.paymentsTable.columns.filter(e => e.order != '').map(e => {
        return {field: e.property, direction: e.order}
      })

      let registriesF = []
      if (this.registryFilter) {
        if (this.registryFilter.ts_start) {
          registriesF.push({field: 'ts_start', value: this.registryFilter.ts_start})
        }
        if (this.registryFilter.ts_end) {
          registriesF.push({field: 'ts_end', value: this.registryFilter.ts_end})
        }
      }

      let registriesS = this.$refs.registryTable.columns.filter(e => e.order != '').map(e => {
        return {field: e.property, direction: e.order}
      })


      this.agent = await AgentService.getAgentById(id);
      this.contracts = await AgentService.getAgentContractsByAgentId(id)

      const {listP, totalP} = await AgentService.getAgentPaymentDataById(id, {
        sort: paymentS,
        filters: paymentF,
        page: this.paymentPage,
        size: this.paymentSize
      })

      this.payments = listP;
      this.paymentTotal = totalP;

      const {listR, totalR} = await AgentService.getAgentRegistriesDataById(id, {
        sort: registriesS,
        filters: registriesF,
        page: this.registriesPage,
        size: this.registriesSize
      })

      this.registries = listR;
      this.registriesTotal = totalR;
    },

    fetchAgentsNBS: async function () {
      this.agentsNBS = await AgentNBSService.getAllAgentsNBS();
    },

    sortContracts: function (a, b, c) {
      console.log(a)
    },

    sortPayments: function (a, b, c) {
      this.fetchAgent(this.agentId)
    },

    sortRegistries: function (a, b, c) {
      this.fetchAgent(this.agentId)
    },

    onSubmitPaymentFilter: function (a, b, c) {
      this.fetchAgent(this.agentId)
    },

    onSubmitRegistryFilter: function (a, b, c) {
      this.fetchAgent(this.agentId)
    },

    handleCurrentPaymentPageChange: function (cp) {
      this.paymentPage = cp
      this.fetchAgent(this.agentId)
    },

    handleCurrentRegistriesPageChange: function (cp) {
      this.registriesPage = cp
      this.fetchAgent(this.agentId)
    },

    onContractSubmit: async function () {
      await AgentService.createNewContractForAgentId(this.agentId, this.newContract);

      this.dialogCreateContractVisible = false

      await this.fetchAgent(this.agentId)
    },

    dateSFormatter: (row, column) => {
      let d = new Date(row.startDate)
      return d.toLocaleDateString()
    },

    dateEFormatter: (row, column) => {
      if (!row.endDate) {
        return ""
      }

      let d = new Date(row.endDate)
      return d.toLocaleDateString()
    },

    notifyTimeFormatter: (row, column) => {
      if (!row.notifyTime) {
        return ""
      }

      let d = new Date(row.notifyTime)
      return d.toLocaleDateString() + " " + d.toLocaleTimeString()
    },

    dateTimeFormatter: (row, column) => {
      let d = new Date(row.ts)
      return d.toLocaleDateString() + " " + d.toLocaleTimeString()
    },

    payStatusFormatter: (row, column) => {
      let s = row.paymentSuccess
      return s ? "Да" : "Нет"
    },
    writeStatusFormatter: (row, column) => {
      let s = row.writeSuccess
      return s ? "Да" : "Нет"
    },
    statusFormatter: (row, column) => {
      let s = row.status
      return s ? "Успех" : "Ошибка"
    }

  },

}

</script>