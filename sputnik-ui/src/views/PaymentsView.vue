<template>
  <div class="container">
    <h2 class="text-center mt-5 mb-3">Посление 50 обращения по API</h2>
    <div class="card">
      <div class="card-body">

        <table class="table table-bordered">
          <thead>
          <tr>
            <th>Время</th>
            <th>Агент</th>
            <th>Запрос</th>
            <th>Статус</th>
            <th>Код ошибки</th>
            <th>Текст ошибки</th>
            <th>Трейс</th>
          </tr>
          </thead>
          <tbody>

          <tr v-for="req in reqs" :key="req.id">
            <td style="white-space: nowrap;padding: 4px;">{{ new Date(req.ts).toLocaleString() }}</td>
            <td style="white-space: nowrap;padding: 4px;">{{ req.agentId }}</td>
            <td style="white-space: nowrap;padding: 4px;">{{ req.agentNBSId }}</td>
            <td style="padding: 4px;">{{ req.type }}</td>
            <td style="padding: 4px;">{{ req.status }}</td>
            <td style="padding: 4px;">{{ req.errorCode }}</td>
            <td style="padding: 4px;">{{ req.errorMessage }}</td>
            <td style="padding: 4px;">{{ req.traceId }}</td>
          </tr>

          </tbody>
        </table>
      </div>
    </div>
  </div>

</template>

<script>
import axios from "axios";

export default {
  name: "PaymentsView",
  components: {},
  data() {
    return {
      reqs: []
    };
  },
  created() {
    this.fetchPayments();
  },
  methods: {
    fetchPayments() {
      // ???
      axios.get(process.env.BASE_API_URL + '/req/')
          .then(response => this.reqs = response.data)
          .catch(error => console.log(error))
    }
  },

}

</script>
