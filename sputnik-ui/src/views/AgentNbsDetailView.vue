<template>
  <el-container>
    <el-header>
      <h1>Агент НБС {{ agentNBS.name }}</h1>
    </el-header>
    <el-main>
      <el-button type="primary">
        Запрос на сертификат
      </el-button>
      <el-button type="primary">
        Загрузить сертификат
      </el-button>
      <el-divider/>
      <h1>Общая информация</h1>
      <el-form :model="agentNBS">
        <el-form-item label="Идентификатор">
          <el-input v-model="agentNBS.id" readonly="true"/>
        </el-form-item>
        <el-form-item label="Наименование">
          <el-input v-model="agentNBS.name" readonly="true"/>
        </el-form-item>
        <el-form-item label="Путь до API НБС">
           <el-input v-model="agentNBS.openApiBaseUrl" readonly="true"/>
        </el-form-item>
        <el-form-item label="API-ключ">
           <el-input v-model="agentNBS.openApiKey"  readonly="true"/>
        </el-form-item>
        <el-form-item label="Сертфиикат">
           <el-input v-model="agentNBS.openApiCertificate"  readonly="true"/>
        </el-form-item>
        <el-form-item label="Имя пользователя для сверки реестров">
           <el-input v-model="agentNBS.sftpusername"  readonly="true"/>
        </el-form-item>
        <el-form-item label="Пароль пользователя для сверки реестров">
            <el-input v-model="agentNBS.sftppassword"  readonly="true"/>
        </el-form-item>
        <el-form-item label="Адрес сервера для сверки реестров">
           <el-input v-model="agentNBS.sftpurl"  readonly="true"/>
        </el-form-item>
        <el-form-item label="Порт сервера для сверки реестров">
           <el-input v-model="agentNBS.sftpport"  readonly="true"/>
        </el-form-item>
        <el-form-item label="Префикс реестра">
           <el-input v-model="agentNBS.registryPrefix"  readonly="true"/>
        </el-form-item>
      </el-form>

      <el-divider/>

    </el-main>
  </el-container>

</template>


<script>

import {useRoute} from 'vue-router'
import {AgentNBSService} from '@/services';

export default {
  name: "AgentNbsDetailView",
  components: {},
  data() {
    return {
      agentNBS: {
        id: '',
        name: '',
        openApiBaseUrl: '',
        openApiKey: '',
        SFTPUsername: '',
        SFTPPassword: '',
        SFTPUrl: '',
        SFTPPort: 0,
        registryPrefix: '',
      },
      agentNBSId: '',
    }
  },
  mounted() {
    this.agentNBSId = useRoute().params.agentId
    this.fetchAgentNBS(this.agentNBSId)
  },
  methods: {
    fetchAgentNBS: async function (agentNBSId) {
      this.agentNBS = await AgentNBSService.getAgentNBSById(agentNBSId);
    },
  }
}

</script>