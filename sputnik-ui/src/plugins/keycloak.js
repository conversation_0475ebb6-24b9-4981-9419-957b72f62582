import Keycloak from 'keycloak-js'

const initOptions = {
    url: process.env.KEYCLOAK_URL, // Адрес Keycloak
    realm: 'sputnik-gateway', // Имя нашего realm в Keycloak
    clientId: 'sputnik-gateway', // Идентификатор клиента в Keycloak
    
    // Перенаправлять неавторизованных пользователей на страницу входа
    onLoad: 'login-required'
  }

const TOKEN_MIN_VALIDITY_SECONDS = 70

export const keycloak = new Keycloak(initOptions)

export async function updateToken() {
  await keycloak.updateToken(TOKEN_MIN_VALIDITY_SECONDS)
  return keycloak.token
}

export function login(onAuthenticatedCallback) {
  keycloak.init({ onLoad: 'login-required' }).then((auth) => {
    if (auth) {
      onAuthenticatedCallback()

      window.onfocus = () => {
        updateToken()
      }
    } else {
      window.location.reload()
    }
  })
}