{"name": "sputnik-ui", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"bootstrap": "^5.3.3", "cors": "^2.8.5", "keycloak-js": "^25.0.4", "vue": "^3.4.29", "vue-resource": "^1.5.3", "vue-router": "^4.4.3"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "axios": "^1.7.4", "element-plus": "^2.8.0", "sass-embedded": "^1.77.8", "vite": "^5.3.1", "vite-plugin-vue-devtools": "^7.3.1"}}