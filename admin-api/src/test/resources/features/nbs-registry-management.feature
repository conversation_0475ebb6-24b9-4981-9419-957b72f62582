# language: ru

Функция: Управление НБС и реестрами
  Как администратор системы
  Я хочу управлять конфигурациями НБС и реестрами
  Чтобы обеспечить корректную работу с внешними системами

  Предыстория:
    Дано я авторизован как администратор
    И в системе существуют конфигурации НБС
    И в системе существуют реестры

  Сценарий: Просмотр списка конфигураций НБС
    Когда я запрашиваю список всех конфигураций НБС
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит список конфигураций НБС
    И каждая конфигурация содержит поля:
      | поле               | тип    |
      | id                 | UUID   |
      | name               | строка |
      | openApiBaseUrl     | строка |
      | openApiKey         | строка |
      | SFTPUsername       | строка |
      | SFTPUrl            | строка |
      | SFTPPort           | число  |

  Сценарий: Создание новой конфигурации НБС
    Дано данные новой конфигурации НБС:
      | поле               | значение                    |
      | name               | Тестовая НБС                |
      | openApiBaseUrl     | https://test-nbs.example.com|
      | openApiKey         | test-api-key-123            |
      | SFTPUsername       | test-user                   |
      | SFTPPassword       | test-password               |
      | SFTPUrl            | sftp.test-nbs.example.com   |
      | SFTPPort           | 22                          |
    Когда я создаю новую конфигурацию НБС
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит данные созданной конфигурации
    И конфигурация сохраняется в базе данных
    И конфигурации присваивается уникальный ID

  Сценарий: Получение конкретной конфигурации НБС
    Дано существует конфигурация НБС с ID "550e8400-e29b-41d4-a716-446655440000"
    Когда я запрашиваю конфигурацию НБС по ID
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит полную информацию о конфигурации
    И данные соответствуют сохраненным в базе

  Сценарий: Ошибка при запросе несуществующей конфигурации НБС
    Дано конфигурация НБС с ID "00000000-0000-0000-0000-000000000000" не существует
    Когда я запрашиваю конфигурацию НБС по ID
    Тогда система возвращает ошибку с кодом 404
    И ответ содержит сообщение "Agent NBS not found"

  Сценарий: Создание нового реестра
    Дано существует конфигурация НБС с ID "550e8400-e29b-41d4-a716-446655440000"
    И данные нового реестра:
      | поле      | значение                             |
      | type      | SALEREG                              |
      | date      | 2024-01-15T00:00:00Z                 |
      | agentNBSId| 550e8400-e29b-41d4-a716-446655440000 |
    Когда я создаю новый реестр
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит данные созданного реестра
    И реестр сохраняется в базе данных
    И реестру присваивается уникальное имя файла

  Сценарий: Просмотр списка реестров
    Дано в системе существуют реестры разных типов
    Когда я запрашиваю список всех реестров
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит список реестров
    И каждый реестр содержит поля:
      | поле         | тип    |
      | id           | UUID   |
      | type         | строка |
      | name         | строка |
      | day          | дата   |
      | status       | строка |
      | createdAt    | дата   |
      | transferedAt | дата   |

  Сценарий: Фильтрация реестров по типу
    Дано в системе есть реестры типа "SALEREG" и "WRITETICKETSREG"
    И параметры фильтрации:
      | поле | значение |
      | type | SALEREG  |
    Когда я запрашиваю реестры с фильтрацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит только реестры типа "SALEREG"

  Сценарий: Фильтрация реестров по периоду
    Дано в системе есть реестры за разные даты
    И параметры фильтрации:
      | поле     | значение             |
      | ts_start | 2024-01-01T00:00:00Z |
      | ts_end   | 2024-01-31T23:59:59Z |
    Когда я запрашиваю реестры с фильтрацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит только реестры за указанный период

  Сценарий: Сортировка реестров по дате
    Дано в системе есть реестры с разными датами
    И параметры сортировки:
      | поле | направление |
      | day  | descending  |
    Когда я запрашиваю реестры с сортировкой
    Тогда система возвращает успешный ответ с кодом 200
    И реестры отсортированы по дате в убывающем порядке

  Сценарий: Пагинация списка реестров
    Дано в системе есть более 20 реестров
    И параметры пагинации:
      | поле | значение |
      | page | 1        |
      | size | 10       |
    Когда я запрашиваю реестры с пагинацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит 10 реестров
    И ответ содержит общее количество реестров

  Сценарий: Генерация имени файла реестра продаж с платежами записи
    Дано дата реестра "2024-01-15"
    И тип реестра "SALEREG"
    И это первый реестр данного типа за день
    И в системе есть платежи типа "PAYWRITE" за указанную дату
    Когда система генерирует имя файла реестра
    Тогда имя файла соответствует шаблону "salereg_FBT_20240115_01.csv"

  Сценарий: Генерация имени файла реестра продаж с удаленными платежами
    Дано дата реестра "2024-01-15"
    И тип реестра "SALEREG"
    И это первый реестр данного типа за день
    И в системе есть только платежи типа "PAY" за указанную дату
    Когда система генерирует имя файла реестра
    Тогда имя файла соответствует шаблону "salereg_DWB_20240115_01.csv"

  Сценарий: Генерация имени файла реестра продаж со смешанными типами платежей
    Дано дата реестра "2024-01-15"
    И тип реестра "SALEREG"
    И это первый реестр данного типа за день
    И в системе есть платежи типа "PAYWRITE" и "PAY" за указанную дату
    Когда система генерирует имя файла реестра
    Тогда имя файла соответствует шаблону "salereg_FBT_20240115_01.csv"

  Сценарий: Генерация имени файла реестра продаж без платежей
    Дано дата реестра "2024-01-15"
    И тип реестра "SALEREG"
    И это первый реестр данного типа за день
    И в системе нет платежей за указанную дату
    Когда система генерирует имя файла реестра
    Тогда имя файла соответствует шаблону "salereg_FBT_20240115_01.csv"

  Сценарий: Генерация имени файла реестра записи билетов
    Дано дата реестра "2024-01-15"
    И тип реестра "WRITETICKETSREG"
    И это второй реестр данного типа за день
    Когда система генерирует имя файла реестра
    Тогда имя файла соответствует шаблону "writeticketsreg_20240115_02.csv"

  Сценарий: Обновление статуса реестра
    Дано существует реестр со статусом "CREATED"
    Когда реестр успешно передается по SFTP
    Тогда статус реестра обновляется на "TRANSFERED"
    И устанавливается время передачи

  Сценарий: Просмотр содержимого реестра
    Дано существует реестр с ID "550e8400-e29b-41d4-a716-446655440000"
    И реестр содержит данные о продажах
    Когда я запрашиваю содержимое реестра
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит CSV данные реестра
    И данные соответствуют формату реестра продаж
