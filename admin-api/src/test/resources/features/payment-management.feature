# language: ru

Функция: Управление платежами и долгами
  Как администратор системы
  Я хочу просматривать и анализировать платежи агентов
  Чтобы контролировать финансовые операции

  Предыстория:
    Дано я авторизован как администратор
    И в системе существуют тестовые платежи
    И в системе существуют тестовые агенты

  Сценарий: Просмотр всех платежей в системе
    Когда я запрашиваю список всех платежей
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит список платежей
    И каждый платеж содержит поля:
      | поле      | тип    |
      | sessionId | UUID   |
      | ts        | дата   |
      | amount    | число  |
      | agent     | строка |
      | agentNBS  | строка |

  Сценарий: Фильтрация платежей по агенту
    Дано существует агент "Тестовый агент 1"
    И у агента есть платежи
    И параметры фильтрации:
      | поле    | значение                             |
      | agentId | 550e8400-e29b-41d4-a716-************ |
    Когда я запрашиваю платежи с фильтрацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит только платежи указанного агента
    И все платежи принадлежат агенту "Тестовый агент 1"

  Сценарий: Фильтрация платежей по периоду времени
    Дано в системе есть платежи за разные периоды
    И параметры фильтрации:
      | поле     | значение             |
      | ts_start | 2024-01-01T00:00:00Z |
      | ts_end   | 2024-01-31T23:59:59Z |
    Когда я запрашиваю платежи с фильтрацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит только платежи за указанный период
    И все платежи имеют дату в диапазоне от 2024-01-01 до 2024-01-31

  Сценарий: Сортировка платежей по времени
    Дано в системе есть платежи с разным временем создания
    И параметры сортировки:
      | поле      | направление |
      | ts        | descending  |
    Когда я запрашиваю платежи с сортировкой
    Тогда система возвращает успешный ответ с кодом 200
    И платежи отсортированы по времени в убывающем порядке
    И первый платеж имеет самую позднюю дату

  Сценарий: Пагинация списка платежей
    Дано в системе есть более 20 платежей
    И параметры пагинации:
      | поле | значение |
      | page | 2        |
      | size | 10       |
    Когда я запрашиваю платежи с пагинацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит 10 платежей
    И ответ содержит общее количество платежей
    И возвращаются платежи с 11 по 20

  Сценарий: Просмотр долгов агентов
    Дано в системе есть агенты с задолженностями
    Когда я запрашиваю информацию о долгах агентов
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит список долгов
    И каждый долг содержит поля:
      | поле           | тип    |
      | date           | дата   |
      | agent          | строка |
      | trxAmount      | число  |
      | paymentBalance | число  |
      | writeBalance   | число  |
      | trxBalance     | число  |

  Сценарий: Фильтрация долгов по агенту
    Дано существует агент с долгами
    И параметры фильтрации долгов:
      | поле    | значение                             |
      | agentId | 550e8400-e29b-41d4-a716-************ |
    Когда я запрашиваю долги с фильтрацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит только долги указанного агента

  Сценарий: Фильтрация долгов по периоду
    Дано в системе есть долги за разные периоды
    И параметры фильтрации долгов:
      | поле     | значение   |
      | ts_start | 2024-01-01 |
      | ts_end   | 2024-01-31 |
    Когда я запрашиваю долги с фильтрацией
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит только долги за указанный период

  Сценарий: Расчет баланса агента
    Дано агент с ID "550e8400-e29b-41d4-a716-************"
    И у агента есть платежи на сумму 10000 рублей
    И у агента есть записи на сумму 9500 рублей
    Когда я запрашиваю баланс агента
    Тогда система возвращает успешный ответ с кодом 200
    И баланс платежей равен 10000
    И баланс записей равен 9500
    И общий баланс равен 500

  Сценарий: Ограничение доступа для агента к чужим платежам
    Дано я авторизован как агент с ID "550e8400-e29b-41d4-a716-************"
    Когда я запрашиваю список всех платежей
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит только мои платежи
    И платежи других агентов не отображаются

  Сценарий: Поиск платежа по сессии
    Дано существует платеж с сессией "7461fc18-cdd6-44ba-b4fa-0c11b658ec99"
    И параметры поиска:
      | поле      | значение                             |
      | sessionId | 7461fc18-cdd6-44ba-b4fa-0c11b658ec99 |
    Когда я ищу платеж по сессии
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит единственный платеж
    И платеж имеет указанную сессию

  Сценарий: Обработка пустого результата поиска
    Дано платеж с сессией "00000000-0000-0000-0000-000000000000" не существует
    И параметры поиска:
      | поле      | значение                             |
      | sessionId | 00000000-0000-0000-0000-000000000000 |
    Когда я ищу платеж по сессии
    Тогда система возвращает успешный ответ с кодом 200
    И ответ содержит пустой список платежей
    И общее количество равно 0
