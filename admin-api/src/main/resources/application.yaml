spring:
  application:
    name: sputnik-admin-api

  r2dbc:
    url: r2dbc:pool:${DB_URL:postgresql://localhost:5432/sputnik}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: ${KEYCLOAK_REALM_URL:https://keycloak-dev.sbertroika.ru/realms/sputnik-gateway}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    ru.metrosoft.input: DEBUG
    ru.metrosoft.input.service: DEBUG
    ru.metrosoft.sputnikgate: DEBUG
    org.springframework.security: DEBUG
    org.springframework.web: DEBUG
    org.springframework.security.oauth2: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

server:
  forward-headers-strategy: framework
  port: ${PORT:8082}
