package ru.metrosoft.input

import kotlinx.coroutines.reactor.awaitSingle
import org.bouncycastle.asn1.x500.X500Name
import org.bouncycastle.cert.X509CertificateHolder
import org.bouncycastle.cert.jcajce.JcaX509CertificateConverter
import org.bouncycastle.cert.jcajce.JcaX509v3CertificateBuilder
import org.bouncycastle.jce.provider.BouncyCastleProvider
import org.bouncycastle.operator.jcajce.JcaContentSignerBuilder
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import ru.metrosoft.domainZone
import ru.metrosoft.input.model.*
import ru.metrosoft.input.service.SecurityService
import ru.metrosoft.model.AgentRegistry
import ru.metrosoft.service.AgentRegistryService
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate
import ru.metrosoft.sputnikgate.service.AgentCertificateService
import ru.metrosoft.sputnikgate.service.AgentContractService
import ru.metrosoft.sputnikgate.service.AgentPaymentService
import ru.metrosoft.sputnikgate.service.AgentService
import java.math.BigInteger
import java.security.KeyPairGenerator
import java.security.SecureRandom
import java.security.Security
import java.security.cert.X509Certificate
import java.time.Instant
import java.time.ZoneId
import java.time.temporal.ChronoUnit
import java.util.*

@RestController
@RequestMapping("/agent")
class AgentController(
    private val agentService: AgentService,
    private val agentContractService: AgentContractService,
    private val agentCertificateService: AgentCertificateService,
    private val agentPaymentService: AgentPaymentService,
    private val agentRegistryService: AgentRegistryService,
    private val securityService: SecurityService
) {
    private val logger = LoggerFactory.getLogger(AgentController::class.java)

    @GetMapping("/{id}")
    suspend fun getAgent(@PathVariable("id") id: UUID): Mono<ResponseEntity<AgentDto>> {
        return try {
            val currentAgentId = securityService.getCurrentAgentId().awaitSingle()
            if (currentAgentId != null && !securityService.isAdministrator().awaitSingle() && currentAgentId != id) {
                logger.warn("Access denied: agent {} trying to access agent {}", currentAgentId, id)
                return Mono.just(ResponseEntity.status(HttpStatus.FORBIDDEN).build())
            }

            Mono.just(agentService.getAgent(id))
                .map { agentDto(it) }
                .map { ResponseEntity.ok(it) }
                .defaultIfEmpty(ResponseEntity.notFound().build())
                .onErrorResume { e ->
                    logger.error("Error getting agent {}", id, e)
                    Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
                }
        } catch (e: Exception) {
            logger.error("Error processing request for agent {}", id, e)
            Mono.just(ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build())
        }
    }

    private fun agentDto(p1: Agent) = AgentDto(
        text = p1.name,
        to = p1.id.toString(),
        id = p1.id.toString(),
        agentNBSId = p1.agentNBSId,
        enabled = p1.enabled
    )

    @PostMapping("/")
    suspend fun getAgents(@RequestBody(required = false) search: PageSortDto?): Flux<AgentDto> {
        return try {
            val currentAgentId = securityService.getCurrentAgentId().awaitSingle()
            if (currentAgentId != null && !securityService.isAdministrator().awaitSingle()) {
                Flux.just(agentService.getAgent(currentAgentId)).map { agentDto(it) }
            } else {
                agentService.getAgents().map { agentDto(it) }
            }
        } catch (e: Exception) {
            logger.error("Error getting agents list", e)
            Flux.empty()
        }
    }

    @PostMapping("/newAgent")
    suspend fun saveAgent(@RequestBody agent: AgentDto): Mono<ResponseEntity<AgentDto>> {
//        if (!securityService.isAdministrator().awaitSingle()) {
//            logger.warn("Access denied: non-administrator trying to create agent")
//            return Mono.just(ResponseEntity.status(HttpStatus.FORBIDDEN).build())
//        }

        return Mono.just(
            agentService.saveAgent(
                Agent(
                    id = null,
                    name = agent.text,
                    agentNBSId = agent.agentNBSId,
                    enabled = true
                )
            )
        )
            .map(object : Function1<Agent, ResponseEntity<AgentDto>> {
                override fun invoke(p1: Agent): ResponseEntity<AgentDto> {
                    return ResponseEntity.ok(
                        agentDto(p1)
                    )
                }
            })
            .defaultIfEmpty(ResponseEntity.notFound().build())
    }

    @PostMapping("/{id}/contract/")
    suspend fun getContracts(@PathVariable("id") id: UUID, @RequestBody(required = false) search: PageSortDto?): Flux<AgentContractDto> {
        val currentAgentId = securityService.getCurrentAgentId().awaitSingle()
        if (currentAgentId != null && !securityService.isAdministrator().awaitSingle() && currentAgentId != id) {
            logger.warn("Access denied: agent {} trying to access contracts of agent {}", currentAgentId, id)
            return Flux.empty()
        }

        return agentContractService.getAllAgentContract(id).map { it.toUiModel() }
    }

    @PostMapping("/{id}/contract/newContract")
    suspend fun saveContract(@PathVariable("id") id: UUID, @RequestBody contract: AgentContractDto): Mono<ResponseEntity<AgentContractDto>> {
        if (!securityService.isAdministrator().awaitSingle()) {
            logger.warn("Access denied: non-administrator trying to create contract")
            return Mono.just(ResponseEntity.status(HttpStatus.FORBIDDEN).build())
        }

        val savedAgentContract = agentContractService.saveAgentContract(
            contract.toDomain(id)
        )
        return ResponseEntity.ok().body(savedAgentContract.toUiModel()).toMono()
    }

    @PostMapping("/{id}/certificate")
    suspend fun generateCertificate(@PathVariable("id") id: UUID): ResponseEntity<AgentCertificate> {
        if (!securityService.isAdministrator().awaitSingle()) {
            logger.warn("Access denied: non-administrator trying to generate certificate")
            return ResponseEntity.status(HttpStatus.FORBIDDEN).build()
        }

        val agentCertificate = agentCertificateService.getAgentCertificate(id)

        if (Security.getProvider("BC") == null) {
            Security.addProvider(BouncyCastleProvider())
        }

        val keyPairGen = KeyPairGenerator.getInstance("RSA", "BC")
        keyPairGen.initialize(2048, SecureRandom())
        val keyPair = keyPairGen.generateKeyPair()

        val issuer = X500Name("CN=My CA, O=My Organization, C=US")
        val subject = issuer
        val now = Date()
        val notBefore = now
        val notAfter = Date.from(Instant.now().plus(365, ChronoUnit.DAYS)) // 1 year validity
        val serial = BigInteger(64, SecureRandom())
        val certBuilder = JcaX509v3CertificateBuilder(
            issuer,
            serial,
            notBefore,
            notAfter,
            subject,
            keyPair.public
        )

        val contentSigner = JcaContentSignerBuilder("SHA256WithRSAEncryption")
            .setProvider("BC").build(keyPair.private)

        val holder: X509CertificateHolder = certBuilder.build(contentSigner)
        val cert: X509Certificate = JcaX509CertificateConverter()
            .setProvider("BC")
            .getCertificate(holder)

        val generatedCrtContent = certToPem(cert)
        agentCertificate.certificate = generatedCrtContent
        val savedAgentCertificate = agentCertificateService.saveAgentCertificate(agentCertificate)

        return ResponseEntity.ok(savedAgentCertificate)
    }

    /**
     * Convert an X509Certificate to PEM format string.
     */
    private fun certToPem(cert: X509Certificate): String {
        val base64Encoded = Base64.getEncoder().encodeToString(cert.encoded)
        return "-----BEGIN CERTIFICATE-----\n$base64Encoded\n-----END CERTIFICATE-----\n"
    }

    @PostMapping("/{id}/payments/")
    suspend fun getPayments(@PathVariable("id") id: UUID, @RequestBody(required = false) search: PageSortDto?): Mono<ResponseEntity<AgentPaymentsDto>> {
        return agentPaymentService.findAgentPayments(
            agentId = id,
            filter = AgentPaymentFilter(
                sessionId = search?.filters?.find { it.field == "sessionId" }?.value,
                tsStart = search?.filters?.find { it.field == "ts_start" }?.value,
                tsEnd = search?.filters?.find { it.field == "ts_end" }?.value,
                sort = search?.sort?.map {
                    Sortable(
                        it.field,
                        if (it.direction == "ascending") Sort.ASC else Sort.DESC
                    )
                }?.toList() ?: emptyList()
            ),
            pagination = search?.page?.let {
                Pagination(
                    page = it,
                    limit = search.size ?: 15,
                )
            } ?: Pagination(page = 0, limit = 15)
        ).let {
            ResponseEntity.ok().body(
                AgentPaymentsDto(
                    list = it.result.map(AgentPayment::toUiModel),
                    total = it.pagination.totalCount ?: 0
                )
            ).toMono()
        }
    }

    @PostMapping("/{id}/registries/")
    suspend fun getRegistry(@PathVariable("id") id: UUID, @RequestBody(required = false) search: PageSortDto?): Mono<ResponseEntity<AgentRegistriesDto>> {
        val currentAgentId = securityService.getCurrentAgentId().awaitSingle()
        if (currentAgentId != null && !securityService.isAdministrator().awaitSingle() && currentAgentId != id) {
            logger.warn("Access denied: agent {} trying to access registry of agent {}", currentAgentId, id)
            return Mono.just(ResponseEntity.status(HttpStatus.FORBIDDEN).build())
        }

        return agentRegistryService.findAgentRegistry(
            agentId = id,
            filter = AgentRegistryFilter(
                tsStart = search?.filters?.find { it.field == "ts_start" }?.value,
                tsEnd = search?.filters?.find { it.field == "ts_end" }?.value,
                sort = search?.sort?.map {
                    Sortable(
                        it.field,
                        if (it.direction == "ascending") Sort.ASC else Sort.DESC
                    )
                }?.toList() ?: emptyList()
            ),
            pagination = search?.page?.let {
                Pagination(
                    page = it,
                    limit = search.size ?: 15,
                )
            } ?: Pagination(page = 0, limit = 15)
        ).let {
            ResponseEntity.ok().body(
                AgentRegistriesDto(
                    list = it.result.map(AgentRegistry::toUiModel),
                    total = it.pagination.totalCount ?: 0
                )
            ).toMono()
        }
    }
}

private fun AgentContract.toUiModel() = AgentContractDto(
    name = this.number,
    startDate = this.startDate.atStartOfDay(ZoneId.of("UTC")),
    endDate = this.endDate?.atStartOfDay(ZoneId.of("UTC")),
    paymentRate = this.saleRate.toString(),
    writeRate = this.writeRate.toString()
)

private fun AgentContractDto.toDomain(agentId: UUID) = AgentContract(
    id = null,
    agentId = agentId,
    number = name,
    startDate = startDate.withZoneSameInstant(domainZone).toLocalDate(),
    endDate = endDate?.withZoneSameInstant(domainZone)?.toLocalDate(),
    saleRate = paymentRate.toDouble(),
    writeRate = writeRate.toDouble()
)

private fun AgentPayment.toUiModel() = AgentPaymentDto(
    sessionId = this._id,
    ts = this.ts.atZone(ZoneId.of("UTC")),
    amount = this.amount,
    agent = this.agentId.toString(),
    agentNBS = this.agentNBSId.toString(),
    paymentSuccess = this.payStatus,
    writeSuccess = this.writeStatus,
    type = if (this.type.equals(AgentPaymentType.WRITE)) "Запись удаленного" else "Прямое пополнение",
    status = this.status,
    cardNum = this.cardNum,
    statusCode = this.statusCode,
    notifyTime = this.notifyTime?.atZone(ZoneId.of("UTC")),
    traceId = this.traceId,
    errorCode = this.errorCode,
    errorMessage = this.errorMessage
)

private fun AgentRegistry.toUiModel() = AgentRegistryDto(
    id = this.id,
    agentId = this.agentId,
    fileName = this.fileName,
    createdAt = this.createdAt,
    status = this.status
)