package ru.metrosoft.input.service

import org.slf4j.LoggerFactory
import org.springframework.security.core.context.ReactiveSecurityContextHolder
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.stereotype.Service
import reactor.core.publisher.Mono
import ru.metrosoft.input.model.UserRole
import java.util.UUID

@Service
class SecurityService {
    private val logger = LoggerFactory.getLogger(SecurityService::class.java)

    fun getCurrentUserRoles(): Mono<List<UserRole>> {
        return ReactiveSecurityContextHolder.getContext()
            .map { context ->
                val roles = mutableListOf<UserRole>()
                
                if (context.authentication?.principal is Jwt) {
                    val jwt = context.authentication?.principal as Jwt
                    println("=== JWT TOKEN DEBUG (ROLES) ===")
                    println("Full JWT token: ${jwt.tokenValue}")
                    println("JWT claims: ${jwt.claims}")
                    println("JWT subject (sub): ${jwt.subject}")
                    println("JWT headers: ${jwt.headers}")
                    
                    // Проверяем роли в realm_access
                    val realmAccess = jwt.claims["realm_access"] as? Map<*, *>
                    println("Realm access: $realmAccess")
                    val realmRoles = realmAccess?.get("roles") as? List<*>
                    println("Realm roles: $realmRoles")
                    
                    realmRoles?.forEach { role ->
                        when (role.toString()) {
                            "ADMINISTRATOR" -> {
                                println("Found ADMINISTRATOR role in realm_access")
                                roles.add(UserRole.ADMINISTRATOR)
                            }
                            "AGENT" -> {
                                println("Found AGENT role in realm_access")
                                roles.add(UserRole.AGENT)
                            }
                        }
                    }
                    
                    // Проверяем роли в resource_access
                    val resourceAccess = jwt.claims["resource_access"] as? Map<*, *>
                    println("Resource access: $resourceAccess")
                    resourceAccess?.forEach { (client, value) ->
                        println("Checking roles for client: $client")
                        val clientRoles = (value as? Map<*, *>)?.get("roles") as? List<*>
                        println("Client roles for $client: $clientRoles")
                        clientRoles?.forEach { role ->
                            when (role.toString()) {
                                "ADMINISTRATOR" -> {
                                    println("Found ADMINISTRATOR role in resource_access for client $client")
                                    roles.add(UserRole.ADMINISTRATOR)
                                }
                                "AGENT" -> {
                                    println("Found AGENT role in resource_access for client $client")
                                    roles.add(UserRole.AGENT)
                                }
                            }
                        }
                    }
                    
                    // Проверяем все claims на наличие ролей
                    jwt.claims.forEach { (key, value) ->
                        println("Checking claim $key: $value")
                        when (value) {
                            is List<*> -> {
                                value.forEach { role ->
                                    when (role.toString()) {
                                        "ADMINISTRATOR" -> {
                                            println("Found ADMINISTRATOR role in list claim $key")
                                            roles.add(UserRole.ADMINISTRATOR)
                                        }
                                        "AGENT" -> {
                                            println("Found AGENT role in list claim $key")
                                            roles.add(UserRole.AGENT)
                                        }
                                    }
                                }
                            }
                            is Map<*, *> -> {
                                value.forEach { (k, v) ->
                                    println("Checking map claim $key: $k = $v")
                                    when (v.toString()) {
                                        "ADMINISTRATOR" -> {
                                            println("Found ADMINISTRATOR role in map claim $key")
                                            roles.add(UserRole.ADMINISTRATOR)
                                        }
                                        "AGENT" -> {
                                            println("Found AGENT role in map claim $key")
                                            roles.add(UserRole.AGENT)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    println("=== END JWT TOKEN DEBUG (ROLES) ===")
                } else {
                    println("No JWT token found in authentication context")
                }
                
                println("Final roles after processing: $roles")
                roles.toList()
            }
            .onErrorResume { e ->
                println("Error getting user roles: ${e.message}")
                e.printStackTrace()
                Mono.just(emptyList<UserRole>())
            }
    }

    fun getCurrentAgentId(): Mono<UUID?> {
        return ReactiveSecurityContextHolder.getContext()
            .map { context ->
                val principal = context.authentication?.principal
                println("Processing principal for agent ID: $principal")
                
                if (principal is Jwt) {
                    val jwt = principal
                    println("=== JWT TOKEN DEBUG (AGENT ID) ===")
                    println("Full JWT token: ${jwt.tokenValue}")
                    println("JWT claims: ${jwt.claims}")
                    println("JWT subject (sub): ${jwt.subject}")
                    
                    // Проверяем все возможные варианты agentId в claims
                    println("Checking all possible agentId locations:")

                    // 1. Проверяем прямой claim agentId
                    val agentId = jwt.claims["agentId"] as? String
                    if (agentId != null) {
                        println("Found agentId claim: $agentId")
                        try {
                            return@map UUID.fromString(agentId).also {
                                println("Successfully parsed agentId from agentId claim: $it")
                            }
                        } catch (e: IllegalArgumentException) {
                            println("Invalid agentId format in agentId claim: $agentId")
                        }
                    }

                    // 2. Проверяем claim agent_id
                    val agent_id = jwt.claims["agent_id"] as? String
                    if (agent_id != null) {
                        println("Found agent_id claim: $agent_id")
                        try {
                            return@map UUID.fromString(agent_id).also {
                                println("Successfully parsed agentId from agent_id claim: $it")
                            }
                        } catch (e: IllegalArgumentException) {
                            println("Invalid agentId format in agent_id claim: $agent_id")
                        }
                    }
                    
                    // 3. Проверяем sub claim
                    val sub = jwt.subject
                    if (sub != null) {
                        println("Found sub claim: $sub")
                        try {
                            return@map UUID.fromString(sub).also {
                                println("Successfully parsed agentId from sub claim: $it")
                            }
                        } catch (e: IllegalArgumentException) {
                            println("Invalid agentId format in sub claim: $sub")
                        }
                    }
                    
                    // 4. Проверяем все claims на наличие UUID
                    println("Checking all claims for UUID values:")
                    jwt.claims.forEach { (key, value) ->
                        println("Checking claim $key: $value")
                        if (value is String) {
                            try {
                                val uuid = UUID.fromString(value)
                                println("Found UUID in claim $key: $uuid")
                                return@map uuid
                            } catch (e: IllegalArgumentException) {
                                // Игнорируем не-UUID значения
                            }
                        }
                    }
                    
                    println("=== END JWT TOKEN DEBUG (AGENT ID) ===")
                } else {
                    println("No JWT token found in authentication context")
                }
                
                // Fallback на name из authentication
                context.authentication?.name?.let { 
                    try {
                        UUID.fromString(it).also {
                            println("Successfully parsed agent ID from authentication name: $it")
                        }
                    } catch (e: IllegalArgumentException) {
                        println("Invalid agent ID format in authentication name: $it")
                        null
                    }
                }
            }
            .onErrorResume { e ->
                println("Error getting agent ID: ${e.message}")
                e.printStackTrace()
                Mono.just(null)
            }
    }

    fun isAdministrator(): Mono<Boolean> {
        println("=== Checking if user is administrator ===")
        return getCurrentUserRoles()
            .map { roles -> 
                val isAdmin = roles.contains(UserRole.ADMINISTRATOR)
                println("User roles: $roles")
                println("Is administrator: $isAdmin")
                isAdmin
            }
            .onErrorResume { e ->
                println("Error checking administrator role: ${e.message}")
                e.printStackTrace()
                Mono.just(false)
            }
    }

    fun isAgent(): Mono<Boolean> {
        println("=== Checking if user is agent ===")
        return getCurrentUserRoles()
            .map { roles -> 
                val isAgent = roles.contains(UserRole.AGENT)
                println("User roles: $roles")
                println("Is agent: $isAgent")
                isAgent
            }
            .onErrorResume { e ->
                println("Error checking agent role: ${e.message}")
                e.printStackTrace()
                Mono.just(false)
            }
    }
} 