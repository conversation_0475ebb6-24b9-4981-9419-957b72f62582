package ru.metrosoft.input

import kotlinx.coroutines.reactor.awaitSingle
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.core.io.buffer.DataBuffer
import org.springframework.core.io.buffer.DataBufferUtils
import org.springframework.core.io.buffer.DefaultDataBufferFactory
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.http.server.reactive.ServerHttpResponse
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import ru.metrosoft.input.model.NBSRegistryDto
import ru.metrosoft.input.model.NbsRegistriesDto
import ru.metrosoft.input.model.NewRegistryRequest
import ru.metrosoft.input.model.PageSortDto
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.service.RegistryService
import java.io.FileInputStream
import java.nio.charset.Charset
import java.time.ZoneId
import kotlin.io.path.deleteIfExists
import ru.metrosoft.input.service.SecurityService
import ru.metrosoft.service.AgentNBSService

@RestController
@RequestMapping("/registry")
class NSBRegistryController(
    private val registryService: RegistryService,
    private val agentNBSService: AgentNBSService,
    private val securityService: SecurityService) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    @PostMapping("/newRegistry")
    suspend fun newRegistry(@RequestBody(required = true) req: NewRegistryRequest) : Mono<ResponseEntity<NBSRegistryDto>> {
        return ResponseEntity
            .ok()
            .body(
                registryService.newRegistry(
                    type = req.type,
                    date = req.date.toOffsetDateTime().atZoneSameInstant(
                        ZoneId.systemDefault()
                    ).plusHours(3).toLocalDate(),
                    agentNBSId = req.agentNBSId
                ).toUiModel(agentNBSService)
            ).toMono()
    }

    @GetMapping("/{registryId}")
    suspend fun getRegistry(@PathVariable("registryId") registryId: String,  response: ServerHttpResponse) : Mono<Void> {
        if (!securityService.isAdministrator().awaitSingle()) {
            logger.warn("Access denied: user is not an administrator")
            return Mono.empty()
        }
        kotlin.io.path.createTempFile().let { f ->
            registryService.byId(registryId).let { r ->
                f.toFile().printWriter().use { out ->
                    out.write(r.content)
                    out.flush()
                }

                try {
                    val input = FileInputStream(f.toAbsolutePath().toString())
                    val dataBufferFlux: Flux<DataBuffer> = DataBufferUtils.readByteChannel(input::getChannel,
                        DefaultDataBufferFactory(),4096)

                    response.headers.set(HttpHeaders.CONTENT_DISPOSITION,"attachment;filename=${r.name}");
                    response.headers.contentType = MediaType("application", "octet-stream", Charset.forName("UTF-8"))

                    return response.writeWith(dataBufferFlux)
                } catch ( e:Exception) {
                    e.printStackTrace()
                } finally {
                    f.deleteIfExists()
                }
            }
        }

        return  Mono.empty()
    }

    @GetMapping("/diff/{registryId}")
    suspend fun getDiffRegistry(@PathVariable("registryId") registryId: String,  response: ServerHttpResponse) : Mono<Void> {
        if (!securityService.isAdministrator().awaitSingle()) {
            logger.warn("Access denied: user is not an administrator")
            return Mono.empty()
        }
        val tempFile = kotlin.io.path.createTempFile()
        val registry = registryService.byId(registryId)
        tempFile.toFile().printWriter().use { out ->
            out.write(registry.diffContent)
            out.flush()
        }

        try {
            val input = FileInputStream(tempFile.toAbsolutePath().toString())
            val dataBufferFlux: Flux<DataBuffer> = DataBufferUtils.readByteChannel(input::getChannel,
                DefaultDataBufferFactory(),4096)

            response.headers.set(HttpHeaders.CONTENT_DISPOSITION,"attachment;filename=" + registry.name);
            response.headers.contentType = MediaType("application", "octet-stream", Charset.forName("UTF-8"))

            return response.writeWith(dataBufferFlux)
        } catch ( e:Exception) {
            e.printStackTrace()
        }
        return  Mono.empty()
    }

    @PostMapping("/")
    suspend fun getRequest(@RequestBody(required = false) search: PageSortDto?) : Mono<ResponseEntity<NbsRegistriesDto>> {
        return registryService.findRegistries(
            filter = RegistryFilter(
                sessionId = search?.filters?.find { it.field == "sessionId" }?.value,
                tsStart = search?.filters?.find { it.field == "ts_start" }?.value,
                tsEnd = search?.filters?.find { it.field == "ts_end" }?.value,
                sort = search?.sort?.map {
                    Sortable(
                        it.field,
                        if (it.direction == "ascending") Sort.ASC else Sort.DESC
                    )
                }?.toList() ?: emptyList()
            ),
            pagination = search?.page?.let {
                Pagination(
                    page = it,
                    limit = search.size ?: 15,
                )
            } ?: Pagination(page = 0, limit = 15)
        ).let {
            ResponseEntity.ok().body(
                NbsRegistriesDto(
                    list = it.result.map { registry -> registry.toUiModel(agentNBSService) },
                    total = it.pagination.totalCount ?: 0
                )
            ).toMono()
        }
    }

}

suspend fun Registry.toUiModel(agentNBSService: AgentNBSService) = NBSRegistryDto(
    id = id.toString(),
    type = type,
    name = name,
    status = status,
    date = date,
    createdAt = createdAt.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of("UTC")),
    transferedAt = transferedAt?.atZone(ZoneId.systemDefault())?.withZoneSameInstant(ZoneId.of("UTC")),
    processedAt = processedAt?.atZone(ZoneId.systemDefault())?.withZoneSameInstant(ZoneId.of("UTC")),
    agentNBS = agentNBSService.name(agentNBSId)
)