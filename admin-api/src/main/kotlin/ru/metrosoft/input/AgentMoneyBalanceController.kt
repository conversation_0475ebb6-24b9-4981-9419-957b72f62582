package ru.metrosoft.input

import kotlinx.coroutines.reactor.awaitSingle
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import ru.metrosoft.input.model.AgentDebtsDto
import ru.metrosoft.input.model.AgentMoneyBalanceDto
import ru.metrosoft.input.model.PageSortDto
import ru.metrosoft.input.service.SecurityService
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.service.AgentDebtService
import ru.metrosoft.sputnikgate.service.AgentService
import java.math.BigDecimal

@RestController
@RequestMapping("/debt")
class AgentMoneyBalanceController(
    private val agentDebtService: AgentDebtService,
    private val agentService: AgentService,
    private val securityService: SecurityService
) {
    private val logger: Logger = LoggerFactory.getLogger(this::class.java)

    @PostMapping("/")
    suspend fun getRequest(@RequestBody(required = false) search: PageSortDto?): Mono<ResponseEntity<AgentDebtsDto>> {
        val uuidAgentMap = agentService.getAgentList().associateBy({ it.id!!.toString() }, { it.name })

        return agentDebtService.calcDebts(
            filter = DebtFilter(
                agentId = search?.filters?.find { it.field == "agentId" }?.value,
                agentNBSId = search?.filters?.find { it.field == "agentNBSId" }?.value,
                tsStart = search?.filters?.find { it.field == "ts_start" }?.value,
                tsEnd = search?.filters?.find { it.field == "ts_end" }?.value,
                sort = search?.sort?.map {
                    Sortable(
                        it.field,
                        if (it.direction == "ascending") Sort.ASC else Sort.DESC
                    )
                }?.toList() ?: emptyList()
            ),
            pagination = search?.page?.let {
                Pagination(
                    page = it,
                    limit = search.size ?: 15,
                )
            } ?: Pagination(page = 0, limit = 15)
        ).let {
            ResponseEntity.ok().body(
                AgentDebtsDto(
                    list = it.result.map { it.toUiModel(uuidAgentMap) },
                    total = it.pagination.totalCount ?: 0
                )
            ).toMono()
        }
    }

//        return agentDebtService.findDebts()
//            .map {
//                AgentMoneyBalanceDto(
//                    date = it.date,
//                    agent = uuidAgentMap.getValue(it.agentId),
//                    trxAmount = it.count,
//                    paymentBalance = BigDecimal.valueOf(it.summPayment),
//                    writeBalance = BigDecimal.valueOf(it.summWrite),
//                    trxBalance = BigDecimal.valueOf(it.sumAll.toLong())
//                )
//            }.toFlux()
//    }
}

private fun AgentDebt.toUiModel(uuidAgentMap: Map<String, String>): AgentMoneyBalanceDto {
    return AgentMoneyBalanceDto(
        date = date,
        agent = uuidAgentMap.getValue(agentId),
        trxAmount = count,
        paymentBalance = BigDecimal.valueOf(summPayment),
        writeBalance = BigDecimal.valueOf(summWrite),
        trxBalance = BigDecimal.valueOf(sumAll.toLong())
    )
}
