package ru.metrosoft.input

import arrow.core.getOrNone
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import ru.metrosoft.input.model.AgentRequestDto
import ru.metrosoft.sputnikgate.model.Agent
import ru.metrosoft.sputnikgate.model.AgentRequest
import ru.metrosoft.sputnikgate.service.AgentRequestService
import ru.metrosoft.sputnikgate.service.AgentService
import java.time.ZoneId
import java.util.*

@RestController
@RequestMapping("/req")
class RequestController(private val agentRequestService: AgentRequestService, private val agentService: AgentService) {

    @GetMapping("/")
    suspend fun getRequest() : Flux<AgentRequestDto> {
        val uuidAgentMap = agentService.getAgentList().associateBy({ it.id!!.toString() }, { it.name })

        return agentRequestService.findAll().map { AgentRequestDto(
            uuidAgentMap.getOrElse(it.agentId) { it.agentId },
            it.ts.atZone(ZoneId.of("UTC")),
            it.type,
            it.status,
            it.errorCode,
            it.errorMessage,
            it.traceId
        ) }
    }

}