package ru.metrosoft.input

import kotlinx.coroutines.reactor.awaitSingle
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.metrosoft.input.model.AgentNBSDto
import ru.metrosoft.service.AgentNBSService
import ru.metrosoft.model.AgentNBS
import ru.metrosoft.model.NewAgentNBS
import java.util.UUID
import org.springframework.http.HttpStatus
import org.slf4j.LoggerFactory
import reactor.core.publisher.Flux
import ru.metrosoft.input.service.SecurityService

@RestController
@RequestMapping("/agents-nbs")
class AgentNBSController(
    private val agentNBSService: AgentNBSService,
    private val securityService: SecurityService
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @PostMapping("/")
    suspend fun getAllAgentNBS(): List<AgentNBSDto> {
        return agentNBSService.getAgentList().map { it.toDto() }
    }

    @GetMapping("/{id}")
    suspend fun getAgentNBS(@PathVariable("id") id: String): Mono<ResponseEntity<AgentNBSDto>> {
        return Mono.just(agentNBSService.getAgent(UUID.fromString(id)))
            .map(object : Function1<AgentNBS, ResponseEntity<AgentNBSDto>> {
                override fun invoke(p1: AgentNBS): ResponseEntity<AgentNBSDto> {
                    return ResponseEntity.ok(
                        p1.toDto()
                    )
                }
            })
            .defaultIfEmpty(ResponseEntity.notFound().build());
    }

    @PostMapping("/newAgent")
    suspend fun createAgentNBS(@RequestBody agentNBSDto: AgentNBSDto): ResponseEntity<AgentNBSDto> {
        val savedAgentNBS = agentNBSService.saveAgent(agentNBSDto.toNewDomain())
        return ResponseEntity.ok(savedAgentNBS.toDto())
    }
}

fun AgentNBS.toDto(): AgentNBSDto = AgentNBSDto(
    id = this.id.toString(),
    name = this.name,
    openApiBaseUrl = this.openApiBaseUrl,
    openApiKey = this.openApiKey,
    openApiCertificate = this.openApiCertificate,
    SFTPUsername = this.SFTPUsername,
    SFTPPassword = this.SFTPPassword,
    SFTPUrl = this.SFTPUrl,
    SFTPPort = this.SFTPPort,
    registryPrefix = this.registryPrefix
)

fun AgentNBSDto.toNewDomain(): NewAgentNBS = NewAgentNBS(
    name = this.name,
    openApiBaseUrl = this.openApiBaseUrl,
    openApiKey = this.openApiKey,
    openApiCertificate = this.openApiCertificate,
    SFTPUsername = this.SFTPUsername,
    SFTPPassword = this.SFTPPassword,
    SFTPUrl = this.SFTPUrl,
    SFTPPort = this.SFTPPort,
    registryPrefix = this.registryPrefix
)
