package ru.metrosoft.input

import kotlinx.coroutines.reactor.awaitSingle
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import reactor.kotlin.core.publisher.toMono
import ru.metrosoft.input.model.AgentPaymentDto
import ru.metrosoft.input.model.AgentPaymentsDto
import ru.metrosoft.input.model.PageSortDto
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.service.AgentPaymentService
import ru.metrosoft.sputnikgate.service.AgentService
import java.time.ZoneId
import java.util.*
import ru.metrosoft.input.service.SecurityService

@RestController
@RequestMapping("/payment")
class PaymentController(
    private val agentPaymentService: AgentPaymentService,
    private val agentService: AgentService,
    private val securityService: SecurityService
) {
    private val logger = LoggerFactory.getLogger(PaymentController::class.java)

    @PostMapping("/")
    suspend fun getPayments(@RequestBody(required = false) search: PageSortDto?): Mono<ResponseEntity<AgentPaymentsDto>> {
        val uuidAgentMap = agentService.getAgentList().associateBy({ it.id!!.toString() }, { it.name })
        
        // Получаем ID текущего агента
        val currentAgentId = securityService.getCurrentAgentId().awaitSingle()
        
        // Если это агент (не админ), фильтруем только его платежи
        val agentId = if (currentAgentId != null && !securityService.isAdministrator().awaitSingle()) {
            currentAgentId.toString()
        } else {
            search?.filters?.find { it.field == "agentId" }?.value
        }

        return agentPaymentService.findPayments(
            filter = AgentPaymentFilter(
                agentId = agentId,
                agentNBSId = search?.filters?.find { it.field == "agentNBSId" }?.value,
                sessionId = search?.filters?.find { it.field == "sessionId" }?.value,
                tsStart = search?.filters?.find { it.field == "ts_start" }?.value,
                tsEnd = search?.filters?.find { it.field == "ts_end" }?.value,
                sort = search?.sort?.map {
                    Sortable(
                        it.field,
                        if (it.direction == "ascending") Sort.ASC else Sort.DESC
                    )
                }?.toList() ?: emptyList()
            ),
            pagination = search?.page?.let {
                Pagination(
                    page = it,
                    limit = search.size ?: 15,
                )
            } ?: Pagination(page = 0, limit = 15)
        ).let {
            ResponseEntity.ok().body(
                AgentPaymentsDto(
                    list = it.result.map { payment -> payment.toUiModel(uuidAgentMap) },
                    total = it.pagination.totalCount ?: 0
                )
            ).toMono()
        }
    }
}

private fun AgentPayment.toUiModel(uuidAgentMap: Map<String, String>) = AgentPaymentDto(
    sessionId = this._id,
    ts = this.ts.atZone(ZoneId.of("UTC")),
    amount = this.amount,
    agent = uuidAgentMap.getValue(this.agentId.toString()),
    agentNBS = "",
    paymentSuccess = this.payStatus,
    writeSuccess = this.writeStatus,
    type = if (this.type.equals(AgentPaymentType.WRITE)) "Запись удаленного" else "Прямое пополнение",
    status = this.status,
    cardNum = this.cardNum,
    statusCode = this.statusCode,
    notifyTime = this.notifyTime?.atZone(ZoneId.of("UTC")),
    traceId = this.traceId,
    errorCode = this.errorCode,
    errorMessage = this.errorMessage
)
