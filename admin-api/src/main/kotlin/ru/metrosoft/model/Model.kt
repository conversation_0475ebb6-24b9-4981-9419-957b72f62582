package ru.metrosoft.model

import org.springframework.data.relational.core.mapping.Column
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.*

data class NewAgentNBS(
    val name: String,
    val openApiBaseUrl: String,
    val openApiKey: String,
    val openApiCertificate: String? = null,
    var SFTPUsername: String,
    var SFTPPassword: String,
    var SFTPUrl: String,
    var SFTPPort: Int,
    var registryPrefix: String
)

data class AgentNBS(
    val id: UUID,
    val name: String,
    val openApiBaseUrl: String,
    val openApiKey: String,
    val openApiCertificate: String? = null,
    var SFTPUsername: String,
    var SFTPPassword: String,
    var SFTPUrl: String,
    var SFTPPort: Int,
    var registryPrefix: String
)

data class AgentRegistry(
    val id: UUID,
    val agentId: UUID,
    val fileName: String,
    val createdAt: LocalDateTime,
    val status: String,
    val content: String,
    val diffContent: String
)