package ru.metrosoft.service

import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.springframework.data.domain.Sort
import org.springframework.data.r2dbc.core.R2dbcEntityOperations
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Service
import ru.metrosoft.input.model.AgentRegistryFilterResult
import ru.metrosoft.output.persistance.AgentResitryMapper
import ru.metrosoft.sputnikgate.model.AgentRegistryFilter
import ru.metrosoft.sputnikgate.model.Pagination
import java.util.*

@Service
class AgentRegistryService(
    private val entityTemplate: R2dbcEntityOperations,
    private val agentResitryMapper: AgentResitryMapper
) {
    suspend fun findAgentRegistry(agentId: UUID, filter: AgentRegistryFilter, pagination: Pagination): AgentRegistryFilterResult {
        val search = mutableListOf<Criteria>()
        search.add(Criteria.where("agentId").`is`(agentId))
        filter.tsStart?.let {
            search.add(Criteria.where("day").greaterThanOrEquals(it))
        }
        filter.tsEnd?.let {
            search.add(Criteria.where("day").lessThan(it))
        }

        val count = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.AgentRegistry::class.java)
                .matching(Query.query(Criteria.from(search)))
                .count().block()
        }?.toInt()!!

        val sort: Sort = if (filter.sort.isEmpty()) Sort.by("createdAt").descending() else {
            filter.sort.first().let {
                if (it.sort == ru.metrosoft.sputnikgate.model.Sort.ASC) Sort.by(it.field).ascending()
                else Sort.by(it.field).descending()
            }
        }

        val res = withContext(Dispatchers.IO) {
            entityTemplate.select(ru.metrosoft.sputnikgate.output.persistance.model.AgentRegistry::class.java)
                .matching(
                    Query.query(Criteria.from(search))
                        .sort(sort)
                        .offset((pagination.limit * (pagination.page - 1)).toLong())
                        .limit(pagination.limit)
                ).all()
                .collectList()
                .block()
        }

        return AgentRegistryFilterResult(
            result = res?.map(agentResitryMapper::toDomain)?.toList() ?: emptyList(),
            pagination = pagination.copy(
                totalCount = count,
                totalPage = if (count % pagination.limit == 0) count / pagination.limit else count / pagination.limit + 1
            )
        )
    }


}
