package ru.metrosoft.service

import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.toList
import org.springframework.stereotype.Service
import ru.metrosoft.output.persistance.AgentNBSMapper
import ru.metrosoft.model.AgentNBS
import ru.metrosoft.model.NewAgentNBS
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentNBSRepository
import java.util.*

@Service
class AgentNBSService(
    val agentNBSRepository: AgentNBSRepository,
    val agentNBSMapper: AgentNBSMapper
) {

    suspend fun getAgentList(): List<AgentNBS> {
        return agentNBSRepository.findAll().toList().map(agentNBSMapper::toDomain)
    }

    suspend fun saveAgent(agent: NewAgentNBS): AgentNBS {
        return agentNBSMapper.toDomain(agentNBSRepository.save(agentNBSMapper.toPersistModel(agent)))
    }

    suspend fun getAgent(id: UUID): AgentNBS {
        val agentNBS = agentNBSRepository.findTopByOrderByIdDesc(id) ?: throw IllegalArgumentException("Agent NBS not found")

        return agentNBSMapper.toDomain(agentNBS.first())
    }

    suspend fun name(agentNBSId: UUID): String {
        return getAgent(agentNBSId).name
    }
}
