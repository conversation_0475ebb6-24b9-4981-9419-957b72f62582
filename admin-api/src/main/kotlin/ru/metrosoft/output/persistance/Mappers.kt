package ru.metrosoft.output.persistance

import org.mapstruct.Mapper


@Mapper(componentModel = "spring")
interface AgentNBSMapper {
    fun toPersistModel(agentNBS: ru.metrosoft.model.NewAgentNBS) : ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS

    fun toPersistModel(agentNBS: ru.metrosoft.model.AgentNBS) : ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS

    fun toDomain(agentNBS: ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS) : ru.metrosoft.model.AgentNBS

}

@Mapper(componentModel = "spring")
interface AgentResitryMapper {
    fun toDomain(agentResitry: ru.metrosoft.sputnikgate.output.persistance.model.AgentRegistry): ru.metrosoft.model.AgentRegistry
}