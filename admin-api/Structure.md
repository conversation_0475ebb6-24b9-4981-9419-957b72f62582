# Structure of admin-api src directory

This document outlines the structure and purpose of files within the `src` directory of the admin-api module.

```
src/
├── main/
│   ├── java/ - (вероятно, старый код или общие Java классы, требующие анализа)
│   │   └── ru/ (...)
│   ├── kotlin/
│   │   └── ru/metrosoft/
│   │       ├── input/ - Пакет для обработки входящих административных запросов (REST API).
│   │       │   ├── model/ - Модели данных (DTO) для входящих запросов админ API.
│   │       │   │   └── Model.kt - DTO для различных административных операций (агенты, платежи, реестры и т.д.).
│   │       │   ├── AgentMoneyBalanceController.kt - REST контроллер для просмотра финансовой информации и долгов агентов.
│   │       │   ├── PaymentController.kt - REST контроллер для просмотра и мониторинга платежей.
│   │       │   ├── RequestController.kt - REST контроллер для просмотра логов запросов от агентов.
│   │       │   ├── AgentController.kt - REST контроллер для управления данными агентов.
│   │       │   ├── NBSRegistryController.kt - REST контроллер для просмотра и управления реестрами НБС.
│   │       │   └── AgentNBSController.kt - REST контроллер для управления конфигурациями агентов НБС.
│   │       ├── service/ - Пакет с бизнес-логикой для админ модуля.
│   │       │   ├── AgentNBSService.kt - Сервис для работы с конфигурациями агентов НБС (в контексте админки).
│   │       │   └── AgentRegistryService.kt - Сервис для работы с реестрами агентов (в контексте админки).
│   │       ├── App.kt - Главный класс приложения для модуля admin-api, точка входа.
│   │       ├── model/ - Модели данных, используемые в разных слоях модуля admin-api (доменные модели или общие DTO).
│   │       │   └── Model.kt - Модели данных для сущностей админки (например, AgentNBS, AgentRegistry).
│   │       └── output/ - Пакет для взаимодействия с внешними системами или другими модулями (например, через persistance).
│   │           └── persistance/ - Пакет для работы с хранилищем данных (вероятно, использует common-lib).
│   │               └── Mappers.kt - Мапперы для преобразования моделей данных между слоями.
│   └── resources/ - Ресурсы приложения для модуля admin-api.
│       └── application.yaml - Конфигурационный файл для модуля admin-api.
``` 