# Admin API Module

## Назначение
Модуль `admin-api` предоставляет административный интерфейс для управления и мониторинга системы Sputnik Gateway. Основные задачи модуля включают:

- Управление данными контрагентов (агентов) и их конфигурациями в НБС.
- Мониторинг транзакций и платежей, включая их детализацию.
- Просмотр и управление реестрами (как агентскими, так и НБС).
- Доступ к информации о запросах и финансовом состоянии (долгах) агентов.
- Управление системными настройками (если предусмотрено административным интерфейсом).

## Основные сущности
Модуль `admin-api` взаимодействует со следующими основными сущностями, определенными в проекте Sputnik Gateway. В контексте этого модуля они часто представляются в виде DTO для отображения или изменения через административный интерфейс:

- **Агенты шлюза (Agent)**:
  - Контрагенты, авторизованные для продажи билетов через шлюз.
  - Содержат основную информацию: название, статус активности (enabled).
  - Связаны с конфигурацией НБС через `agent_nbs_id`.
  - Функционал админки: Просмотр, создание, редактирование и управление статусом агентов.

- **Агенты НБС (AgentNBS)**:
  - Конфигурации подключения агентов к Новой Билетной Системе (НБС).
  - Содержат параметры подключения: API ключи (`openApiKey`), сертификат (`openApiCertificate`), учетные данные SFTP (`SFTPUsername`, `SFTPPassword`, `SFTPUrl`, `SFTPPort`).
  - Функционал админки: Просмотр и управление конфигурациями подключения агентов к НБС.

- **Платежи (AgentPayment, AgentPaymentService)**:
  - Операции пополнения/продажи билетов, зарегистрированные в шлюзе.
  - Содержат информацию о сумме, статусе транзакции, номере карты, ошибках.
  - Типы платежей: PAYWRITE (1), PAY (2), INFO, WRITE.
  - Услуги платежей (AgentPaymentService) - детализация конкретных билетов/услуг в рамках платежа (код услуги, название, сумма).
  - Функционал админки: Просмотр списка платежей, их статусов и детализации услуг.

- **Реестры агентов (AgentRegistry)**:
  - Файлы реестров операций (продаж и записей билетов), сформированные агентами.
  - Отправляются агентами в шлюз для последующей сверки.
  - Форматы: salereg_*.csv, writeticketsreg_*.csv.
  - Функционал админки: Просмотр загруженных агентских реестров, их статусов обработки и содержимого.

- **Реестры НБС (Registry)**:
  - Файлы сводных реестров операций, сформированные шлюзом для каждого агента НБС.
  - Создаются ежедневно и отправляются на SFTP сервер агента НБС.
  - Форматы: salereg_*.csv, writeticketsreg_*.csv (единые с агентскими реестрами).
  - Функционал админки: Просмотр сформированных шлюзом реестров НБС, их статусов отправки и содержимого.

- **Запросы агентов (AgentRequest)**:
  - Логирование входящих запросов от агентов к шлюзу.
  - Содержит тип запроса, статус выполнения, коды и тексты ошибок, идентификатор трассировки.
  - Функционал админки: Мониторинг входящих запросов от агентов для отладки и анализа.

- **Долги агентов (AgentDebt)**:
  - Финансовая информация по взаиморасчетам с агентами.
  - Содержат суммы платежей (summ_p), записей (summ_w) и общие показатели.
  - Функционал админки: Просмотр финансовой информации и балансов агентов.

## Логика модуля
Модуль реализует логику для предоставления административного доступа к данным шлюза. Это включает:

- **REST Контроллеры**: Предоставление API эндпоинтов для взаимодействия с клиентской частью административного интерфейса.
- **Сервисы**: Бизнес-логика для выборки, агрегации и преобразования данных из хранилища шлюза для отображения в админке.
- **Безопасность**: Аутентификация и авторизация административных пользователей, контроль доступа к различным функциям.
- **Взаимодействие с данными**: Чтение данных из базы данных шлюза (возможно, через общую библиотеку `common-lib`).

## Возможные настройки
Основные настройки модуля могут включать параметры, связанные с безопасностью (например, секрет для JWT) и лимиты для отображения данных (например, максимальное количество записей в отчетах). Эти настройки, вероятно, определены в файлах конфигурации (`admin-api/src/main/resources/application.yaml`). 