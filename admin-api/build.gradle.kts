import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.springframework.boot.gradle.tasks.bundling.BootJar

plugins {
    id("idea")
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.dependency.management)
}

group = "ru.metrosoft"
version = "2.0.0-SNAPSHOT"

kapt {
    showProcessorStats = true
    correctErrorTypes = true
}

dependencies {
    implementation(project(":common-lib"))

    implementation(libs.spring.boot.starter.actuator)
    implementation(libs.spring.boot.starter.data.r2dbc)
//    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation(libs.spring.boot.starter.security)
    implementation(libs.spring.security.oauth2.jose)
    implementation(libs.spring.boot.starter.oauth2.resource.server)
    implementation(libs.spring.boot.starter.webflux)

    implementation(libs.reactor.kotlin.extensions)
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib.jdk8)
    implementation(libs.kotlinx.coroutines.reactor)
    implementation(libs.kotlinx.coroutines.core)

    // certificates
    implementation(libs.bouncycastle.bcprov.jdk15on)
    implementation(libs.bouncycastle.bcpkix.jdk15on)

    //Kotlin-ext
    implementation(libs.arrow.core)
    implementation(libs.sftp.client)

    developmentOnly("org.springframework.boot:spring-boot-devtools") // TODO: Add to Version Catalog?

    // MapStruct
    compileOnly(libs.mapstruct)
    kapt(libs.mapstruct.processor)

    //jjwt
    // implementation(libs.jjwt.api)
    // implementation(libs.jjwt.impl)
    // implementation(libs.jjwt.jackson)

    runtimeOnly(libs.r2dbc.postgresql)
    runtimeOnly(libs.r2dbc.pool)

    implementation(libs.flyway.core)
    runtimeOnly(libs.postgresql)

    implementation(libs.commons.io)

    //Test
    testImplementation(libs.junit.jupiter.api)
    testRuntimeOnly(libs.junit.jupiter.engine)
    testCompileOnly(libs.lombok)
    testImplementation(libs.spring.boot.starter.test) {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
    testImplementation(libs.mockk.jvm)
}

tasks.named<Test>("test") {
    useJUnitPlatform()
}
