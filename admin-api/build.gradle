import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

buildscript {
    ext {
        springBootVersion = '3.4.2'
        springDependencyManagementVersion = '1.1.0'

        kotlinJvmVersion = '2.1.10'
        kotlinCoroutinesOkhttpVersion = "1.0"

        okhttpVersion = '4.10.0'
        jacksonModuleKotlinVersion = "2.14.0"

        arrowCoreVersion = "1.0.1"

        flywayVersion = "9.14.1"
        postgresqlVersion = "42.5.2"

        jjwtVersion = "0.11.5"
        springdocVersion = "2.8.5"
    }
}

plugins {
    id 'idea'
    id 'org.jetbrains.kotlin.jvm' version "$kotlinJvmVersion"
    id "org.jetbrains.kotlin.kapt" version "$kotlinJvmVersion"
    id 'org.jetbrains.kotlin.plugin.spring' version "$kotlinJvmVersion"
    id 'org.springframework.boot' version "$springBootVersion"
    id 'io.spring.dependency-management' version "$springDependencyManagementVersion"
}

group = 'ru.metrosoft'
version = '2.0.0-SNAPSHOT'

repositories {
    mavenCentral()
}

kapt {
    showProcessorStats = true
    correctErrorTypes true
}

dependencies {
    implementation project(":common-lib")

    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'
//    implementation "org.springframework.boot:spring-boot-starter-jdbc"
    implementation "org.springframework.boot:spring-boot-starter-security"
    implementation "org.springframework.security:spring-security-oauth2-jose"
    implementation "org.springframework.boot:spring-boot-starter-oauth2-resource-server"
    implementation "org.springframework.boot:spring-boot-starter-webflux"

    implementation 'io.projectreactor.kotlin:reactor-kotlin-extensions'
    implementation 'org.jetbrains.kotlin:kotlin-reflect'
    implementation 'org.jetbrains.kotlin:kotlin-stdlib-jdk8'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-reactor'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core"

    // certificates
    implementation("org.bouncycastle:bcprov-jdk15on:1.70")
    implementation("org.bouncycastle:bcpkix-jdk15on:1.70")

    //Kotlin-ext
    implementation "io.arrow-kt:arrow-core:$arrowCoreVersion"
    implementation 'com.springml:sftp.client:1.0.3'

    developmentOnly 'org.springframework.boot:spring-boot-devtools'

    // MapStruct
    compileOnly "org.mapstruct:mapstruct:1.5.3.Final"
    kapt "org.mapstruct:mapstruct-processor:1.5.3.Final"

    //jjwt
//    implementation "io.jsonwebtoken:jjwt-api:$jjwtVersion"
//    implementation "io.jsonwebtoken:jjwt-impl:$jjwtVersion"
//    implementation "io.jsonwebtoken:jjwt-jackson:$jjwtVersion"


    runtimeOnly('org.postgresql:r2dbc-postgresql')
    runtimeOnly('io.r2dbc:r2dbc-pool')

    implementation 'commons-io:commons-io:2.16.1'

    //Test
    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly 'org.projectlombok:lombok'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        // Exclude the test engine you don't need
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

jar {
    enabled = false
}
