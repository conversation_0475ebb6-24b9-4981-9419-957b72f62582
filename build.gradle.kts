import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("idea")
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.dependency.management)
    id("com.github.ben-manes.versions") version "0.52.0"
}

group = "ru.metrosoft"
version = "2.0.0-SNAPSHOT"

kapt {
    showProcessorStats = true
}

dependencies {
    implementation(project(":nbs-client"))
    implementation(project(":common-lib"))

    implementation(libs.spring.boot.starter.actuator)
    implementation(libs.spring.boot.starter.data.r2dbc)
    implementation(libs.spring.boot.starter.logging)
    implementation(libs.spring.boot.starter.security)
    implementation(libs.spring.boot.starter.webflux)

    implementation(libs.logbook.spring.boot.webflux.autoconfigure)
    implementation(libs.logbook.okhttp)

    implementation(libs.reactor.kotlin.extensions)
    implementation(libs.kotlin.reflect)
    implementation(libs.kotlin.stdlib.jdk8)
    implementation(libs.kotlinx.coroutines.reactor)
    implementation(libs.kotlinx.coroutines.core)
    testImplementation(libs.kotlinx.coroutines.test)

    //Kotlin-ext
    implementation(libs.arrow.core)

    developmentOnly("org.springframework.boot:spring-boot-devtools")

    // MapStruct
    compileOnly(libs.mapstruct)
    kapt(libs.mapstruct.processor)

    //okHttp
    implementation(libs.okhttp)
    implementation(libs.okhttp.logging.interceptor)
    implementation(libs.jackson.module.kotlin)
    implementation(libs.kotlin.coroutines.okhttp)

    runtimeOnly(libs.r2dbc.postgresql)
    runtimeOnly(libs.r2dbc.pool)

    //MDC
    implementation(libs.kotlinx.coroutines.slf4j)

    implementation(libs.flyway.core)
    runtimeOnly(libs.postgresql)

    implementation(libs.commons.io)

    //swagger
    implementation(libs.springdoc.openapi.starter.webflux.api)
    implementation(libs.springdoc.openapi.starter.webflux.ui)

    //Test
    testImplementation(libs.junit.jupiter.api)
    testRuntimeOnly(libs.junit.jupiter.engine)
    testCompileOnly(libs.lombok)
    testImplementation(libs.spring.boot.starter.test) {
        // Exclude the test engine you don't need
        exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
    }
}

tasks.named<Test>("test") {
    useJUnitPlatform()
}
