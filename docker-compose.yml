version: '3.7'

services:
  agent_gateway:
    image: ${IMAGE_PREFIX:-sputnik-}api:${TAG:-latest}
    build: .
    container_name: agent_gateway
    ports:
      - 8088:8081
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      - DB_URL=postgresql://agent_gateway_db/sputnik
      - DB_USER=postgres
      - DB_PASSWORD=postgres

  agent_regestry:
    image: ${IMAGE_PREFIX:-sputnik-}regestry:${TAG:-latest}
    build:
      context: .
      dockerfile: Dockerfile-regestry
    container_name: agent_regestry
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      - DB_URL=postgresql://agent_gateway_db/sputnik
      - DB_USER=postgres
      - DB_PASSWORD=postgres

  agent_admin_api:
    image: ${IMAGE_PREFIX:-sputnik-}console-api:${TAG:-latest}
    build:
      context: .
      dockerfile: Dockerfile-api
    container_name: agent_admin_api
    ports:
      - 8089:8082
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      - DB_URL=postgresql://agent_gateway_db/sputnik
      - DB_USER=postgres
      - DB_PASSWORD=postgres

  agent_ui:
    image: ${IMAGE_PREFIX:-sputnik-}ui:${TAG:-latest}
    build: ./sputnik-ui
    container_name: agent_ui
    ports:
      - 8080:80

  agent_gateway_db:
    image: postgres:13
    container_name: agent_gateway_db
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - pg_data:/var/lib/postgresql/data
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
    environment:
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: sputnik
      PGDATA: /var/lib/postgresql/data/pgdata
    networks:
      - agent_gateway

networks:
  agent_gateway:
volumes:
  pg_data:

