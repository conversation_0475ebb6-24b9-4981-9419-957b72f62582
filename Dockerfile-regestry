FROM gradle:8.13.0-jdk23-alpine as builder
RUN apk add gcompat
WORKDIR /build
ADD .. /build

RUN --mount=type=cache,target=/home/<USER>/.gradle/caches \
    --mount=type=cache,target=/home/<USER>/.gradle/wrapper \
    gradle --no-daemon regestry:bootJar -i


FROM openjdk:23-jdk
COPY --from=builder build/regestry/build/libs/regestry-*.jar ./sputnik-regestry.jar

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dspring.active.profiles=prod", "-jar", "sputnik-regestry.jar"]

