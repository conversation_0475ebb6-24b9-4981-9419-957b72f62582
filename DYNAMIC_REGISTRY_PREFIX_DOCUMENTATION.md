# Документация по динамическим префиксам реестров продаж

## Обзор

Реализована функциональность динамического определения префиксов для файлов реестров продаж на основе типов платежей, обрабатываемых за указанный день.

## Логика определения префиксов

### Типы платежей
- **PAYWRITE** - платеж с записью на карту
- **PAY** - удаленный платеж (без записи на карту)

### Правила определения префикса
1. **FBT** - используется когда есть платежи типа PAYWRITE (приоритет)
2. **DWB** - используется когда есть только платежи типа PAY
3. **FBT** - используется по умолчанию (когда нет платежей)

### Условия учета платежей
- Платеж должен иметь `status = true`
- Для PAYWRITE платежей дополнительно требуется `payStatus = true`
- Для PAY платежей дополнительных условий нет

## Реализация

### Основной метод
```kotlin
private suspend fun determineSaleRegistryPrefix(day: LocalDate): String {
    val payments = agentPaymentService.findSalesByDay(day)
        .filter { it.status }
        .filter { (it.type == AgentPaymentType.PAYWRITE && it.payStatus) || it.type == AgentPaymentType.PAY }
        .toSet()

    val hasPaywrite = payments.any { it.type == AgentPaymentType.PAYWRITE }
    val hasPay = payments.any { it.type == AgentPaymentType.PAY }

    return when {
        hasPaywrite -> "FBT"  // Есть платежи с записью - используем FBT
        hasPay -> "DWB"       // Есть только удаленные платежи - используем DWB
        else -> "FBT"         // По умолчанию FBT
    }
}
```

### Интеграция в генерацию имени файла
```kotlin
"SALEREG" -> { 
    val prefix = determineSaleRegistryPrefix(date)
    "salereg_${prefix}_" + date.format(REGISTRY_DATE_FORMAT) + "_" + 
    (registryRepository.countByTypeAndDate(type, date).block()!! + 1).toString().padStart(2, '0') + ".csv" 
}
```

## Примеры использования

### Сценарий 1: Только платежи с записью
**Входные данные:**
- Платежи типа PAYWRITE с payStatus = true

**Результат:**
- Префикс: FBT
- Имя файла: `salereg_FBT_240115_01.csv`

### Сценарий 2: Только удаленные платежи
**Входные данные:**
- Платежи типа PAY

**Результат:**
- Префикс: DWB
- Имя файла: `salereg_DWB_240115_01.csv`

### Сценарий 3: Смешанные типы платежей
**Входные данные:**
- Платежи типа PAYWRITE с payStatus = true
- Платежи типа PAY

**Результат:**
- Префикс: FBT (приоритет PAYWRITE)
- Имя файла: `salereg_FBT_240115_01.csv`

### Сценарий 4: Нет платежей
**Входные данные:**
- Нет платежей за день

**Результат:**
- Префикс: FBT (по умолчанию)
- Имя файла: `salereg_FBT_240115_01.csv`

### Сценарий 5: PAYWRITE без payStatus
**Входные данные:**
- Платежи типа PAYWRITE с payStatus = false
- Платежи типа PAY

**Результат:**
- Префикс: DWB (PAYWRITE не учитывается без payStatus)
- Имя файла: `salereg_DWB_240115_01.csv`

## Тестирование

### Unit тесты
- `RegistryServiceTest.kt` - тестирование логики определения префикса
- Покрывает все сценарии использования
- Использует моки для изоляции тестируемой функциональности

### Интеграционные тесты
- `RegistryServiceIntegrationTest.kt` - тестирование с реальной БД
- Проверяет полный цикл создания реестра
- Использует TestContainers для PostgreSQL

### Cucumber тесты
- Обновлены feature файлы для покрытия новой функциональности
- `nbs-registry-management.feature` - тесты генерации имен файлов
- `registry-processing.feature` - тесты обработки реестров

## Обратная совместимость

- Существующие реестры не затрагиваются
- По умолчанию используется префикс FBT для обеспечения совместимости
- Логика применяется только к новым реестрам типа SALEREG

## Мониторинг и логирование

- Рекомендуется добавить логирование выбранного префикса
- Мониторинг соотношения FBT/DWB реестров
- Алерты при неожиданных изменениях в типах платежей

## Возможные улучшения

1. **Конфигурируемые префиксы** - вынести префиксы в конфигурацию
2. **Дополнительные типы** - поддержка новых типов платежей
3. **Кэширование** - кэширование результатов определения префикса
4. **Аудит** - логирование изменений в логике определения префиксов

## Связанные компоненты

- `RegistryService.kt` - основная логика
- `AgentPaymentService.kt` - получение платежей
- `AgentPaymentType.kt` - типы платежей
- Планировщик задач генерации реестров
- SFTP клиент для передачи файлов
