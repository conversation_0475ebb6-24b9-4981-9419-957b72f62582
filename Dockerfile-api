FROM gradle:8.13.0-jdk23-alpine as builder
RUN apk add gcompat
WORKDIR /build
ADD .. /build

RUN --mount=type=cache,target=/home/<USER>/.gradle/caches \
    --mount=type=cache,target=/home/<USER>/.gradle/wrapper \
    gradle --no-daemon admin-api:bootJar -i


FROM openjdk:23-jdk
COPY --from=builder build/admin-api/build/libs/admin-api-*.jar ./sputnik-admin-api.jar

EXPOSE 8082

ENTRYPOINT ["java", "-Djava.security.egd=file:/dev/./urandom", "-Dspring.active.profiles=prod", "-jar", "sputnik-admin-api.jar"]

