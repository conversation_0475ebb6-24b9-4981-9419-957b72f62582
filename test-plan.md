# План тестирования проекта Sputnik Gateway

## Обзор архитектуры

Проект состоит из трех основных модулей:
- **Gateway** - основной API для работы с картами Тройка
- **Admin-API** - административный интерфейс
- **Registry** - обработка реестров и файлов

## Структура тестов

### 1. Модуль Gateway (src/test)
- Тесты API пополнения карт Тройка
- Тесты аутентификации и авторизации
- Тесты интеграции с НБС
- Тесты обработки ошибок

### 2. Модуль Admin-API (admin-api/src/test)
- Тесты управления агентами
- Тесты просмотра платежей и долгов
- Тесты управления реестрами НБС
- Тесты безопасности админки

### 3. Модуль Registry (regestry/src/test)
- Тесты обработки файлов реестров
- Тесты валидации данных
- Тесты сверки с базой данных
- Тесты планировщика задач

## Созданные Cucumber тест-кейсы

### Gateway модуль:
1. **troika-refill-api.feature** - Основные API операции пополнения карт
2. **security-authentication.feature** - Безопасность и аутентификация

### Admin-API модуль:
1. **agent-management.feature** - Управление агентами и контрактами
2. **payment-management.feature** - Управление платежами и долгами
3. **nbs-registry-management.feature** - Управление НБС и реестрами

### Registry модуль:
1. **registry-processing.feature** - Обработка файлов реестров
2. **registry-validation.feature** - Валидация данных реестров

## Инструменты тестирования

- **Cucumber** - BDD тесты
- **JUnit 5** - unit тесты
- **TestContainers** - интеграционные тесты с БД
- **WireMock** - мокирование внешних API
- **Spring Boot Test** - тестирование Spring контекста

## Покрытие тестами

### Критические компоненты (100% покрытие):
- TroikaRefillRestApi
- Сервисы безопасности
- Валидация реестров
- Обработка платежей

### Важные компоненты (80%+ покрытие):
- Административные контроллеры
- Сервисы работы с данными
- Мапперы и конвертеры

### Вспомогательные компоненты (60%+ покрытие):
- Утилиты
- Конфигурация
- Логирование

## Дополнительные рекомендации

### Unit тесты:
- Тестирование сервисов изолированно
- Мокирование зависимостей
- Проверка бизнес-логики

### Интеграционные тесты:
- Тестирование с реальной БД (TestContainers)
- Тестирование HTTP API
- Тестирование взаимодействия модулей

### Performance тесты:
- Нагрузочное тестирование API
- Тестирование обработки больших файлов реестров
- Мониторинг производительности БД

### Security тесты:
- Тестирование различных сценариев атак
- Проверка CORS настроек
- Валидация JWT токенов
