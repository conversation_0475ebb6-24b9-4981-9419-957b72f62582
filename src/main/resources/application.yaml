spring:
  application:
    name: sputnik-gateway

  r2dbc:
    url: r2dbc:pool:${DB_URL:postgresql://localhost:5432/sputnik}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:true}
    validate-migration-naming: true
    url: jdbc:${DB_URL:postgresql://localhost:5432/sputnik}
    user: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}
    locations:
      - classpath:db/migration
    mixed: true
    baseline-on-migrate: true
    out-of-order: true
    ignore-missing-migrations: true

  web:
    resources:
      add-mappings: false

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

server:
  forward-headers-strategy: framework
  port: ${PORT:8081}

springdoc:
  show-actuator: false
  swagger-ui:
    disable-swagger-default-url: true
    path: /swagger-ui
    configUrl: /v3/api-docs/swagger-config
    url: /v3/api-docs

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization
