--CREATE EXTENSION "uuid-ossp";

alter table agent_contract add column start_date date not null;
alter table agent_contract alter column end_date type date;
alter table agent_contract alter column end_date drop not null;

create or replace view agent_debt as
select
    ap.agent_id,
    ap.ts::date as date,
    sum(
            CASE pay_status
                WHEN true THEN (ac.sale_rate / 100) * ap.amount
                WHEN false THEN 0
            END
    ) as summ_p,
    sum(
        CASE write_status
            WHEN true THEN ap.amount * (ac.write_rate / 100)
            WHEN false THEN 0
        END
    ) as summ_w,
    count(ap.id) as cnt
from agent_payment ap
left join agent_contract ac on ac.agent_id = ap.agent_id and ap.ts >= ac.start_date and ap.ts <= coalesce(ac.end_date, '2099-01-01')
group by ap.agent_id, ap.ts;

create index agent_payment_agent_id_and_ts_idx on agent_payment (agent_id, ts);
