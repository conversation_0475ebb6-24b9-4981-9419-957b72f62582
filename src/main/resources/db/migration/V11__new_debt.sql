create or replace view debt_agent as
select
    ap.agent_id,
    ap.ts::date as date,
    sum(
            CASE pay_status
                WHEN true THEN (ac.sale_rate / 100) * ap.amount
                WHEN false THEN 0
            END
    ) as summ_p,
    sum(
        CASE write_status
            WHEN true THEN ap.amount * (ac.write_rate / 100)
            WHEN false THEN 0
        END
    ) as summ_w,
    count(ap.id) as cnt,
    sum(ap.amount) as sum_all
from agent_payment ap
left join agent_contract ac on ac.agent_id = ap.agent_id and ap.ts >= ac.start_date and ap.ts <= coalesce(ac.end_date, '2099-01-01')
group by ap.agent_id, ap.ts::date;