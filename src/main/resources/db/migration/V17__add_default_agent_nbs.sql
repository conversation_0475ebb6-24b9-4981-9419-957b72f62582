-- V17__add_default_agent_nbs_and_update.sql

INSERT INTO agent_nbs (name) VALUES ('Default Agent NBS');

DO $$
DECLARE
default_uuid UUID;
BEGIN
SELECT id INTO default_uuid FROM agent_nbs WHERE name = 'Default Agent NBS';

UPDATE agent
SET agent_nbs_id = default_uuid
WHERE agent_nbs_id IS NULL;

UPDATE agent_payment
SET agent_nbs_id = default_uuid
WHERE agent_nbs_id IS NULL;

UPDATE registry
SET agent_nbs_id = default_uuid
WHERE agent_nbs_id IS NULL;
END $$;