create table ftp_users
(
    username varchar(255)                  not null primary key,
    password varchar(255)                  not null,
    uid      integer default '1000'::integer not null,
    gid      integer default '1000'::integer not null,
    agent_id uuid
        references agent not null,
    home_dir varchar(255) generated always as (('/home/<USER>/'::text || agent_id)) stored
);

comment on column ftp_users.password is 'echo -n ''password'' | argon2 "$(openssl rand -hex 16)" -id | awk ''/Encoded:/ {print $2}''';