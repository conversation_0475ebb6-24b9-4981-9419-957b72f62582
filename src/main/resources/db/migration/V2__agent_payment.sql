--CREATE EXTENSION "uuid-ossp";

create table agent_payment (
        id varchar(36)         not null constraint agent_payment_id_pk primary key,
        ts date not null default 'now':: date,
        agent_id uuid not null,

        service_code varchar(10) not null,
        amount decimal(12,0) not null
);

create table card_sector_read (
    uid varchar(14) not null,
    sector_num int not null,
    sector_value varchar(64) not null
);

create table card_sector_write (
    uid varchar(64) not null,
    sector_num int not null,
    sector_value varchar(64) not null
);