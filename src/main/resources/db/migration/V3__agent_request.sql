--CREATE EXTENSION "uuid-ossp";

create table agent_request (
        id BIGSERIAL primary key,

        ts timestamp null default 'now':: timestamp,
        agent_id uuid not null,

        type varchar(20) not null,

        status int,

        errorCode varchar(150),

        errorMessage varchar(250),

        traceId varchar(150)

);

create unique index agent_request_tx_idx on agent_request (ts, agent_id);

create index agent_request_type_idx on agent_request (type);

create index agent_request_ts_idx on agent_request (ts);