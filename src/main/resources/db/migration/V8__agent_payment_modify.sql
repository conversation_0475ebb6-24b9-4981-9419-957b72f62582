create type agent_payment_type as enum ('INFO', 'PAYWRITE', 'WRITE');

alter table agent_payment add type agent_payment_type;

alter table agent_payment add status boolean default false;
alter table agent_payment add status_code int;
alter table agent_payment add error_code varchar(150);
alter table agent_payment add error_message varchar(250);
alter table agent_payment add trace_id varchar(150);

alter table agent_payment add notify_time timestamp;

alter table agent_payment add card_num varchar(50);

create table agent_payment_service (
        id varchar(36)         not null,
        service_code int       not null,

        amount decimal(12,0)   not null,
        name varchar(500),

        constraint agent_payment_service_id_pk primary key (id, service_code)
);

create index agent_payment_service_id_idx on agent_payment_service (id);