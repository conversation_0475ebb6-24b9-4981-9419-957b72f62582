--CREATE EXTENSION "uuid-ossp";

create table agent_contract (
        id uuid not null constraint agent_contract_id_pk primary key default gen_random_uuid(),

        ts timestamp null default 'now':: timestamp,
        agent_id uuid not null,

        number varchar(100) not null,

        end_date timestamp not null,

        sale_rate double precision not null,
        write_rate double precision not null
);

create index agent_contract_agent_id_idx on agent_contract (agent_id);