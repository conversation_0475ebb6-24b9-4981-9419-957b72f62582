package ru.metrosoft.sputnikgate.logging
import org.apache.commons.lang3.exception.ExceptionUtils

object ExceptionUtils {
    private const val INDENT = "\n    "
    private val suppressedPackages: List<String> = listOf(
        "org.apache",
        "com.sun",
        "sun",
        "com.ctc",
        "com.fasterxml",
        "java",
        "javax",
        "org.eclipse",
        "org.springframework",
        "io.netty",
    )
    private const val CGLIB_MARKER_1 = "$\$EnhancerByCGLIB$$"
    private const val CGLIB_MARKER_2 = "$\$FastClassByCGLIB$$"
    private const val CGLIB_SPRING_MARKER = "BySpringCGLIB"

    fun append(sb: StringBuilder, exception: Throwable): StringBuilder {
        val lastThrowable = appendExceptionChain(sb, exception)
        if (lastThrowable != null) {
            appendStackTraceSkippedTill(sb, lastThrowable.stackTrace, Int.MAX_VALUE)
        }
        return sb
    }

    private fun appendExceptionChain(sb: StringBuilder, exception: Throwable): Throwable? {
        val throwableList = ExceptionUtils.getThrowableList(exception)
        if (throwableList.isEmpty()) {
            return null
        }
        var throwable = throwableList[0]
        sb.append(throwable)
        var i = 1
        val n = throwableList.size
        while (i < n) {
            appendStackTraceSkippedTill(sb, throwable.stackTrace, 1)
            throwable = throwableList[i]
            sb.append("\nCaused by: ").append(throwable)
            i++
        }

        return throwableList[throwableList.size - 1]
    }

    private fun appendStackTraceSkippedTill(sb: StringBuilder, stackTrace: Array<StackTraceElement>, max: Int) {
        if (stackTrace.size == 0) {
            return
        }

        sb.append(INDENT).append("at ").append(stackTrace[0])
        var printedLines = 0
        var skippedLines = 0
        val skippedPackages: MutableCollection<String> = HashSet()
        var i = 1
        val n = stackTrace.size
        while (i < n && printedLines < max) {
            val traceElement = stackTrace[i]
            if (isCGLIB(traceElement)) {
                i++
                continue
            }
            val skippedPackage = getSkippedPackage(traceElement)
            if (skippedPackage != null) {
                skippedLines++
                skippedPackages.add(skippedPackage)
                i++
                continue
            }

            if (skippedLines > 0) {
                sb.append(INDENT).append('<').append(skippedLines).append(" internal calls ").append(skippedPackages)
                    .append('>')
                skippedLines = 0
                skippedPackages.clear()
            }

            sb.append(INDENT).append("at ").append(traceElement)
            printedLines++
            i++
        }
        if (skippedLines > 0) {
            sb.append(INDENT).append('<').append(skippedLines).append(" internal calls ").append(skippedPackages)
                .append('>')
        }
    }

    private fun isCGLIB(traceElement: StackTraceElement): Boolean {
        val className = traceElement.className
        return className.contains(CGLIB_SPRING_MARKER) || className.contains(CGLIB_MARKER_1) || className.contains(
            CGLIB_MARKER_2
        )
    }

    private fun getSkippedPackage(traceElement: StackTraceElement): String? {
        for (suppressedPackage in suppressedPackages) {
            if (traceElement.className.startsWith(suppressedPackage)) {
                return suppressedPackage
            }
        }

        return null
    }
}