package ru.metrosoft.sputnikgate.logging

import org.apiguardian.api.API
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.http.HttpStatus
import org.zalando.logbook.*
import java.io.IOException

@API(status = API.Status.STABLE)
class EcsLogSink(private val formatter: EcsHttpLogFormatter) : Sink {
    private val log: Logger = LoggerFactory.getLogger(Logbook::class.java)

    override fun isActive(): Boolean {
        return log.isInfoEnabled
    }

    @Throws(IOException::class)
    override fun write(precorrelation: Precorrelation, request: HttpRequest) {
        val oldContext = MDC.getCopyOfContextMap()
        try {
            MDC.put("http", formatter.format(precorrelation, request))
            MDC.put("span.id", precorrelation.id)
            log.info("Start: {} {}", request.method, request.requestUri)
        } finally {
            MDC.setContextMap(oldContext)
        }
    }

    @Throws(IOException::class)
    override fun write(correlation: Correlation, request: HttpRequest, response: HttpResponse) {
        val oldContext = MDC.getCopyOfContextMap()
        try {
            MDC.put("http", formatter.format(correlation, request, response))
            MDC.put("span.id", correlation.id)
            log.info("Stop: {} {} Status: {} ({})",
                request.method,
                request.requestUri,
                response.status,
                HttpStatus.resolve(response.status)?.toString() ?: response.status.toString()
            )
        } finally {
            MDC.setContextMap(oldContext)
        }
    }
}