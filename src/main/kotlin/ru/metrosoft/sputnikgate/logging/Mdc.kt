package ru.metrosoft.sputnikgate.logging

import org.slf4j.MDC
import org.springframework.web.server.ServerWebExchange

fun ServerWebExchange.fillMdcContext() {
    MDC.setContextMap(this.attributes["mdc"] as? MutableMap<String, String>)
}

fun ServerWebExchange.putMdcValue(key: String, value: String?) {
    if (value == null) {
        MDC.remove(key)
        (this.attributes["mdc"] as? MutableMap<String, String>)?.remove(key)
    } else {
        MDC.put(key, value)
        (this.attributes["mdc"] as? MutableMap<String, String>)?.put(key, value)
    }
}