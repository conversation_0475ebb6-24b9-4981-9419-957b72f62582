package ru.metrosoft.sputnikgate.logging

import com.fasterxml.jackson.databind.ObjectMapper
import org.apiguardian.api.API
import org.springframework.http.server.reactive.ServerHttpRequest
import org.zalando.logbook.*
import java.io.IOException
import java.nio.charset.StandardCharsets
import java.util.*
import kotlin.time.Duration

@API(status = API.Status.STABLE)
class ReactHttpLogFormatter {
    companion object {
        private val SENSITIVE_PATTERNS = listOf(
            "\"(\\w+KeyValue)\":\"[0-9A-Fa-f]+\"".toRegex(),
            "\"(\\w+_key_value)\":\"[0-9A-Fa-f]+\"".toRegex(),
        )
    }

    private val mapper: ObjectMapper

    init {
        mapper = ObjectMapper()
    }

    private fun prepareHeaders(message: ServerHttpRequest): Map<String, List<String>>? {
        val headers: Map<String, List<String>> = message.headers
        return headers.ifEmpty { null }
    }

    fun _format(content: MutableMap<String, Any?>): String = mapper.writeValueAsString(content)

    @Throws(IOException::class)
    fun format(duration: Duration, request: CachedRequest, response: CachedResponse?): String {
        return _format(prepare(duration, request, response!!))
    }

    @Throws(IOException::class)
    fun format(request: CachedRequest): String {
        return _format(prepare(request))
    }

    fun prepare(request: CachedRequest): MutableMap<String, Any?> {

        val content: MutableMap<String, Any?> = LinkedHashMap()

        content["remote"] = request.remoteAddress?.address.toString()
        content["method"] = request.method.name()
        content["uri"] = request.uri.toString()
        content["host"] = request.uri.host
        content["path"] = request.path.toString()
        content["headers"] = request.headers.mapValues { entry -> serializeHeaders(entry) }
        prepareBody(request).ifPresent { body: Any? -> content["body"] = body }

        return hashMapOf("request" to content)
    }

    private fun prepare(
        duration: Duration,
        request: CachedRequest,
        response: CachedResponse
    ): MutableMap<String, Any?> {
        val content: MutableMap<String, Any> = LinkedHashMap()

        content["duration"] = duration.inWholeMilliseconds
        if (response.statusCode != null) {
            content["status_code"] = response.statusCode!!.value()
        }

        content["headers"] = request.headers.mapValues { entry -> serializeHeaders(entry) }

        prepareBody(response).ifPresent { body: Any -> content["body"] = body }

        val req = hashMapOf(
            "uri" to request.uri.toString(),
            "path" to request.path.toString(),
            "host" to request.uri.host
        )
        return hashMapOf("response" to content, "request" to req)
    }

    @Throws(IOException::class)
    fun prepareBody(message: CachedRequest): Optional<Any> {
        val body = message.getCachedBody()
        return if (body.isEmpty()) {
            Optional.of<Any>(
                hashMapOf(
                    "bytes" to 0,
                )
            )
        } else {
            Optional.of<Any>(
                hashMapOf(
                    "bytes" to body.size,
                    "content" to sanitize(String(body, StandardCharsets.UTF_8))
                )
            )
        }
    }

    @Throws(IOException::class)
    fun prepareBody(message: CachedResponse): Optional<Any> {
        val body = message.getCachedBody()
        return if (body == null || body.isEmpty()) {
            Optional.of<Any>(
                hashMapOf(
                    "bytes" to 0,
                )
            )
        } else {
            Optional.of<Any>(
                hashMapOf(
                    "bytes" to body.size,
                    "content" to sanitize(String(body, StandardCharsets.UTF_8))
                )
            )
        }
    }

    private fun sanitize(body: String?): String? {
        if (body.isNullOrEmpty()) return body

        var sanitizedBody = body
        for (pattern in SENSITIVE_PATTERNS) {
            sanitizedBody = sanitizedBody?.replace(pattern) { matchResult ->
                val key = matchResult.groupValues[1]
                "\"$key\":\"[REDACTED]\""
            }
        }
        return sanitizedBody
    }

    private fun serializeHeaders(entry: Map.Entry<String, MutableList<String>>): MutableList<String> {
        if (entry.key.lowercase() == "authorization") {
            return entry.value
                .map { "[REDACTED]" }
                .toMutableList()
        }
        return entry.value
    }
}
