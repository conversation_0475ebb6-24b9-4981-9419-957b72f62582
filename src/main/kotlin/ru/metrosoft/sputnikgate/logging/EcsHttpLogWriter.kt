package ru.metrosoft.sputnikgate.logging

import org.apiguardian.api.API
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.zalando.logbook.Correlation
import org.zalando.logbook.HttpLogWriter
import org.zalando.logbook.Logbook
import org.zalando.logbook.Precorrelation

@API(status = API.Status.STABLE)
class EcsHttpLogWriter : HttpLogWriter {
    private val log: Logger = LoggerFactory.getLogger(Logbook::class.java)

    override fun isActive(): Boolean {
        return log.isInfoEnabled
    }

    override fun write(precorrelation: Precorrelation, request: String) {
        log.info(request)
    }

    override fun write(correlation: Correlation, response: String) {
        log.info(response)
    }
}
