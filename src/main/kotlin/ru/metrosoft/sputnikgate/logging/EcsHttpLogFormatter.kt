package ru.metrosoft.sputnikgate.logging

import com.fasterxml.jackson.databind.ObjectMapper
import org.apiguardian.api.API
import org.zalando.logbook.*
import java.io.IOException
import java.util.*

@API(status = API.Status.STABLE)
class EcsHttpLogFormatter : StructuredHttpLogFormatter {
    companion object {
        private val SENSITIVE_PATTERNS = listOf(
            "\"(\\w+KeyValue)\":\"[0-9A-Fa-f]+\"".toRegex(),
            "\"(\\w+_key_value)\":\"[0-9A-Fa-f]+\"".toRegex(),
        )
    }

    private val mapper: ObjectMapper

    init {
        mapper = ObjectMapper()
    }

    override fun format(content: MutableMap<String, Any>): String = mapper.writeValueAsString(content)

    @Throws(IOException::class)
    fun format(correlation: Correlation?, request: HttpRequest, response: HttpResponse?): String {
        return format(prepare(correlation!!, request, response!!))
    }
    override fun prepare(precorrelation: Precorrelation, request: HttpRequest): MutableMap<String, Any?> {

        val content: MutableMap<String, Any?> = LinkedHashMap()

        content["remote"] = request.remote
        content["method"] = request.method
        content["uri"] = request.requestUri
        content["host"] = request.host
        content["path"] = request.path

        if (!request.attributes.isEmpty()) content["attributes"] = request.attributes

        prepareHeaders(request).ifPresent { headers: Map<String?, List<String?>?>? ->
            content["headers"] = headers
        }
        prepareBody(request).ifPresent { body: Any? -> content["body"] = body }

        return hashMapOf("request" to content)
    }

    private fun prepare(correlation: Correlation, request: HttpRequest, response: HttpResponse): MutableMap<String, Any> {
        val content: MutableMap<String, Any> = LinkedHashMap()

        content["duration"] = correlation.duration.toMillis()
        content["status_code"] = response.status

        if (!response.attributes.isEmpty()) {
            content["attributes"] = response.attributes
        }

        prepareHeaders(response).ifPresent { headers: Map<String?, List<String?>?> ->
            content["headers"] = headers
        }
        prepareBody(response).ifPresent { body: Any -> content["body"] = body }

        val req = hashMapOf(
            "uri" to request.requestUri,
            "path" to request.path,
            "host" to request.host
        )
        return hashMapOf("response" to content, "request" to req)
    }

    @Throws(IOException::class)
    override fun prepareBody(message: HttpMessage): Optional<Any> {
        val body = message.bodyAsString
        return if (body.isEmpty()) {
            Optional.of<Any>(
                hashMapOf(
                    "bytes" to message.body.size,
                )
            )
        } else {
            Optional.of<Any>(
                hashMapOf(
                    "bytes" to message.body.size,
                    "content" to sanitize(body)
                )
            )
        }
    }

    private fun sanitize(body: String?): String? {
        if (body.isNullOrEmpty()) return body

        var sanitizedBody = body
        for (pattern in SENSITIVE_PATTERNS) {
            sanitizedBody = sanitizedBody?.replace(pattern) { matchResult ->
                val key = matchResult.groupValues[1]
                "\"$key\":\"[REDACTED]\""
            }
        }
        return sanitizedBody
    }
}