package ru.metrosoft.sputnikgate.logging

import kotlinx.coroutines.slf4j.MDCContext
import kotlinx.coroutines.withContext
import org.slf4j.MDC
import org.springframework.web.server.CoWebFilter
import org.springframework.web.server.CoWebFilterChain
import org.springframework.web.server.ServerWebExchange
import java.util.*
import java.util.regex.Pattern


class RequestIdWebFilter : CoWebFilter() {
    companion object {
        private const val REQUEST_ID_HEADER = "x-request-id"
        private val HEX_PATTERN: Pattern = Pattern.compile("(?<=\\G.{2})")
    }

    override suspend fun filter(exchange: ServerWebExchange, chain: CoWebFilterChain) {
        val requestId = exchange.request.headers.getFirst(REQUEST_ID_HEADER) ?: UUID.randomUUID().toString()

        val mdcMap = mutableMapOf("x-request-id" to requestId)

        exchange.request.uri.query?.split("&")?.forEach { value ->
            when {
                "uid=" in value -> {
                    val cardUid = value.substringAfter("uid=")
                    val cardSerialNo = cardUidToDec(cardUid)
                    cardSerialNo?.let { mdcMap["cardSerialNo"] = it.toString() }
                    mdcMap["cardUid"] = cardUid
                }

                "session=" in value -> {
                    mdcMap["session"] = value.substringAfter("session=")
                }
            }
        }

        exchange.attributes["mdc"] = mdcMap
        //AttributesStore.AttributesStore[] = mdcMap

        return withContext(MDCContext(mdcMap)) {
            try {
                chain.filter(exchange)
            } finally {
                MDC.clear()
            }
        }
    }

    private fun cardUidToDec(uid: String): Long? {
        return when (uid.length) {
            14 -> uid.toLong(16)
            8 -> {
                val b: Array<String> = HEX_PATTERN.split(uid)
                if (b.size == 4) {
                    (b[3] + b[2] + b[1] + b[0]).toLong(16)
                } else {
                    null
                }
            }
            else -> null
        }
    }
}