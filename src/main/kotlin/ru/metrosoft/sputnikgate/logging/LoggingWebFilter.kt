package ru.metrosoft.sputnikgate.logging

import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.core.io.buffer.DataBuffer
import org.springframework.core.io.buffer.DataBufferUtils
import org.springframework.core.io.buffer.DefaultDataBufferFactory
import org.springframework.http.server.reactive.ServerHttpRequest
import org.springframework.http.server.reactive.ServerHttpRequestDecorator
import org.springframework.http.server.reactive.ServerHttpResponse
import org.springframework.http.server.reactive.ServerHttpResponseDecorator
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.*
import java.util.concurrent.ThreadLocalRandom
import kotlin.time.TimeSource


class CachedRequest(delegate: ServerHttpRequest) : ServerHttpRequestDecorator(delegate) {
    private lateinit var _cache: ByteArray

    override fun getBody(): Flux<DataBuffer> {
        return Flux.just(DefaultDataBufferFactory.sharedInstance.wrap(_cache))
    }

    fun setCachedBody(dataBuffer: DataBuffer) {
        val bytes = ByteArray(dataBuffer.readableByteCount())
        dataBuffer.read(bytes)
        DataBufferUtils.release(dataBuffer) // Освобождаем буфер после чтения

        _cache = bytes
    }

    fun getCachedBody(): ByteArray {
        return _cache
    }
}

class CachedResponse(delegate: ServerHttpResponse) : ServerHttpResponseDecorator(delegate) {
    private var _cache: ByteArray? = null

    override fun writeWith(body: Publisher<out DataBuffer>): Mono<Void> {
        return super.writeWith(
            DataBufferUtils
                .join(body)
                .map { dataBuffer ->
                    val bytes = ByteArray(dataBuffer.readableByteCount())
                    dataBuffer.read(bytes)
                    DataBufferUtils.release(dataBuffer) // Освобождаем буфер после чтения

                    _cache = bytes

                    DefaultDataBufferFactory.sharedInstance.wrap(_cache!!)
                }
        )
    }

    fun getCachedBody(): ByteArray? {
        return _cache
    }
}

class LoggingWebFilter : WebFilter {

    companion object {
        val random: Random = ThreadLocalRandom.current()
        val formatter: ReactHttpLogFormatter = ReactHttpLogFormatter()
        val timeSource = TimeSource.Monotonic
    }

    private val log = LoggerFactory.getLogger(LoggingWebFilter::class.java)
    override fun filter(exchange: ServerWebExchange, chain: WebFilterChain): Mono<Void> {
        val requestedAt = timeSource.markNow()

        val spanId = java.lang.Long.toHexString(random.nextLong() or Long.MIN_VALUE)

        val request = CachedRequest(exchange.request)
        val response = CachedResponse(exchange.response)

        return DataBufferUtils.join(request.delegate.body)
            .map { bodyBuffer ->
                request.setCachedBody(bodyBuffer)

                val oldContext = MDC.getCopyOfContextMap()
                try {
                    exchange.fillMdcContext()
                    MDC.put("http", formatter.format(request))
                    MDC.put("span.id", spanId)
                    log.info("Start: {} {}", request.method, request.uri)
                } finally {
                    MDC.setContextMap(oldContext)
                }
            }
            .then(chain.filter(exchange.mutate().request(request).response(response).build()))
            .doOnSuccess {
                val oldContext = MDC.getCopyOfContextMap()
                try {
                    exchange.fillMdcContext()

                    MDC.put("http", formatter.format(timeSource.markNow() - requestedAt, request, response))
                    MDC.put("span.id", spanId)
                    log.info(
                        "Stop: {} {} Status: {} ({})",
                        request.method,
                        request.uri,
                        response.statusCode,
                        response.statusCode?.toString() ?: "-"
                    )
                } finally {
                    MDC.setContextMap(oldContext)
                }
            }
            .doOnError { ex ->
                val oldContext = MDC.getCopyOfContextMap()
                try {
                    exchange.fillMdcContext()
                    MDC.put("http", formatter.format(timeSource.markNow() - requestedAt, request, response))
                    MDC.put("span.id", spanId)
                    MDC.put("exception.message", ex.message)
                    log.info(
                        "Stop: {} {} Status: {} ({})",
                        request.method,
                        request.uri,
                        response.statusCode,
                        response.statusCode?.toString() ?: "-"
                    )
                } finally {
                    MDC.setContextMap(oldContext)
                }
            }
    }
}

