package ru.metrosoft.sputnikgate.input.model

import io.swagger.v3.oas.annotations.media.Schema


data class ReadBlock(
    @field:Schema(
        description = "Сквозной номер блока (начиная с нуля)"
    )
    var block: Int,

    @field:Schema(
        description = "Тип ключа чтения, A или B. По умолчанию A"
    )
    var keyType: String = "A",

    @field:Schema(
        description = "Идентификатор ключа чтения в SAM"
    )
    var keyId: Int?,

    @field:Schema(
        description = "Версия ключа чтения в SAM. По умолчанию 0"
    )
    var keyVersion: Int? = 0,

    @field:Schema(
        description = "Значение ключа чтения"
    )
    var keyValue: String?
)

data class GetKeys(
    @field:Schema(
        description = "Номер заказа"
    )
    var blocks: List<ReadBlock>?
)

data class ReadBitmapBlock(
    @field:Schema(
        description = "Сквозной номер блока (начиная с нуля)"
    )
    var block: Int,

    @field:Schema(
        description = "Содержимое блока в виде строки в шестнадцатеричном представлении"
    )
    var data: String
)

data class RequestUnwritten(
    @field:Schema(
        description = "Прочитанные блоки"
    )
    var items: List<ReadBitmapBlock>
)

data class WriteBitmapBlock(
    @field:Schema(
        description = "Сквозной номер блока (начиная с нуля)"
    )
    var block: Int,

    @field:Schema(
        description = "Идентификатор ключа записи в SAM"
    )
    var writeKeyId: Int,

    @field:Schema(
        description = "Тип ключа записи, A или B. По умолчанию A"
    )
    var writeKeyType: String = "A",

    @field:Schema(
        description = "Версия ключа записи в SAM. По умолчанию 0"
    )
    var writeKeyVersion: Int,

    @field:Schema(
        description = "Значение ключа записи"
    )
    var writeKeyValue: String,

    @field:Schema(
        description = "Содержимое блока в виде строки в шестнадцатеричном представлении"
    )
    var data: String,

    @field:Schema(
        description = "Идентификатор ключа чтения в SAM"
    )
    var readKeyId: Int?,

    @field:Schema(
        description = "Тип ключа чтения, A или B. По умолчанию A"
    )
    var readKeyType: String = "A",

    @field:Schema(
        description = "Версия ключа чтения в SAM. По умолчанию 0"
    )
    var readKeyVersion: Int? = 0
)

data class RequestUnwrittenResponse(
    @field:Schema(
        description = "Печатный номер"
    )
    var num: String,

    @field:Schema(
        description = "Идентификатор сессии"
    )
    var sessionId: String,

    @field:Schema(
        description = "Блоки для записи на носитель"
    )
    var blocks: List<WriteBitmapBlock>
)

data class Blocked(
    @field:Schema(
        description = "Дата/время попадания носителя в стоп-лист (UTC)"
    )
    var time: String,

    @field:Schema(
        description = "Причина постановки в стоп-лист"
    )
    var reason: String
)

data class CardInfo(
    @field:Schema(
        description = "Печатный номер"
    )
    var num: String,

    @field:Schema(
        description = "UID носителя"
    )
    var uid: String,

    @field:Schema(
        description = "Статус [OK, BLOCKED, RESTORED, EXPIRED]"
    )
    var status: String,

    @field:Schema(
        description = "Текущие билеты"
    )
    var tickets: List<TroikaTicket>,

    @field:Schema(
        description = "Отложенные билеты"
    )
    var unwrittens: List<TroikaTicket>,

    @field:Schema(
        description = "Блокировка"
    )
    var blocked: Blocked
)