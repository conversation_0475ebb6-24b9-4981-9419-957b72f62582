package ru.metrosoft.sputnikgate.input

import kotlinx.coroutines.currentCoroutineContext
import org.openapitools.client.infrastructure.ClientError
import org.openapitools.client.infrastructure.ClientException
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.HttpStatusCode
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.reactive.result.method.annotation.ResponseEntityExceptionHandler
import org.springframework.web.server.MissingRequestValueException
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.ServerWebInputException
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.input.ApiError.Companion.technicalError
import ru.metrosoft.sputnikgate.input.security.auth.UnauthorizedException
import ru.metrosoft.sputnikgate.logging.ExceptionUtils
import ru.metrosoft.sputnikgate.logging.putMdcValue
import java.lang.Exception
import java.net.SocketTimeoutException


@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice
class RestExceptionHandler : ResponseEntityExceptionHandler() {

    @ExceptionHandler(ClientException::class)
    suspend fun handleCustomErrorException(
        ex: ClientException
    ): Mono<ResponseEntity<ApiError>> {

        val context = currentCoroutineContext()

        val l = ex.response as ClientError<*>

        return Mono.just(buildResponseEntity(ApiError(HttpStatus.BAD_REQUEST, l.body?.title.orEmpty(), ex, ApiError.Error(l.body?.error?.code.orEmpty(), l.body?.error?.comment.orEmpty()))))
    }

    @ExceptionHandler(UnauthorizedException::class)
    fun handleCustomErrorException(
        ex: UnauthorizedException
    ): Mono<ResponseEntity<ApiError>> {

        return Mono.just(buildResponseEntity(ApiError(HttpStatus.UNAUTHORIZED, "", ex, ApiError.Error("TECHNICAL_ERROR"))))
    }

    @ExceptionHandler(SocketTimeoutException::class)
    fun handleTimeoutErrorException(ex: SocketTimeoutException): Mono<ResponseEntity<ApiError>> {
        return Mono.just(buildResponseEntity(ApiError(HttpStatus.GATEWAY_TIMEOUT, "Таймаут ожидания ответа сервера", ex, ApiError.Error("TECHNICAL_ERROR"))))
    }

    override fun handleMissingRequestValueException(
        ex: MissingRequestValueException,
        headers: HttpHeaders,
        status: HttpStatusCode,
        exchange: ServerWebExchange
    ): Mono<ResponseEntity<Any>> = wrongRequest(ex, exchange)

    override fun handleServerWebInputException(
        ex: ServerWebInputException,
        headers: HttpHeaders,
        status: HttpStatusCode,
        exchange: ServerWebExchange
    ): Mono<ResponseEntity<Any>> = wrongRequest(ex, exchange)

    private fun wrongRequest(
        ex: Exception,
        exchange: ServerWebExchange
    ): Mono<ResponseEntity<Any>> {
        val cause = ex.cause as Exception
        val sb = StringBuilder(2048)
        ExceptionUtils.append(sb, cause)
        exchange.putMdcValue("exception", sb.toString())

        val apiError = ApiError(HttpStatus.BAD_REQUEST, "Ошибка в запросе", ex, ApiError.Error("WRONG_REQUEST"))
        return Mono.just(ResponseEntity<Any>(apiError, apiError.status))
    }

    override fun handleExceptionInternal(
        ex: Exception,
        body: Any?,
        headers: HttpHeaders?,
        status: HttpStatusCode,
        exchange: ServerWebExchange
    ): Mono<ResponseEntity<Any>> {
        val sb = StringBuilder(2048)
        ExceptionUtils.append(sb, ex)
        exchange.putMdcValue("exception", sb.toString())

        val apiError = ApiError(HttpStatus.BAD_REQUEST, technicalError.comment!!, ex, technicalError)
        return Mono.just(ResponseEntity<Any>(apiError, apiError.status))
    }

    //    @ExceptionHandler(Exception::class)
//    fun handleException(ex: Exception): Mono<ResponseEntity<ApiError>> =
//        Mono.just(buildResponseEntity(ApiError(HttpStatus.BAD_REQUEST, "Unexpected error", ex, ApiError.Error("TECHNICAL_ERROR"))))
//
    private fun buildResponseEntity(apiError: ApiError): ResponseEntity<ApiError> {
        return ResponseEntity<ApiError>(apiError, apiError.status)
    }

}