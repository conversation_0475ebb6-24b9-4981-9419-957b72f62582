package ru.metrosoft.sputnikgate.input

import com.fasterxml.jackson.annotation.JsonInclude
import org.springframework.http.HttpStatus
import java.time.ZoneId
import java.time.ZonedDateTime


class ApiError {
    companion object {
        val technicalError: Error = ApiError.Error(
            "TECHNICAL_ERROR",
            "Системная ошибка, необходимо повторить запрос согласно политике повторных сообщений"
        )
    }

    val status: HttpStatus

//    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd-MM-yyyy hh:mm:ss")
    private val timestamp: ZonedDateTime = ZonedDateTime.now(ZoneId.systemDefault())

    val message: String

    private var debugMessage: String? = null

    var error: Error = technicalError

    constructor(status: HttpStatus, ex: Throwable?) {
        this.status = status
        this.message = "Unexpected error"
        this.debugMessage = ex?.localizedMessage
    }

    constructor(status: HttpStatus, message: String, ex: Throwable?) {
        this.status = status
        this.message = message
        this.debugMessage = ex?.localizedMessage
    }

    constructor(status: HttpStatus, message: String, ex: Throwable?,  error: Error) {
        this.status = status
        this.message = message
        this.debugMessage = ex?.localizedMessage
        this.error = error
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    data class Error(val code: String, val comment: String? = null)
}