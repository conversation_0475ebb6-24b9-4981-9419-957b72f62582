package ru.metrosoft.sputnikgate.input

import com.google.common.cache.Cache
import com.google.common.cache.CacheBuilder
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.Parameter
import org.openapitools.client.infrastructure.ClientError
import org.openapitools.client.infrastructure.ClientException
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import org.springframework.web.server.ServerWebExchange
import org.zalando.logbook.Logbook
import ru.metrosoft.sputnikgate.exceptions.UnknownSessionException
import ru.metrosoft.sputnikgate.logging.putMdcValue
import ru.metrosoft.sputnikgate.model.AgentPayment
import ru.metrosoft.sputnikgate.model.AgentPaymentType
import ru.metrosoft.sputnikgate.model.AgentRequest
import ru.metrosoft.sputnikgate.output.nbs.api.BitmapTicketsPaymentApi
import ru.metrosoft.sputnikgate.output.nbs.api.ExternalInfoApi
import ru.metrosoft.sputnikgate.output.nbs.api.TicketsPaymentApi
import ru.metrosoft.sputnikgate.output.nbs.model.*
import ru.metrosoft.sputnikgate.service.AgentPaymentService
import ru.metrosoft.sputnikgate.service.AgentRequestService
import ru.metrosoft.sputnikgate.service.ApiAgentNBSService
import ru.metrosoft.sputnikgate.utils.toAgent
import java.security.Principal
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * TroikaRefill - Пополнение карты Тройка
 */
@RestController
@RequestMapping("/troika/v1")
class TroikaRefillRestApi(
    private val agentRequestService: AgentRequestService,
    private val agentPaymentService: AgentPaymentService,
    private val agentNBSService: ApiAgentNBSService,
    private val logbook: Logbook
) {
    companion object {
        const val GET_KEYS = "GetKeys"
        const val REQUEST_PAY_WRITE = "RequestPaywrite"
        const val PROCESS_PAY_WRITE = "ProcessPaywrite"
        const val PROCESS_UNWRITTEN = "ProcessUnwritten"
        const val NOTIFY_WRITE_RESULT = "NotifyWriteResult"
        const val REQUEST_PAY = "RequestPay"
        const val REQUEST_PAY_BY_UID = "RequestPayByUid"
        const val PROCESS_PAY = "ProcessPay"
        const val GET_UID = "GetUid"
        const val GET_CARD_BY_UID = "GetCardByUid"

        @JvmStatic
        val bitmapClients: Cache<UUID, BitmapTicketsPaymentApi> = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.SECONDS)
            .build()
        @JvmStatic
        val ticketClients: Cache<UUID, TicketsPaymentApi> = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.SECONDS)
            .build()
        @JvmStatic
        val externalInfoClients: Cache<UUID, ExternalInfoApi> = CacheBuilder.newBuilder()
            .expireAfterWrite(5, TimeUnit.SECONDS)
            .build()
    }

    @Operation(
        summary = "Запрос параметров чтения блоков Носителя.",
        description = "ЦОТТ сообщает Агенту, какие блоки и какими ключами из SAM следует считать с Носителя, чтобы работать с ЭБ.",
        operationId = "BitmapTicketsPayment_GetKeys",
        tags = ["BitmapTicketsPayment"]
    )
    @PostMapping(path = ["/get_keys"])
    suspend fun troikaGetKeys(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "UID Носителя в шестнадцатеричном представлении.",
            example = "AABB0011",
        ) uid: String,
        p: Principal,
        exchange: ServerWebExchange
    ): ResponseEntity<GetKeysResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = GET_KEYS
        )

        val bitmapTicketPaymentAPI = bitmapClients.get(agentNBS.id!!) {
            agentNBS.bitmapTicketsPaymentApi(logbook)
        }

        try {
            return bitmapTicketPaymentAPI.bitmapTicketsPaymentGetKeys(uid).let {
                agentRequest.status = 200

                ResponseEntity.ok().body(it)
            }
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }
    }

    @Operation(
        summary = "Запрос на прямое пополнение.",
        description = "" +
                "Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.\n" +
                "\n" +
                "При наличии Носителя в реестре замененных возвращается ошибка, в поле replaced_num возвращается печатный номер Носителя, на который был заменен Носитель из запроса.\n" +
                "\n" +
                "При успешном выполнении этого запроса, начинается транзакция продажи с записью. В ответе возвращается номер транзакции (session). Его следует указывать в последующих сообщениях сценария.\n" +
                "\n" +
                "Если запрос request_payment сообщает о незаписанных отложенных ЭБ, следует, начав сценарий с начала, выполнить запись отложенных (get_keys, request_unwritten, ..), а потом заново начать сценарий продажи с записью.",
        operationId = "BitmapTicketsPayment_RequestPayment",
        tags = ["BitmapTicketsPayment"]
    )
    @PostMapping(path = ["/request_payment"], consumes = ["application/json"], produces = ["application/json"])
    suspend fun requestPayment(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "UID Носителя в шестнадцатеричном представлении.",
            example = "00112233445566"
        ) uid: String,
        @RequestBody(required = true) @Parameter(
            required = true,
            description = "Данные прочитанные с носителя"
        ) body: RequestPaywriteRequest,
        p: Principal,
        exchange: ServerWebExchange
    ): ResponseEntity<RequestPaywriteResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        exchange.putMdcValue("cardUid", uid)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = REQUEST_PAY_WRITE
        )

        val bitmapTicketPaymentAPI = bitmapClients.get(agentNBS.id!!) {
            agentNBS.bitmapTicketsPaymentApi(logbook)
        }

        try {
            agentRequest.status = 200
            val response = bitmapTicketPaymentAPI.bitmapTicketsPaymentRequestPaywrite(uid, body)

            exchange.putMdcValue("session", response.session)

            agentPaymentService.findAgentPayment(response.session).fold(
                {
                    agentPaymentService.persist(
                        AgentPayment(
                            _id = response.session,
                            type = AgentPaymentType.INFO,
                            ts = LocalDateTime.now(),
                            agentId = agent.id!!,
                            agentNBSId = agent.agentNBSId!!,
                            cardNum = response.num!!,
                        )
                    )
                },
                {}
            )

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }

    }

    @Operation(
        summary = "Запрос на прямое пополнение.",
        description = "" +
                "Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.\n" +
                "\n" +
                "При наличии Носителя в реестре замененных возвращается ошибка, в поле replaced_num возвращается печатный номер Носителя, на который был заменен Носитель из запроса.\n" +
                "\n" +
                "При успешном выполнении этого запроса, начинается транзакция продажи с записью. В ответе возвращается номер транзакции (session). Его следует указывать в последующих сообщениях сценария.\n" +
                "\n" +
                "Если запрос request_payment сообщает о незаписанных отложенных ЭБ, следует, начав сценарий с начала, выполнить запись отложенных (get_keys, request_unwritten, ..), а потом заново начать сценарий продажи с записью.",
        operationId = "BitmapTicketsPayment_RequestPaywrite",
        tags = ["BitmapTicketsPayment"],
        hidden = true,
    )
    @PostMapping(path = ["/request_paywrite"], consumes = ["application/json"], produces = ["application/json"])
    suspend fun requestPaywrite(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "UID Носителя в шестнадцатеричном представлении.",
            example = "00112233445566"
        ) uid: String,
        @RequestBody(required = true) @Parameter(
            required = true,
            description = "Данные прочитанные с носителя"
        ) body: RequestPaywriteRequest,
        p: Principal,
        exchange: ServerWebExchange
    ): ResponseEntity<RequestPaywriteResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        exchange.putMdcValue("cardUid", uid)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = REQUEST_PAY_WRITE
        )

        val bitmapTicketPaymentAPI = bitmapClients.get(agentNBS.id!!) {
            agentNBS.bitmapTicketsPaymentApi(logbook)
        }

        try {
            agentRequest.status = 200
            val response = bitmapTicketPaymentAPI.bitmapTicketsPaymentRequestPaywrite(uid, body)

            exchange.putMdcValue("session", response.session)

            agentPaymentService.findAgentPayment(response.session).fold(
                {
                    agentPaymentService.persist(
                        AgentPayment(
                            _id = response.session,
                            type = AgentPaymentType.INFO,
                            ts = LocalDateTime.now(),
                            agentId = agent.id!!,
                            agentNBSId = agent.agentNBSId!!,
                            cardNum = response.num!!,
                        )
                    )
                },
                {}
            )

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }

    }

    @Operation(
        summary = "Запрос на запись отложенного пополнения.",
        description = "" +
                "Агент сообщает ЦОТТ считанный с карты битмап. ЦОТТ, по итогам анализа битмапа и используя другие данные, сообщает Агенту, какие ЭБ записаны на карту, ожидают записи или просрочены.\n" +
                "\n" +
                "При наличии Носителя в реестре замененных возвращается ошибка, в поле replaced_num возвращается печатный номер Носителя, на который был заменен Носитель из запроса.\n" +
                "\n" +
                "В ответе возвращается номер транзакции (session). Его следует указывать в последующих сообщениях сценария.\n" +
                "\n" +
                "Если запрос request_unwritten сообщает о незаписанных отложенных ЭБ, следует выполнить запись отложенных.",
        operationId = "BitmapTicketsPayment_RequestUnwritten",
        tags = ["BitmapTicketsPayment"]
    )
    @PostMapping(path = ["/request_unwritten"], consumes = ["application/json"], produces = ["application/json"])
    suspend fun requestUnwritten(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "UID Носителя в шестнадцатеричном представлении.",
            example = "00112233445566"
        ) uid: String,
        @RequestBody(required = true) @Parameter(
            required = true,
            description = "Данные прочитанные с носителя"
        ) body: RequestUnwrittenRequest,
        p: Principal,
        exchange: ServerWebExchange
    ): ResponseEntity<RequestUnwrittenResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        exchange.putMdcValue("cardUid", uid)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = REQUEST_PAY_WRITE
        )

        val bitmapTicketPaymentAPI = bitmapClients.get(agentNBS.id!!) {
            agentNBS.bitmapTicketsPaymentApi(logbook)
        }

        try {
            agentRequest.status = 200
            val response = bitmapTicketPaymentAPI.bitmapTicketsPaymentRequestUnwritten(uid, body)

            exchange.putMdcValue("session", response.session)

            agentPaymentService.findAgentPayment(response.session).fold(
                {
                    agentPaymentService.persist(
                        AgentPayment(
                            _id = response.session,
                            type = AgentPaymentType.INFO,
                            ts = LocalDateTime.now(),
                            agentId = agent.id!!,
                            agentNBSId = agent.agentNBSId!!,
                            cardNum = response.num!!,
                        )
                    )
                },
                {}
            )

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }

    }

    @Operation(
        summary = "Подтверждение прямого пополнение.",
        description = "Фиксируется оплачиваемая услуга и выдаются блоки, которые следует записать на Носитель. Завершает транзакцию продажи с записью на Носитель.\n" +
                "\n" +
                "Этот запрос можно отправлять только после успешного ответа на запрос /request_paywrite. Значение session должно совпадать со значением session из ответа на него.\n" +
                "\n" +
                "Атрибут status_code для соответствующего билета должен быть равен OK. Если status_code для соответствующего билета равен UNWRITTEN, то следует вызывать /request_unwritten, чтобы выполнить сценарий \"Запись Удаленного пополнения\".\n" +
                "\n" +
                "Для следующих параметров, если агент их указывает в agent_params, следует использовать предопределенные имена:\n" +
                "\n" +
                "pay_type - способ оплаты:\n" +
                "CASH - наличные;\n" +
                "CARD - банковская карта;\n" +
                "PHONE - мобильный телефон;\n" +
                "INTERNET - электронная платежная система;\n" +
                "pos - номер точки продаж\n" +
                "Рекомендуется также заполнить:\n" +
                "\n" +
                "phone - номер телефона плательщика, если он известен. 11 цифр без разделителей.",
        operationId = "BitmapTicketsPayment_ProcessPaywrite",
        tags = ["BitmapTicketsPayment"]
    )
    @PostMapping(path = ["/refill/process_paywrite"])
    suspend fun troikaRefillProcessPaywrite(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "Сессия."
        ) session: String,
        @RequestBody(required = true) @Parameter(
            required = true,
            description = "Запрос."
        ) request: ProcessPaywriteRequest, p: Principal
    ): ResponseEntity<ProcessPaywriteResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = PROCESS_PAY_WRITE
        )

        val bitmapTicketPaymentAPI = bitmapClients.get(agentNBS.id!!) {
            agentNBS.bitmapTicketsPaymentApi(logbook)
        }

        return agentPaymentService.findAgentPayment(session).fold(
            {
                throw UnknownSessionException()
            },
            { payment ->
                try {
                    agentRequest.status = 200
                    val body = ResponseEntity.ok()
                        .body(bitmapTicketPaymentAPI.bitmapTicketsPaymentProcessPaywrite(session, request))

                    payment.type = AgentPaymentType.PAYWRITE
                    payment.amount = body.body?.service?.sumOf { it.sum!! }!!
                    payment.payStatus = true
                    payment.status = true
                    payment.processTime = body.body?.time?.atZoneSameInstant(ZoneId.systemDefault())?.toLocalDateTime()

                    payment.services = body.body?.service?.map {
                        ru.metrosoft.sputnikgate.model.AgentPaymentService(
                            session,
                            it.id!!,
                            it.name,
                            it.sum!!
                        )
                    }?.toList() ?: emptyList()

                    return@fold body
                } catch (e: ClientException) {
                    val error = e.response as ClientError<*>

                    agentRequest.status = error.statusCode
                    agentRequest.errorMessage = error.body?.title
                    agentRequest.errorCode = error.body?.error?.code
                    agentRequest.traceId = error.body?.traceId

                    if (!payment.status) {
                        payment.status = false
                        payment.statusCode = error.statusCode
                        payment.errorMessage = error.body?.title
                        payment.errorCode = error.body?.error?.code
                        payment.traceId = error.body?.traceId
                    }

                    throw e
                } finally {
                    agentRequestService.saveAgentRequest(agentRequest)
                    agentPaymentService.persist(payment)
                }
            }
        )
    }

    @Operation(
        summary = "Подтверждение записи удалённого пополнения.",
        description = "Выдаются блоки отложенного ЭБ, которые следует записать на Носитель.\n" +
                "\n" +
                "Завершает транзакцию записи отложенного ЭБ.\n" +
                "\n" +
                "Этот запрос можно отправлять только после успешного ответа на запрос request_paywrite. Значение session должно совпадать со значением session из ответа на него.\n" +
                "\n" +
                "При отсутствии билетов, которые можно записать в данный момент, возвращается ошибка NO_TICKETS_TO_WRITE либо NO_ROOM_TO_WRITE_TICKET. Эту ошибку ЦОТТ может вернуть, даже если есть ожидающие записи отложенные (например, если текущий остаток на носителе не позволяет записать ожидающий записи отложенный).",
        operationId = "BitmapTicketsPayment_ProcessUnwritten",
        tags = ["BitmapTicketsPayment"]
    )
    @PostMapping(path = ["/refill/process_unwritten"])
    suspend fun troikaRefillProcessUnwitten(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "Сессия."
        ) session: String, p: Principal
    ): ResponseEntity<ProcessUnwrittenResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = p.toAgent().id.toString(),
            ts = LocalDateTime.now(),
            type = PROCESS_UNWRITTEN
        )

        val bitmapTicketPaymentAPI = bitmapClients.get(agentNBS.id!!) {
            agentNBS.bitmapTicketsPaymentApi(logbook)
        }

        return agentPaymentService.findAgentPayment(session).fold(
            {
                throw UnknownSessionException()
            },
            { payment ->
                if (payment.type == AgentPaymentType.INFO) {
                    payment.type = AgentPaymentType.WRITE
                }

                try {
                    agentRequest.status = 200
                    val body =
                        ResponseEntity.ok().body(bitmapTicketPaymentAPI.bitmapTicketsPaymentProcessUnwritten(session))

                    payment.amount = body.body?.service?.sumOf { it.sum!! }!!
                    payment.writeStatus = true
                    payment.status = true
                    payment.processTime = body.body?.time?.atZoneSameInstant(ZoneId.systemDefault())?.toLocalDateTime()

                    payment.services = body.body?.service?.map {
                        ru.metrosoft.sputnikgate.model.AgentPaymentService(
                            session,
                            it.id!!,
                            it.name,
                            it.sum!!
                        )
                    }?.toList() ?: emptyList()

                    return@fold body
                } catch (e: ClientException) {
                    val error = e.response as ClientError<*>

                    agentRequest.status = error.statusCode
                    agentRequest.errorMessage = error.body?.title
                    agentRequest.errorCode = error.body?.error?.code
                    agentRequest.traceId = error.body?.traceId

                    if (!payment.status) {
                        payment.status = false
                        payment.statusCode = error.statusCode
                        payment.errorMessage = error.body?.title
                        payment.errorCode = error.body?.error?.code
                        payment.traceId = error.body?.traceId
                    }

                    throw e
                } finally {
                    agentRequestService.saveAgentRequest(agentRequest)
                    agentPaymentService.persist(payment)
                }

            }
        )
    }

    @Operation(
        summary = "Уведомление о результате записи на Носитель.",
        description = "Агент сообщает ЦОТТ о результате записи на Носитель.\n" +
                "\n" +
                "Запрос должен отправляться только в случае успешного ответа сервера на process_paywrite или process_unwritten. Значение session должно совпадать со значением session из ответа на него.",
        operationId = "BitmapTicketsPayment_NotifyWriteResult",
        tags = ["BitmapTicketsPayment"]
    )
    @PostMapping(path = ["/notify_write_result"])
    suspend fun troikaWriteStatus(
        @RequestParam(required = true) @Parameter(
            required = true,
            example = "7461fc18-cdd6-44ba-b4fa-0c11b658ec99",
            description = "Сессия."
        ) session: String,
        @RequestBody(required = true) @Parameter(
            required = true,
            description = "Запрос."
        ) request: NotifyWriteResultRequest, p: Principal
    ): ResponseEntity<NotifyWriteResultResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = NOTIFY_WRITE_RESULT
        )

        val bitmapTicketPaymentAPI = bitmapClients.get(agentNBS.id!!) {
            agentNBS.bitmapTicketsPaymentApi(logbook)
        }

        return agentPaymentService.findAgentPayment(session).fold(
            {
                throw UnknownSessionException()
            },
            { payment ->
                try {
                    agentRequest.status = 200
                    val body = ResponseEntity.ok()
                        .body(bitmapTicketPaymentAPI.bitmapTicketsPaymentNotifyWriteResult(session, request))

                    payment.writeStatus = checkWriteStatus(request.writeResults)
                    payment.status = true
                    payment.notifyTime = body.body?.time?.atZoneSameInstant(ZoneId.systemDefault())?.toLocalDateTime()

                    return@fold body
                } catch (e: ClientException) {
                    val error = e.response as ClientError<*>

                    agentRequest.status = error.statusCode
                    agentRequest.errorMessage = error.body?.title
                    agentRequest.errorCode = error.body?.error?.code
                    agentRequest.traceId = error.body?.traceId

                    payment.writeStatus = false
                    payment.statusCode = error.statusCode
                    payment.errorMessage = error.body?.title
                    payment.errorCode = error.body?.error?.code
                    payment.traceId = error.body?.traceId

                    throw e
                } finally {
                    agentRequestService.saveAgentRequest(agentRequest)
                    agentPaymentService.persist(payment)
                }
            }
        )
    }

    @Operation(
        summary = "Запрос удаленного пополнения.",
        operationId = "TicketsPayment_request_pay",
        tags = ["TicketsPayment"]
    )
    @PostMapping(path = ["/request_pay"])
    suspend fun requestPay(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "Транспортный номер носителя.."
        ) num: String, p: Principal
    ): ResponseEntity<AgentRequestPayResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = REQUEST_PAY
        )

        val ticketsPaymentApi = ticketClients.get(agentNBS.id) {
            agentNBS.ticketsPaymentApi(logbook)
        }

        try {
            val response = ticketsPaymentApi.ticketsPaymentRequestPay(num)
            agentRequest.status = 200

            agentPaymentService.findAgentPayment(response.session).fold(
                {
                    agentPaymentService.persist(
                        AgentPayment(
                            _id = response.session,
                            type = AgentPaymentType.INFO,
                            ts = LocalDateTime.now(),
                            agentId = agent.id!!,
                            cardNum = num,
                            agentNBSId = agentNBS.id
                        )
                    )
                },
                {

                }
            )

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }
    }

    @Operation(
        summary = "Запрос удаленного пополнения по UID.",
        operationId = "TicketsPayment_request_pay",
        tags = ["TicketsPayment"]
    )
    @PostMapping(path = ["/request_pay_by_uid"])
    suspend fun requestPayByUid(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "UID носителя."
        ) uid: String, p: Principal
    ): ResponseEntity<AgentRequestPayResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = REQUEST_PAY_BY_UID
        )

        val ticketsPaymentApi = ticketClients.get(agentNBS.id!!) {
            agentNBS.ticketsPaymentApi(logbook)
        }

        try {
            val response = ticketsPaymentApi.ticketsPaymentRequestPayByUid(uid)
            agentRequest.status = 200

            agentPaymentService.findAgentPayment(response.session).fold(
                {
                    agentPaymentService.persist(
                        AgentPayment(
                            _id = response.session,
                            type = AgentPaymentType.INFO,
                            ts = LocalDateTime.now(),
                            agentId = agent.id!!,
                            cardNum = response.cardSerialNumber?.toString().orEmpty(),
                            agentNBSId = agentNBS.id
                        )
                    )
                },
                {

                }
            )

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }
    }

    @Operation(
        summary = "Подтвержение удаленного пополнения.",
        operationId = "TicketsPayment_process_pay",
        tags = ["TicketsPayment"]
    )
    @PostMapping(path = ["/process_pay"])
    suspend fun processPay(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "Сессия."
        ) session: String,
        @RequestBody(required = true) @Parameter(
            required = true,
            description = "Запрос."
        ) request: AgentProcessPayRequest,
        p: Principal
    ): ResponseEntity<AgentProcessPayResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = PROCESS_PAY
        )

        val ticketsPaymentApi = ticketClients.get(agentNBS.id!!) {
            agentNBS.ticketsPaymentApi(logbook)
        }

        try {
            agentRequest.status = 200
            val response = ticketsPaymentApi.ticketsPaymentProcessPay(session, request)

            agentPaymentService.findAgentPayment(session).fold(
                {
                    agentPaymentService.persist(
                        AgentPayment(
                            _id = session,
                            type = AgentPaymentType.PAY,
                            ts = LocalDateTime.now(),
                            agentId = agent.id!!,
                            cardNum = "unknown",
                            agentNBSId = agentNBS.id
                        )
                    )
                },
                {
                    agentPaymentService.persist(
                        it.copy(
                            type = AgentPaymentType.PAY,
                            amount = request.sum,
                            status = true,
                            agentNBSId = agentNBS.id,
                            payStatus = true,
                            writeStatus = false,
                            _isNew = false
                        )
                    )
                }
            )

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }
    }

    @Operation(
        summary = "Проверка доступности канала связи.",
        operationId = "TicketsPayment_ping",
        tags = ["ExternalInfo"]
    )
    @GetMapping(path = ["/ping"])
    suspend fun ping(
        p: Principal
    ): ResponseEntity<String> {
        return ResponseEntity.ok().body(ZonedDateTime.now().toString())
    }

    @Operation(
        summary = "Запрос UID носителя по его транспортному номеру.",
        operationId = "TicketsPayment_get_uid",
        tags = ["ExternalInfo"]
    )
    @PostMapping(path = ["/get_uid"])
    suspend fun getUid(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "Печатный номер носителя."
        ) num: String,
        p: Principal
    ): ResponseEntity<AgentGetUidResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = GET_UID
        )

        val externalInfoApi = externalInfoClients.get(agentNBS.id!!) {
            agentNBS.externalInfoApi(logbook)
        }

        try {
            val response = externalInfoApi.externalInfoGetUid(num)
            agentRequest.status = 200

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }
    }

    @Operation(
        summary = "Запрос информации о носителе по его UID.",
        operationId = "TicketsPayment_get_card_by_uid",
        tags = ["ExternalInfo"]
    )
    @PostMapping(path = ["/get_card_by_uid"])
    suspend fun getCardByUid(
        @RequestParam(required = true) @Parameter(
            required = true,
            description = "UID носителя."
        ) uid: String,
        p: Principal
    ): ResponseEntity<GetCardResponse> {
        val agent = p.toAgent()
        val agentNBS = agentNBSService.getAgent(agent.agentNBSId!!)

        val agentRequest = AgentRequest(
            agentId = agent.id.toString(),
            ts = LocalDateTime.now(),
            type = GET_CARD_BY_UID
        )

        val externalInfoApi = externalInfoClients.get(agentNBS.id!!) {
            agentNBS.externalInfoApi(logbook)
        }

        try {
            val response = externalInfoApi.externalInfoGetCardByUid(uid)
            agentRequest.status = 200

            return ResponseEntity.ok().body(response)
        } catch (e: ClientException) {
            val error = e.response as ClientError<*>

            agentRequest.status = error.statusCode
            agentRequest.errorMessage = error.body?.title
            agentRequest.errorCode = error.body?.error?.code
            agentRequest.traceId = error.body?.traceId

            throw e
        } finally {
            agentRequestService.saveAgentRequest(agentRequest)
        }
    }

    private fun checkWriteStatus(results: List<AgentWriteResult>): Boolean {
        return results.all { it.result.equals("OK", true) }
    }
}