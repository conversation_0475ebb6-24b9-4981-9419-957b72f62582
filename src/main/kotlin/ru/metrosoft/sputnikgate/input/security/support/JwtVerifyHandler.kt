package ru.metrosoft.sputnikgate.input.security.support

import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.input.security.auth.UnauthorizedException
import java.util.*


class JwtVerifyHandler {

    fun check(accessToken: String): Mono<VerificationResult> {
        return Mono.just(verify(accessToken))
            .onErrorResume { e: Throwable ->
                Mono.error(
                    UnauthorizedException(e.message)
                )
            }
    }

    private fun verify(token: String): VerificationResult {

        try {
            UUID.fromString(token)

            return VerificationResult(token)
        } catch (e: IllegalArgumentException) {
            throw UnauthorizedException("Неверный формат токена авторизации")
        }
    }

}

class VerificationResult(var token: String)
