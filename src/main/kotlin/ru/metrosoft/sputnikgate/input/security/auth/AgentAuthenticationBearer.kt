package ru.metrosoft.sputnikgate.input.security.auth

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken
import org.springframework.security.core.Authentication
import org.springframework.security.core.authority.SimpleGrantedAuthority
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.input.security.support.VerificationResult


object AgentAuthenticationBearer {
    fun create(verificationResult: VerificationResult): Mono<Authentication> {

        val principal = AgentPrincipal(verificationResult.token)

        return Mono.justOrEmpty(UsernamePasswordAuthenticationToken(principal, null, arrayListOf(SimpleGrantedAuthority("AGENT_API"))))
    }
}