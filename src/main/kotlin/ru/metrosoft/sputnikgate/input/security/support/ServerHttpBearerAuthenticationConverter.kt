package ru.metrosoft.sputnikgate.input.security.support

import org.springframework.http.HttpHeaders
import org.springframework.security.core.Authentication
import org.springframework.web.server.ServerWebExchange
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.input.security.auth.AgentAuthenticationBearer


class ServerHttpBearerAuthenticationConverter(jwtVerifier: JwtVerifyHandler) : java.util.function.Function<ServerWebExchange, Mono<Authentication>> {
    private val jwtVerifier: JwtVerifyHandler = jwtVerifier

    override fun apply(serverWebExchange: ServerWebExchange): Mono<Authentication> {
        return Mono.justOrEmpty(serverWebExchange)
            .flatMap<String?> { serverWebExchange: ServerWebExchange ->
                extract(
                    serverWebExchange
                )
            }
            .filter(matchBearerLength)
            .flatMap(isolateBearerValue)
            .flatMap(jwtVerifier::check)
            .flatMap(AgentAuthenticationBearer::create)
    }

    companion object {
        private const val BEARER = "Bearer "
        private val matchBearerLength: (String) -> Boolean = { it.length > BEARER.length }
        private val isolateBearerValue: (String) -> Mono<String> = { authValue -> Mono.justOrEmpty(authValue.substring(BEARER.length)) }

        fun extract(serverWebExchange: ServerWebExchange): Mono<String?> {
            return Mono.justOrEmpty<String?>(
                serverWebExchange.request
                    .headers
                    .getFirst(HttpHeaders.AUTHORIZATION)
            )
        }
    }

//    public final <R> Mono<R> flatMap(Function<? super T, ? extends Mono<? extends R>> transformer) {
//        return onAssembly(new MonoFlatMap(this, transformer));
//    }
}
