package ru.metrosoft.sputnikgate.input.security

import io.swagger.v3.oas.annotations.enums.SecuritySchemeType
import io.swagger.v3.oas.annotations.security.SecurityScheme
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.security.access.AccessDeniedException
import org.springframework.security.authentication.ReactiveAuthenticationManager
import org.springframework.security.config.Customizer
import org.springframework.security.config.annotation.method.configuration.EnableReactiveMethodSecurity
import org.springframework.security.config.annotation.web.configurers.oauth2.server.resource.OAuth2ResourceServerConfigurer
import org.springframework.security.config.web.server.SecurityWebFiltersOrder
import org.springframework.security.config.web.server.ServerHttpSecurity
import org.springframework.security.core.AuthenticationException
import org.springframework.security.web.server.SecurityWebFilterChain
import org.springframework.security.web.server.authentication.AuthenticationWebFilter
import org.springframework.security.web.server.util.matcher.AndServerWebExchangeMatcher
import org.springframework.security.web.server.util.matcher.NegatedServerWebExchangeMatcher
import org.springframework.security.web.server.util.matcher.ServerWebExchangeMatchers
import org.springframework.web.server.ServerWebExchange
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.input.security.support.JwtVerifyHandler
import ru.metrosoft.sputnikgate.input.security.support.ServerHttpBearerAuthenticationConverter


@Configuration
@EnableReactiveMethodSecurity
@SecurityScheme(
    name = "Bearer Authentication",
    type = SecuritySchemeType.HTTP,
    bearerFormat = "JWT",
    scheme = "bearer"
)
class WebSecurityConfig {
    private val logger: Logger = LoggerFactory.getLogger(WebSecurityConfig::class.java)

    @Bean
    fun securityWebFilterChain(http: ServerHttpSecurity, apiAuthManager: ReactiveAuthenticationManager): SecurityWebFilterChain {
        return http
            .authorizeExchange { auth ->
                auth
                    .pathMatchers(HttpMethod.OPTIONS).permitAll()
                    .pathMatchers(*arrayOf("/swagger-ui/**", "/v3/**")).permitAll()
                    .pathMatchers("/favicon.ico").permitAll()
                    .anyExchange().authenticated()
            }
            .csrf { it.disable() }
            .httpBasic { it.disable() }
            .formLogin { it.disable() }
            .exceptionHandling { exceptions ->
                exceptions
                    .authenticationEntryPoint { swe: ServerWebExchange, e: AuthenticationException ->
                        logger.info("[1] Authentication error: Unauthorized[401]: " + e.message)
                        Mono.fromRunnable { swe.response.setStatusCode(HttpStatus.UNAUTHORIZED) }
                    }
                    .accessDeniedHandler { swe: ServerWebExchange, e: AccessDeniedException ->
                        logger.info("[2] Authentication error: Access Denied[401]: " + e.message)
                        Mono.fromRunnable { swe.response.setStatusCode(HttpStatus.FORBIDDEN) }
                    }
            }
            .addFilterAt(bearerAuthenticationFilter(apiAuthManager), SecurityWebFiltersOrder.AUTHENTICATION)
            .build()
    }

    /**
     * Spring security works by filter chaining.
     * We need to add a JWT CUSTOM FILTER to the chain.
     *
     * what is AuthenticationWebFilter:
     *
     * A WebFilter that performs authentication of a particular request. An outline of the logic:
     * A request comes in and if it does not match setRequiresAuthenticationMatcher(ServerWebExchangeMatcher),
     * then this filter does nothing and the WebFilterChain is continued.
     * If it does match then... An attempt to convert the ServerWebExchange into an Authentication is made.
     * If the result is empty, then the filter does nothing more and the WebFilterChain is continued.
     * If it does create an Authentication...
     * The ReactiveAuthenticationManager specified in AuthenticationWebFilter(ReactiveAuthenticationManager) is used to perform authentication.
     * If authentication is successful, ServerAuthenticationSuccessHandler is invoked and the authentication is set on ReactiveSecurityContextHolder,
     * else ServerAuthenticationFailureHandler is invoked
     *
     */
    fun bearerAuthenticationFilter(authManager: ReactiveAuthenticationManager?): AuthenticationWebFilter {
        val bearerAuthenticationFilter = AuthenticationWebFilter(authManager)
        bearerAuthenticationFilter.setServerAuthenticationConverter { exchange ->
            ServerHttpBearerAuthenticationConverter(JwtVerifyHandler()).apply(exchange)
        }
        bearerAuthenticationFilter.setRequiresAuthenticationMatcher(
            AndServerWebExchangeMatcher(
                ServerWebExchangeMatchers.pathMatchers("/**"),
                NegatedServerWebExchangeMatcher(
                    ServerWebExchangeMatchers.pathMatchers(*arrayOf("/swagger-ui/**", "/v3/**"))
                ),
            )
        )

        return bearerAuthenticationFilter
    }

}
