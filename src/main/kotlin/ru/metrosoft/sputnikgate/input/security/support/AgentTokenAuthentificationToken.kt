package ru.metrosoft.sputnikgate.input.security.support

import org.springframework.security.authentication.AbstractAuthenticationToken
import ru.metrosoft.sputnikgate.model.Agent

class AgentTokenAuthentificationToken(private val agent: Agent) : AbstractAuthenticationToken(null) {

    override fun isAuthenticated(): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun getCredentials(): Any {
        return agent.id!!
    }

    override fun getPrincipal(): Any {
        return agent
    }

}