package ru.metrosoft.sputnikgate.input.security

import arrow.core.Either
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.springframework.security.authentication.ReactiveAuthenticationManager
import org.springframework.security.core.Authentication
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.input.security.auth.AgentPrincipal
import ru.metrosoft.sputnikgate.input.security.auth.UnauthorizedException
import ru.metrosoft.sputnikgate.input.security.support.AgentTokenAuthentificationToken
import ru.metrosoft.sputnikgate.service.AgentService


@Component
class AuthenticationManager(val agentService: AgentService) : ReactiveAuthenticationManager {

    override fun authenticate(authentication: Authentication): Mono<Authentication> {
        val principal: AgentPrincipal = authentication.principal as AgentPrincipal

        val agentByAuthToken = runBlocking {
            withContext(Dispatchers.IO) {
                agentService.getAgentByAuthToken(principal.agentToken)
            }
        }

        return when(agentByAuthToken) {
            is Either.Left -> Mono.error(UnauthorizedException("User account not found."))
            is Either.Right -> Mono.just(AgentTokenAuthentificationToken(agentByAuthToken.value))
        }
    }
}