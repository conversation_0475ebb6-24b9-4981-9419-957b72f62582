package ru.metrosoft.sputnikgate.model

import org.openapitools.client.infrastructure.ApiClient
import org.zalando.logbook.Logbook
import org.zalando.logbook.okhttp.LogbookInterceptor
import ru.metrosoft.sputnikgate.output.nbs.api.BitmapTicketsPaymentApi
import ru.metrosoft.sputnikgate.output.nbs.api.ExternalInfoApi
import ru.metrosoft.sputnikgate.output.nbs.api.TicketsPaymentApi
import java.util.*

data class ApiAgentNBS(
    val id: UUID,
    val name: String,
    val openApiBaseUrl: String,
    val openApiKey: String,
    val openApiCertificate: String,
) {
    fun externalInfoApi(logbook: Logbook) = ExternalInfoApi(
        basePath = this.openApiBaseUrl,
        client = ApiClient.defaultClient(
            listOf(
                LogbookInterceptor(logbook)
            ),
            this.openApiKey,
            this.openApiCertificate,
        )
    )

    fun ticketsPaymentApi(logbook: Logbook) =
        TicketsPaymentApi(
            basePath = this.openApiBaseUrl,
            client = ApiClient.defaultClient(
                listOf(
                    LogbookInterceptor(logbook)
                ),
                this.openApiKey,
                this.openApiCertificate,
            )
        )

    fun bitmapTicketsPaymentApi(logbook: Logbook) =
        BitmapTicketsPaymentApi(
            basePath = this.openApiBaseUrl,
            client = ApiClient.defaultClient(
                listOf(
                    LogbookInterceptor(logbook)
                ),
                this.openApiKey,
                this.openApiCertificate,
            )
        )
}