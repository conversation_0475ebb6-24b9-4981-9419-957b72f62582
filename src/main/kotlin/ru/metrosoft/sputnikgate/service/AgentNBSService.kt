package ru.metrosoft.sputnikgate.service

import org.springframework.stereotype.Service
import ru.metrosoft.sputnikgate.model.ApiAgentNBS
import ru.metrosoft.sputnikgate.output.persistance.ApiAgentNBSMapper
import ru.metrosoft.sputnikgate.output.persistance.repository.AgentNBSRepository
import java.util.*

@Service
class ApiAgentNBSService(
    val agentNBSRepository: AgentNBSRepository,
    val agentNBSMapper: ApiAgentNBSMapper
) {

    suspend fun getAgent(id: UUID): ApiAgentNBS {
        val agentNBS = agentNBSRepository.findById(id) ?: throw IllegalArgumentException("Agent NBS not found")

        return agentNBSMapper.toDomain(agentNBS)
    }
}