package ru.metrosoft.sputnikgate.output.persistance

import org.mapstruct.Mapper
import org.mapstruct.Mapping
import ru.metrosoft.sputnikgate.model.*
import ru.metrosoft.sputnikgate.output.persistance.model.AgentCertificate
import ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS


@Mapper(componentModel = "spring")
interface ApiAgentNBSMapper {
    fun toPersistModel(agentNBS: ApiAgentNBS) : ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS

    fun toDomain(agentNBS: ru.metrosoft.sputnikgate.output.persistance.model.AgentNBS) : ApiAgentNBS
}
