package ru.metrosoft.sputnikgate

import ErrorHandlingWebFilter
import io.swagger.v3.oas.annotations.OpenAPIDefinition
import io.swagger.v3.oas.annotations.info.Info
import io.swagger.v3.oas.annotations.security.SecurityRequirement
import io.swagger.v3.oas.annotations.tags.Tag
import org.flywaydb.core.Flyway
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.flyway.FlywayProperties
import org.springframework.boot.autoconfigure.r2dbc.R2dbcProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.data.r2dbc.repository.config.EnableR2dbcRepositories
import org.springframework.scheduling.annotation.EnableAsync
import org.zalando.logbook.Logbook
import ru.metrosoft.sputnikgate.logging.EcsHttpLogFormatter
import ru.metrosoft.sputnikgate.logging.EcsLogSink
import ru.metrosoft.sputnikgate.logging.LoggingWebFilter
import ru.metrosoft.sputnikgate.logging.RequestIdWebFilter


@SpringBootApplication
@EnableR2dbcRepositories
@EnableAsync
@OpenAPIDefinition(
    info = Info(title = "SberTroika Agent API", version = "1.0.0", description = "Documentation APIs v1.0"), tags = [
        Tag(name = "BitmapTicketsPayment", description = "Пrополнение карты Тройка"),
    ], security = [SecurityRequirement(name = "Bearer Authentication")]
)
class GatewayApp

fun main(args: Array<String>) {
    runApplication<GatewayApp>(*args)
}

@Configuration
@EnableConfigurationProperties(R2dbcProperties::class, FlywayProperties::class)
internal class DatabaseConfig {
    @Bean(initMethod = "migrate")
    fun flyway(flywayProperties: FlywayProperties, r2dbcProperties: R2dbcProperties): Flyway {
        return Flyway.configure()
            .dataSource(
                flywayProperties.url,
                r2dbcProperties.username,
                r2dbcProperties.password
            )
            .locations(
                *flywayProperties.locations.toTypedArray()
            )
            .baselineOnMigrate(true)
            .load()
    }
}

@Configuration
class LoggingConfig {

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE)
    fun requestIdWebFilter(): RequestIdWebFilter {
        return RequestIdWebFilter()
    }

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE + 1)
    fun loggingWebFilter(): LoggingWebFilter {
        return LoggingWebFilter()
    }

    @Bean
    @Order(Ordered.HIGHEST_PRECEDENCE + 2)
    fun errorHandlingWebFilter(): ErrorHandlingWebFilter {
        return ErrorHandlingWebFilter()
    }

    @Bean
    fun logbook(): Logbook {
        val logbook = Logbook.builder()
            .sink(EcsLogSink(EcsHttpLogFormatter()))
            .build()
        return logbook
    }
}