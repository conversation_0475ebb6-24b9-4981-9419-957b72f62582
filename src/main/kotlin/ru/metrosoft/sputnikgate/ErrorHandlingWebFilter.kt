import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.springframework.core.io.buffer.DefaultDataBufferFactory
import org.springframework.http.HttpStatus
import org.springframework.web.server.ServerWebExchange
import org.springframework.web.server.WebFilter
import org.springframework.web.server.WebFilterChain
import reactor.core.publisher.Mono
import ru.metrosoft.sputnikgate.exceptions.UnknownSessionException
import ru.metrosoft.sputnikgate.input.ApiError
import ru.metrosoft.sputnikgate.input.security.auth.UnauthorizedException
import ru.metrosoft.sputnikgate.logging.ExceptionUtils
import ru.metrosoft.sputnikgate.logging.putMdcValue

class ErrorHandlingWebFilter : WebFilter {

    override fun filter(exchange: ServerWebExchange, chain: WebFilterChain): Mono<Void> {

        return chain.filter(exchange)
            .onErrorResume { ex ->
                val response = exchange.response

                val errorResponse: ApiError = when (ex) {
                    is UnauthorizedException -> ApiError(
                        HttpStatus.BAD_REQUEST,
                        "Доступ запрещён",
                        null,
                        ApiError.Error("ACCESS_DENIED", "Доступ запрещён")
                    )
                    is UnknownSessionException -> ApiError(
                        HttpStatus.BAD_REQUEST,
                        ex.message ?: "Сессия не найдена",
                        null,
                        ApiError.Error("UNKNOWN_SESSION", "Сессия не найдена")
                    )
                    else -> ApiError(HttpStatus.BAD_REQUEST, ex)
                }

                val buffer = DefaultDataBufferFactory.sharedInstance.wrap(
                    jacksonObjectMapper().writeValueAsBytes(errorResponse)
                )

                val sb = StringBuilder(2048)
                ExceptionUtils.append(sb, ex)
                exchange.putMdcValue("exception", sb.toString())

                response.setStatusCode(errorResponse.status)
                response.writeWith(Mono.just(buffer))
            }
    }
}