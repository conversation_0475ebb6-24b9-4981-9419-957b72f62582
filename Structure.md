# Structure of src directory

This document outlines the structure and purpose of files within the `src` directory of the Sputnik Gateway project.

```
src/
├── main/
│   ├── kotlin/
│   │   └── ru/metrosoft/sputnikgate/
│   │       ├── GatewayApp.kt - Главный класс приложения, точка входа, настройка Spring Boot и OpenAPI.
│   │       ├── ErrorHandlingWebFilter.kt - Веб-фильтр для централизованной обработки ошибок.
│   │       ├── input/ - Пакет для обработки входящих запросов (REST API).
│   │       │   ├── ApiError.kt - Модель данных для стандартизированных ответов с ошибками.
│   │       │   ├── RestExceptionHandler.kt - Обработчик исключений для REST контроллеров.
│   │       │   ├── TroikaRefillRestApi.kt - REST контроллер для операций пополнения и работы с билетами "Тройка".
│   │       │   ├── security/ - Пакет для конфигурации безопасности.
│   │       │   │   ├── WebSecurityConfig.kt - Основная конфигурация безопасности Spring Security WebFlux.
│   │       │   │   ├── AuthenticationManager.kt - Менеджер аутентификации.
│   │       │   │   └── auth/ - Пакет для компонентов аутентификации.
│   │       │   │       ├── AgentAuthenticationBearer.kt - Аутентификация агента по Bearer токену.
│   │       │   │       ├── AgentPrincipal.kt - Представление аутентифицированного принципа (агента).
│   │       │   │       └── UnauthorizedException.kt - Кастомное исключение для неавторизованного доступа.
│   │       │   │   └── support/ - Вспомогательные классы безопасности.
│   │       │   │       ├── AgentTokenAuthentificationToken.kt - Токен аутентификации для агентов.
│   │       │   │       ├── JwtVerifyHandler.kt - Обработчик верификации JWT.
│   │       │   │       └── ServerHttpBearerAuthenticationConverter.kt - Конвертер HTTP запроса в Bearer токен аутентификации.
│   │       │   └── model/ - Модели данных для входящих запросов API.
│   │       │       ├── TroikaRefillModel.kt - Модели данных для операций пополнения "Тройка".
│   │       │       └── TroikaUnwrittenModel.kt - Модели данных для работы с незаписанными билетами.
│   │       ├── logging/ - Пакет для компонентов логирования.
│   │       │   ├── EcsHttpLogFormatter.kt - Форматтер логов в формате ECS для HTTP запросов.
│   │       │   ├── EcsHttpLogWriter.kt - Writer логов в формате ECS.
│   │       │   ├── EcsLogSink.kt - Sink для логов в формате ECS.
│   │       │   ├── ExceptionUtils.kt - Утилиты для работы с исключениями в логировании.
│   │       │   ├── LoggingWebFilter.kt - Веб-фильтр для логирования входящих/исходящих запросов и ответов.
│   │       │   ├── Mdc.kt - Утилиты для работы с MDC (Mapped Diagnostic Context).
│   │       │   ├── ReactHttpLogFormatter.kt - Форматтер логов для реактивных HTTP запросов.
│   │       │   └── RequestIdWebFilter.kt - Веб-фильтр для добавления уникального ID запроса.
│   │       ├── model/ - Модели данных, используемые в разных слоях приложения.
│   │       │   └── Modelkt.kt - Общие модели данных (например, Pagination, Sortable, AgentPaymentType и др.).
│   │       ├── output/ - Пакет для взаимодействия с внешними системами и базой данных.
│   │       │   └── persistance/ - Пакет для работы с хранилищем данных.
│   │       │       └── Mappers.kt - Мапперы для преобразования моделей данных (вероятно, с использованием MapStruct).
│   │       ├── service/ - Пакет с бизнес-логикой.
│   │       │   └── AgentNBSService.kt - Сервис для работы с конфигурациями агентов НБС.
│   │       └── utils/ - Вспомогательные утилиты.
│   │           └── AgentExt.kt - Расширения для класса Agent.
│   └── resources/ - Ресурсы приложения (конфигурации, скрипты БД).
│       ├── application-prod.yaml - Конфигурационный файл для production окружения.
│       ├── application.yaml - Основной конфигурационный файл.
│       └── db/ - Скрипты базы данных.
│           └── migration/ - Скрипты миграций Flyway.
``` 