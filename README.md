# Sputnik Gateway

## Назначение проекта
Sputnik Gateway — это шлюз для обработки платежей и операций с билетами в рамках Новой Билетной Системы (НБС) Московского Метрополитена. Проект обеспечивает:
- Продажу и пополнение билетов на карту "Тройка".
- Управление отложенными записями билетов (например, для последующей активации).
- Интеграцию с API НБС для взаимодействия с партнерами и контрагентами.
- Безопасное и масштабируемое выполнение транзакций в транспортной системе.

## Архитектура
Проект построен на микросервисной архитектуре с использованием следующих компонентов:
1. **Основной шлюз (sputnik-gateway)**: Обрабатывает запросы на продажу и пополнение билетов, включая интеграцию с НБС.
2. **Админ API (admin-api)**: Предоставляет интерфейс для административных задач, таких как мониторинг транзакций и управление контрагентами.
3. **Общая библиотека (common-lib)**: Содержит общие модули для работы с билетами и картами "Тройка".
4. **Клиент для API НБС (nbs-client)**: Обеспечивает взаимодействие с Новой Билетной Системой Московского Метрополитена.

### Основные сущности

1. **Агенты (Agent)**
   - Контрагенты, авторизованные для продажи билетов
   - Содержат информацию о названии, статусе активности (enabled)
   - Связаны с НБС через agent_nbs_id

2. **Агенты НБС (AgentNBS)**
   - Конфигурации для работы с Новой Билетной Системой
   - Содержат параметры подключения (API ключи, SFTP доступ)

3. **Платежи (AgentPayment)**
   - Операции пополнения/продажи билетов
   - Содержат информацию о сумме, статусе, карте, ошибках
   - Типы: PAYWRITE (1), PAY (2), INFO, WRITE

4. **Услуги платежей (AgentPaymentService)**
   - Конкретные билеты/услуги в рамках платежа
   - Содержат код услуги, название, сумму

5. **Реестры**
   - **Реестры агентов (AgentRegistry)**
     - Формируются агентами по итогам суток
     - Содержат информацию о совершенных операциях
     - Отправляются в шлюз для сверки
   
   - **Реестры НБС (Registry)**
     - Формируются шлюзом для каждого агента НБС
     - Создаются ежедневно
     - Отправляются на SFTP сервер агента НБС
     - Содержат сводную информацию по операциям

   - **Форматы реестров**
     - Единый формат для обоих типов реестров
     - salereg_*.csv - реестр продаж
     - writeticketsreg_*.csv - реестр записей билетов

6. **Контракты (AgentContract)**
   - Договорные условия с агентами
   - Содержат даты действия, ставки комиссии (sale_rate, write_rate)

7. **Долги (AgentDebt)**
   - Финансовая информация по агентам
   - Суммы платежей (summ_p), записей (summ_w), общие показатели

8. **Билеты (TroikaTicket)**
   - Детализация совершенного платежа
   - Содержит информацию о приобретенном билете/услуге:
     - Код услуги
     - Название
     - Цена
     - Срок действия
     - Лимиты пополнения (для кошелька)
   - Используется для формирования запроса на запись билета на карту

9. **Данные карт (CardSector, ReadBitmapBlock/WriteBitmapBlock)**
    - Детализация операций с картой "Тройка"
    - Сектора карт (CardSector):
      - Номер сектора
      - Значение сектора
    - Блоки данных:
      - ReadBitmapBlock - данные для чтения с карты
      - WriteBitmapBlock - данные для записи на карту
    - Содержат информацию о конкретных манипуляциях с картой в рамках платежа

10. **Ошибки (ApiError)**
    - Стандартизированные ошибки системы
    - Форматы: TECHNICAL_ERROR и другие

## Технологии
### Backend
- **Язык программирования**: Kotlin
- **Фреймворк**: Spring Boot (WebFlux, Security)
- **База данных**: PostgreSQL
- **Доступ к данным**: R2DBC (реактивный доступ)
- **Миграции БД**: Flyway

### Инфраструктура
- **Контейнеризация**: Docker
- **Оркестрация**: Kubernetes (развертывание через Helm-чарты)
- **CI/CD**: GitLab CI

### API
- **REST API**: Для взаимодействия с клиентами и партнерами НБС.
- **OpenAPI**: Для документирования и интеграции с внешними системами.
- **Логирование**: Кастомный `LoggingWebFilter` для мониторинга запросов/ответов.

### Авторизация в НБС
Шлюз использует два основных способа авторизации при взаимодействии с Новой Билетной Системой, которые настраиваются индивидуально для каждого Агента НБС:

1.  **API Авторизация**: Для взаимодействия через REST API используется авторизация на основе Bearer токенов. Токены генерируются с использованием `openApiKey` и `openApiCertificate`, настроенных для соответствующего Агента НБС.
2.  **SFTP Авторизация**: Для обмена файлами реестров используется SFTP протокол с авторизацией по имени пользователя (`SFTPUsername`) и паролю (`SFTPPassword`), также настроенных для Агента НБС.

## Запуск проекта
1. Убедитесь, что установлены:
   - Docker
   - Kubernetes (опционально для локального развертывания)
   - Helm (для управления чартами)
2. Клонируйте репозиторий:
   ```bash
   git clone <репозиторий>
   ```
3. Запустите сборку и развертывание:
   ```bash
   docker-compose build
   docker-compose up
   ```

## Лицензия
Проект распространяется под лицензией [MIT](LICENSE). 