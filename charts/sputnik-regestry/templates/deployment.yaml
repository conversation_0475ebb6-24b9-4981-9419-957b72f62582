apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sputnik-regestry.fullname" . }}
  labels:
    {{- include "sputnik-regestry.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "sputnik-regestry.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "sputnik-regestry.selectorLabels" . | nindent 8 }}
        checksum/git-revision: {{ .Values.app.gitRevision | quote }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "sputnik-regestry.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          env:
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: database
                  key: url
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: database
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: database
                  key: password
            - name: DIC_PATH
              value: "/mnt/ftp"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: ftp-storage
              mountPath: "/mnt/ftp"
              subPath: ftp-dev
      volumes:
        - name: ftp-storage
          persistentVolumeClaim:
            claimName: sputnik-ftp-pvc
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
