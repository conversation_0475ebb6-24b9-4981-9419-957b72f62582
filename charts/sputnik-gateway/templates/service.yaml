apiVersion: v1
kind: Service
metadata:
  name: {{ printf "%s-http" (include "sputnik-gateway.fullname" .) }}
  labels:
    {{- include "sputnik-gateway.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.http.type }}
  ports:
    - port: {{ .Values.service.http.port }}
      targetPort: {{ .Values.service.http.targetPort }}
      protocol: TCP
      name: http
  selector:
    {{- include "sputnik-gateway.selectorLabels" . | nindent 4 }}
