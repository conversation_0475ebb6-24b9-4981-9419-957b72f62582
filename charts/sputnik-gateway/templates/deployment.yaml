apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "sputnik-gateway.fullname" . }}
  labels:
    {{- include "sputnik-gateway.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "sputnik-gateway.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "sputnik-gateway.selectorLabels" . | nindent 8 }}
        checksum/git-revision: {{ .Values.app.gitRevision | quote }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "sputnik-gateway.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ["java"]
          args: ["-Djava.security.egd=file:/dev/./urandom", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-jar", "sputnik-gateway.jar"]
          env:
            - name: DB_URL
              valueFrom:
                secretKeyRef:
                  name: database
                  key: url
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: database
                  key: username
            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: database
                  key: password
            - name: DB_MIGRATION_ENABLE
              value: {{ ternary "true" "false" .Values.env.db.migration.enable | quote }}
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.http.targetPort }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
