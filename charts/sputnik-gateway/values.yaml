# Default values for sputnik-gateway.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: sputnik-gateway
app:
  gitRevision: 'unknown'

replicaCount: 1

image:
  repository: sputnik-gateway
  pullPolicy: Always
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }
# fsGroup: 2000

securityContext: { }
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
# runAsUser: 1000

service:
  http:
    type: ClusterIP
    port: 8081
    targetPort: 8081

resources:
  limits:
    cpu: 1000m
    memory: 1024Mi
  requests:
    cpu: 500m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

env:
  db:
    migration:
      enable: true