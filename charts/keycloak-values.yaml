global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  defaultStorageClass: ""
  storageClass: ""
  ## Security parameters
  ##
  security:
    ## @param global.security.allowInsecureImages Allows skipping image verification
    allowInsecureImages: false
  ## Compatibility adaptations for Kubernetes platforms
  ##
  compatibility:
    ## Compatibility adaptations for Openshift
    ##
    openshift:
      ## @param global.compatibility.openshift.adaptSecurityContext Adapt the securityContext sections of the deployment to make them compatible with Openshift restricted-v2 SCC: remove runAsUser, runAsGroup and fsGroup and let the platform use their allowed default IDs. Possible values: auto (apply if the detected running cluster is Openshift), force (perform the adaptation always), disabled (do not perform adaptation)
      ##
      adaptSecurityContext: auto

ingress:
  enabled: false
  hostname: "keycloak-dev.sbertroika.ru"
  servicePort: http
  hostnameStrict: true

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 1

keycloakConfigCli:
  image:
    registry: docker.io
    repository: bitnami/keycloak
    tag: 26.1.0-debian-12-r1
    pullPolicy: IfNotPresent
  hostAliases:
    - ip: "***********"
      hostnames:
        - "keycloak-dev.sbertroika.ru"
  extraEnvVars:
    - name: PROXY_ADDRESS_FORWARDING
      value: "true"
    - name: KEYCLOAK_HOSTNAME
      value: keycloak-dev.sbertroika.ru

proxy: edge

postgresql:
  enabled: false
externalDatabase:
  host: "**********"
  port: 5432
  user: keycloakdev
  database: keycloakdev
  password: "U6HDv7M2SMp14KPQ1oy8"