stages:
  - build
  - testing
  - tag-build
  - deploy
  - deploy-prod

variables:
  DOCKER_REPOSITORY_ADDR: $DOCKER_REPOSITORY_ADDR
  GIT_SUBMODULE_STRATEGY: recursive
  GIT_SUBMODULE_FORCE_HTTPS: "true"
  KUBEVALURL: "https://github.com/instrumenta/kubeval/releases/download/v0.16.1/kubeval-linux-amd64.tar.gz"
  CI_DEBUG_TRACE: "true"

#Сборка контейнера приложения
build:
  stage: build
  when: on_success
  tags:
    - docker
  script:
    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA"
      MODE=development;
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)-$CI_COMMIT_SHORT_SHA"
      MODE=production;
      fi

    # Сборка и пуш образа api в репозиторий Docker
    - IMAGE_PREFIX=$DOCKER_REPOSITORY_ADDR/new-ticket-system/ TAG=$TAG docker-compose build
    - IMAGE_PREFIX=$DOCKER_REPOSITORY_ADDR/new-ticket-system/ TAG=$TAG docker-compose push

  rules:
    #Запускаем сборку при коммите в dev, release/*  hotfix/* ветки
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

# Сборка контейнера приложения по тэгу
build_by_tag:
  stage: tag-build
  when: on_success
  tags:
    - docker
  script:
    - |
      if [[ "$CI_COMMIT_TAG" =~ ^[0-9]+\.[0-9]+\.[0-9]+-prod$ ]]; then
        TAG="${CI_COMMIT_TAG}"
        MODE=production
        echo "Building and pushing image with tag: $TAG"
      else
        echo "Invalid tag format. Skipping build."
        exit 0
      fi
    - IMAGE_PREFIX=$DOCKER_REPOSITORY_ADDR/new-ticket-system/ TAG=$TAG docker-compose build
    - IMAGE_PREFIX=$DOCKER_REPOSITORY_ADDR/new-ticket-system/ TAG=$TAG docker-compose push
  rules:
    - if: '$CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+-prod$/'
      when: always
    - when: never

# Тестирование манифестов HELM инструменом kubeval
helm_kubeval_testing:
  stage: testing
  image:
    name: alpine/helm
    entrypoint: [""]

  before_script:
    - wget $KUBEVALURL
    - tar xvf kubeval-linux-amd64.tar.gz
    - chmod +x kubeval

  script:
    - ./kubeval --version
    - helm version
    - helm template charts/sputnik-gateway -n sputnik | ./kubeval
    - helm template charts/sputnik-admin-api -n sputnik | ./kubeval
    - helm template charts/sputnik-ui -n sputnik | ./kubeval
    - helm template charts/sputnik-regestry -n sputnik | ./kubeval

  tags:
    #Используем docker-exec тэг для раннера типа "docker execution"
    - docker-executor

  rules:
    - changes:
      - charts/**/*

#Установка HELM манифестов
deploy_chart:
  stage: deploy
  image:
    name: alpine/helm
    entrypoint: [""]
  when: on_success
  tags:
    #Используем docker-exec тэг для раннера типа "docker execution"
    - docker-executor
  script:

    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi

    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-api --namespace sputnik ./charts/sputnik-gateway
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/api
      --set image.tag=$TAG
      --wait
      --kubeconfig $KUBECONFIG_NBS_DEV
      
    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-console-api --namespace sputnik ./charts/sputnik-admin-api
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/console-api
      --set image.tag=$TAG
      --set env.keycloak.realm.url=https://keycloak-dev.sbertroika.ru/realms/sputnik-gateway
      --wait
      --kubeconfig $KUBECONFIG_NBS_DEV

    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-ui --namespace sputnik ./charts/sputnik-ui
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/ui
      --set image.tag=$TAG
      --wait
      --kubeconfig $KUBECONFIG_NBS_DEV

    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-regestry --namespace sputnik ./charts/sputnik-regestry
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/regestry
      --set image.tag=$TAG
      --wait
      --kubeconfig $KUBECONFIG_NBS_DEV

  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME =~ /^release\/.*$/ || $CI_COMMIT_REF_NAME =~ /^hotfix\/.*$/'

#Установка HELM манифестов
deploy_chart_prod:
  stage: deploy-prod
  dependencies:
    - build_by_tag
  when: manual
  image:
    name: alpine/helm
    entrypoint: [""]
  tags:
    - docker-executor
  script:

    #Если ветка DEV то использует CI_COMMIT_REF_NAME в качестве тэга, для веток release/* и hotfix/* используем версии
    - if [ "$CI_COMMIT_REF_NAME" = "dev" ]; then
      TAG="$CI_COMMIT_SHORT_SHA";
      else
      TAG="$(echo "$CI_COMMIT_BRANCH" | cut -d'/' -f2)";
      fi

    # Деплой Helm-чарта с установкой образа и его параметрами
    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-api --namespace sputnik ./charts/sputnik-gateway
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/api
      --set image.tag=$TAG
      --wait
      --kubeconfig $KUBECONFIG_NBS_PROD

    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-console-api --namespace sputnik ./charts/sputnik-admin-api
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/console-api
      --set image.tag=$TAG
      --set env.keycloak.realm.url=https://keycloak.sbertroika.ru/realms/sputnik-gateway
      --wait
      --kubeconfig $KUBECONFIG_NBS_PROD

    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-ui --namespace sputnik ./charts/sputnik-ui
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/ui
      --set image.tag=$TAG
      --wait
      --kubeconfig $KUBECONFIG_NBS_PROD

    # Деплой Helm-чарта с установкой образа и его параметрами
    - helm upgrade sputnik-regestry --namespace sputnik ./charts/sputnik-regestry
      --install
      --set app.gitRevision=${CI_COMMIT_SHORT_SHA}
      --set image.repository=$DOCKER_REPOSITORY_ADDR/new-ticket-system/regestry
      --set image.tag=$TAG
      --wait
      --kubeconfig $KUBECONFIG_NBS_PROD
  rules:
    - if: '$CI_COMMIT_TAG =~ /^[0-9]+\.[0-9]+\.[0-9]+-prod$/'
      when: always
    - when: never

