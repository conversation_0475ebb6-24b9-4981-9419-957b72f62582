import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

plugins {
    id("idea")
    alias(libs.plugins.kotlin.kapt)
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.dependency.management)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
}

group = "ru.metrosoft"
version = "2.0.0-SNAPSHOT"

kapt {
    showProcessorStats = true
    correctErrorTypes = true
}

dependencies {
    implementation(project(":common-lib"))

    implementation(libs.spring.boot.starter.data.r2dbc)

    implementation(libs.kotlinx.coroutines.reactor)
    implementation(libs.kotlinx.coroutines.core)
    implementation(libs.kotlin.reflect)

    implementation(libs.arrow.core)

    // MapStruct
    compileOnly(libs.mapstruct)
    kapt(libs.mapstruct.processor)

    runtimeOnly(libs.r2dbc.postgresql)
    runtimeOnly(libs.r2dbc.pool)
    runtimeOnly(libs.postgresql)
}

tasks.withType<KotlinCompile> {
    kotlinOptions {
        freeCompilerArgs = listOf("-Xjsr305=strict")
    }
}

tasks.named<Test>("test") {
    useJUnitPlatform()
}
