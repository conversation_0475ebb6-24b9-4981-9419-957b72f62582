import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

buildscript {
    ext {
        springBootVersion = '3.4.2'
        springDependencyManagementVersion = '1.1.0'

        kotlinJvmVersion = '2.1.10'

        arrowCoreVersion = "1.0.1"
        postgresqlVersion = "42.5.2"

        mockkVersion = "1.13.5"
    }
}

plugins {
    id 'idea'
    id "org.jetbrains.kotlin.kapt" version "$kotlinJvmVersion"
    id 'org.springframework.boot' version "$springBootVersion"
    id 'io.spring.dependency-management' version "$springDependencyManagementVersion"
    id 'org.jetbrains.kotlin.jvm' version "$kotlinJvmVersion"
    id 'org.jetbrains.kotlin.plugin.spring' version "$kotlinJvmVersion"
}

group = 'ru.metrosoft'
version = '2.0.0-SNAPSHOT'

configurations {
    compileOnly {
        extendsFrom annotationProcessor
    }
}

repositories {
    mavenCentral()
}

kapt {
    showProcessorStats = true
    correctErrorTypes true
}

dependencies {
    implementation project(":common-lib")

    implementation 'org.springframework.boot:spring-boot-starter-data-r2dbc'

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-reactor'
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core"
    implementation 'org.jetbrains.kotlin:kotlin-reflect'

    implementation "io.arrow-kt:arrow-core:$arrowCoreVersion"

    // MapStruct
    compileOnly "org.mapstruct:mapstruct:1.5.3.Final"
    kapt "org.mapstruct:mapstruct-processor:1.5.3.Final"

    runtimeOnly('org.postgresql:r2dbc-postgresql')
    runtimeOnly('io.r2dbc:r2dbc-pool')
    runtimeOnly "org.postgresql:postgresql:$postgresqlVersion"

    testImplementation("org.junit.jupiter:junit-jupiter-api")
    testRuntimeOnly("org.junit.jupiter:junit-jupiter-engine")
    testCompileOnly 'org.projectlombok:lombok'
    testImplementation('org.springframework.boot:spring-boot-starter-test') {
        exclude group: 'org.junit.vintage', module: 'junit-vintage-engine'
    }
    testImplementation 'org.jetbrains.kotlin:kotlin-test'
    testImplementation("io.mockk:mockk:$mockkVersion")
}

tasks.withType(KotlinCompile) {
    kotlinOptions {
        freeCompilerArgs = ['-Xjsr305=strict']
    }
}

tasks.named('test') {
    useJUnitPlatform()
}

jar {
    enabled = false
}
