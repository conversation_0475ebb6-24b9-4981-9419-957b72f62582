spring:
  application:
    name: sputnik-repository

  r2dbc:
    url: r2dbc:pool:${DB_URL:postgresql://localhost:5432/sputnik}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}

dic:
  path: ${DIC_PATH:./csvlist}

job:
  agent-registry: "0 0 2 * * ?"
  registry-check: "0 * * * * *"
