package ru.metrosoft.model

import arrow.core.Either
import arrow.core.left
import arrow.core.right
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.flow.toSet
import ru.metrosoft.sputnikgate.service.AgentPaymentService
import java.io.File
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

data class RegistryImpl(val file: File) {

    fun detectRegistryType(): Registry = when {
        file.name.matches(Regex(SALE_REG_REGEX)) -> SaleRegistry(file)
        file.name.matches(Regex(WRITE_TICKETS_REG_REGEX)) -> WriteTicketsRegistry(file)
        else -> UnknownRegistry(file)
    }

    internal data class UnknownRegistry(
        val file: File,
    ): Registry {
        override fun parse(file: File): Either<List<Errors>, IParsedRegistry> = UnknownParsedRegistry(this).right()
        override suspend fun reconciliation(agentPaymentService: AgentPaymentService): List<Errors> = listOf()
        internal data class UnknownParsedRegistry(val saleRegistry: UnknownRegistry): IParsedRegistry {
            override fun validate(): List<Errors> = listOf()
        }
    }

    /** Продажи */
    internal data class SaleRegistry(
        val file: File,
    ): Registry {
        var headers: SaleRegistryHeaderLine? = null
        var summaryLine: SaleRegistrySummaryLine? = null
        var rows: List<SaleRegistryLine>? = null

        internal data class ParsedSaleRegistry(
            val saleRegistry: SaleRegistry
        ): IParsedRegistry {

            override fun validate(): List<Errors> {
                val errors = mutableListOf<Errors>()

                if (
                    saleRegistry.headers == null
                    || saleRegistry.summaryLine == null
                    || saleRegistry.rows == null
                ) {
                    return listOf()
                }

                if (saleRegistry.summaryLine!!.operation != saleRegistry.rows!!.size) {
                    errors.add(Errors.INVALID_OPERATION_SIZE_IN_SUMMARY_ROW)
                }

                if (saleRegistry.summaryLine!!.amount != saleRegistry.rows!!.sumOf { it.amount ?: 0 }) {
                    errors.add(Errors.INVALID_AMOUNT_SUM_IN_SUMMARY_ROW)
                }

                errors.plus(saleRegistry.headers!!.validate())
                errors.plus(saleRegistry.summaryLine!!.validate())
                saleRegistry.rows!!.forEach { errors.plus(it.validate()) }

                return errors
            }
        }

        override fun parse(file: File): Either<List<Errors>, IParsedRegistry> {
            file.useLines { lines ->
                val rowsIterator = lines.iterator()
                if (!rowsIterator.hasNext()) Errors.FILE_EMPTY.left()

                headers = SaleRegistryHeaderLine(rowsIterator.next().split(";"))

                val fieldMapSummary = headers!!.headers.zip(rowsIterator.next().split(";")).toMap()
                summaryLine = parseRecord(fieldMapSummary, ::SaleRegistrySummaryLine)

                val records = mutableListOf<SaleRegistryLine>()
                while (rowsIterator.hasNext()) {
                    val fieldMap = headers!!.headers.zip(rowsIterator.next().split(";")).toMap()
                    records.add(parseRecord(fieldMap, ::SaleRegistryLine))
                }

                rows = records
            }

            return ParsedSaleRegistry(this).right()
        }

        private fun <T> parseRecord(fieldMap: Map<String, String>, constructor: (Int?, String?, String?, Int?, String?, Int?, Int?, Int?, Int?, String?) -> T): T {
            return constructor(
                fieldMap[OPERATION]?.toIntOrNull(),
                fieldMap[PAYMENT_ID],
                fieldMap[CARD_NUM],
                fieldMap[AMOUNT]?.toIntOrNull(),
                fieldMap[PAYMENT_TIME],
                fieldMap[RESOURCE_AMOUNT]?.toIntOrNull(),
                fieldMap[TICKET_TYPE]?.toIntOrNull(),
                fieldMap[TICKET_SERVICE]?.toIntOrNull(),
                fieldMap[OPERATION_TYPE]?.toIntOrNull(),
                fieldMap[SAM]
            )
        }

        override suspend fun reconciliation(
            agentPaymentService: AgentPaymentService
        ): List<Errors> {
            if (rows.isNullOrEmpty()) return emptyList()

            val errors = mutableListOf<Errors>()
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

            val registryDate = rows?.firstNotNullOfOrNull { row ->
                row.paymentTime?.let { time ->
                    val registryTime = time.let { LocalDateTime.parse(it, formatter) }
                    registryTime.atZone(ZoneId.of("Europe/Moscow")).toLocalDate()
                }
            } ?: return listOf(Errors.INVALID_TIME_FORMAT)

            val databasePayments = agentPaymentService
                .findByDay(registryDate)
                .filter { it.payStatus && it.status }
                .toSet()
                .associateBy { it._id }

            val registryPayments = rows!!.associateBy { it.paymentId }

            for (paymentId in registryPayments.keys) {
                if (!databasePayments.containsKey(paymentId)) {
                    errors.add(Errors.TRANSACTION_MISSING_FROM_DATABASE)
                }
            }

            for (paymentId in databasePayments.keys) {
                if (!registryPayments.containsKey(paymentId)) {
                    errors.add(Errors.TRANSACTION_MISSING_FROM_REGISTRY)
                }
            }

            registryPayments.forEach { (paymentId, registry) ->
                val payment = databasePayments[paymentId] ?: return@forEach
                /** Ужас ужасный, запросы в форыче, надо убрать, если останется время */
                payment.services = agentPaymentService.findServiceById(payment._id).toList()

//                ticketType а это ваще где ?
                if (payment.cardNum != registry.cardNum) {
                    errors.add(Errors.INVALID_RECONCILIATION_CARD_NUMBER)
                }

                if (payment.amount.toInt() != registry.amount) {
                    errors.add(Errors.INVALID_RECONCILIATION_AMOUNT)
                }

                if (payment.type.id == null || payment.type.id != registry.operationType) {
                    errors.add(Errors.INVALID_RECONCILIATION_OPERATION_TYPE)
                }

                if (payment.services[0].serviceCode != registry.ticketService?.toInt()) {
                    errors.add(Errors.INVALID_RECONCILIATION_TICKET_SERVICE)
                }

                if (payment.services[0].amount.toInt() != registry.resourceAmount) {
                    errors.add(Errors.INVALID_RECONCILIATION_RESOURCE_AMOUNT)
                }

                if (!registry.sam.isNullOrEmpty()) {
                    errors.add(Errors.INVALID_RECONCILIATION_SAM)
                }

                try {
                    val registryTime = registry.paymentTime?.let { LocalDateTime.parse(it, formatter) }
                    val paymentTime = payment.ts.atZone(ZoneId.of("Europe/Moscow")).toLocalDateTime().withNano(0)

                    if (registryTime != null && registryTime != paymentTime) {
                        errors.add(Errors.TIME_MISMATCH)
                    }
                } catch (e: Exception) {
                    errors.add(Errors.INVALID_TIME_FORMAT)
                }
            }

            return errors
        }

        /**
         * 1 строка - список названий полей для следующих далее строк.
         */
        internal data class SaleRegistryHeaderLine(
            val headers: List<String>,
        ) : RegistryLine {

            override fun validate(): List<Errors> {
                if (headers[0] != OPERATION) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[1] != PAYMENT_ID) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[2] != CARD_NUM) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[3] != AMOUNT) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[4] != PAYMENT_TIME) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[5] != RESOURCE_AMOUNT) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[6] != TICKET_TYPE) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[7] != TICKET_SERVICE) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[8] != OPERATION_TYPE) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[9] != SAM) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)

                return listOf()
            }
        }

        /**
         * 2 строка - итоговая строка
         */
        internal data class SaleRegistrySummaryLine(
            val operation: Int? = null,
            val paymentId: String? = null,
            val cardNum: String? = null,
            val amount: Int? = null,
            val paymentTime: String? = null,
            val resourceAmount: Int? = null,
            val ticketType: Int? = null,
            val ticketService: Int? = null,
            val operationType: Int? = null,
            val sam: String? = null,
        ) : RegistryLine {

            // досмотреть документацию
            override fun validate(): List<Errors> {
                val errors = mutableListOf<Errors>()

                if (operation == null) errors.add(Errors.INVALID_OPERATION_IN_SUMMARY_ROW)
                if (!paymentId.isNullOrEmpty()) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (!cardNum.isNullOrEmpty()) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (amount == null) errors.add(Errors.INVALID_AMOUNT_IN_SUMMARY_ROW)
                if (paymentTime?.matches(PAYMENT_TIME_REGEX) == false) errors.add(Errors.INVALID_PAYMENT_TIME_IN_SUMMARY_ROW)
                if (resourceAmount != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (ticketType != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (ticketService != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (operationType != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (sam != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)

                return errors
            }

            companion object {
                val PAYMENT_TIME_REGEX = Regex("""\d{4}-\d{2}-\d{2}""")
            }
        }

        /**
         * 3+ строки - продажи
         */
        internal data class SaleRegistryLine(
            val operation: Int? = null,
            val paymentId: String? = null,
            val cardNum: String? = null,
            val amount: Int? = null,
            val paymentTime: String? = null,
            val resourceAmount: Int? = null,
            val ticketType: Int? = null,
            val ticketService: Int? = null,
            val operationType: Int? = null,
            val sam: String? = null,
        ) : RegistryLine {

            override fun validate(): List<Errors> {
                val errors = mutableListOf<Errors>()

                if (operation != 1) errors.add(Errors.INVALID_OPERATION)
                if (paymentId.isNullOrBlank()) errors.add(Errors.INVALID_PAYMENT_ID)
                if (cardNum.isNullOrBlank()) errors.add(Errors.INVALID_CARD_NUM)
                if (amount == null) errors.add(Errors.INVALID_AMOUNT)
                if (paymentTime?.matches(PAYMENT_TIME_REGEX) == false) errors.add(Errors.INVALID_PAYMENT_TIME)
                if (ticketService == null) errors.add(Errors.INVALID_TICKET_SERVICE)
                if (operationType !in VALID_OPERATION_TYPES) errors.add(Errors.INVALID_OPERATION_TYPE)

                return errors
            }

            companion object {
                val VALID_OPERATION_TYPES = setOf(1, 2)
                val PAYMENT_TIME_REGEX = Regex("""\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}""")
            }
        }
    }

    /** Записи на носитель билетов */
    internal data class WriteTicketsRegistry(
        val file: File,
    ): Registry {
        var headers: WriteTicketsRegistryHeaderLine? = null
        var summaryLine: WriteTicketsRegistrySummaryLine? = null
        var rows: List<WriteTicketsRegistryLine>? = null

        internal data class ParsedWriteTicketsRegistry(
            val writeTicketsRegistry: WriteTicketsRegistry
        ): IParsedRegistry {

            override fun validate(): List<Errors> {
                val errors = mutableListOf<Errors>()

                if(
                    writeTicketsRegistry.headers == null
                    || writeTicketsRegistry.summaryLine == null
                    || writeTicketsRegistry.rows == null
                ) {
                    return listOf()
                }

                if (writeTicketsRegistry.summaryLine!!.ticketsWriteId?.toInt() != writeTicketsRegistry.rows!!.size) {
                    errors.add(Errors.INVALID_OPERATION_SIZE_IN_SUMMARY_ROW)
                }

                if (writeTicketsRegistry.summaryLine!!.amount != writeTicketsRegistry.rows!!.sumOf { it.amount ?: 0 }) {
                    errors.add(Errors.INVALID_AMOUNT_SUM_IN_SUMMARY_ROW)
                }

                errors.plus(writeTicketsRegistry.headers!!.validate())
                errors.plus(writeTicketsRegistry.summaryLine!!.validate())
                writeTicketsRegistry.rows!!.forEach { errors.plus(it.validate()) }

                return errors
            }
        }

        override fun parse(file: File): Either<List<Errors>, IParsedRegistry> {
            file.useLines { lines ->
                val rowsIterator = lines.iterator()
                if (!rowsIterator.hasNext()) Errors.FILE_EMPTY.left()

                headers = WriteTicketsRegistryHeaderLine(rowsIterator.next().split(";"))

                val fieldMapSummary = headers!!.headers.zip(rowsIterator.next().split(";")).toMap()
                summaryLine = parseRecord(fieldMapSummary, ::WriteTicketsRegistrySummaryLine)

                val records = mutableListOf<WriteTicketsRegistryLine>()
                while (rowsIterator.hasNext()) {
                    val fieldMap = headers!!.headers.zip(rowsIterator.next().split(";")).toMap()
                    records.add(parseRecord(fieldMap, ::WriteTicketsRegistryLine))
                }

                rows = records
            }

            return ParsedWriteTicketsRegistry(this).right()
        }

        private inline fun <reified T> parseRecord(
            fieldMap: Map<String, String>,
            constructor: (String?, String?, String?, Int?, Int?, Int?, String?, Int?) -> T
        ): T {
            return constructor(
                fieldMap[TICKETS_WRITE_ID],
                fieldMap[CARD_NUM],
                fieldMap[WRITE_TIME],
                fieldMap[TICKET_TYPE]?.toIntOrNull(),
                fieldMap[TICKET_SERVICE]?.toIntOrNull(),
                fieldMap[AMOUNT]?.toIntOrNull(),
                fieldMap[SAM],
                fieldMap[WRITE_RESULT]?.toIntOrNull()
            )
        }

        override suspend fun reconciliation(
            agentPaymentService: AgentPaymentService
        ): List<Errors> {
            if (rows.isNullOrEmpty()) return emptyList()

            val errors = mutableListOf<Errors>()
            val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

            val registryDate = rows!!.firstNotNullOfOrNull { row ->
                row.writeTime?.let { time ->
                    val registryTime = time.let { LocalDateTime.parse(it, formatter) }
                    registryTime.atZone(ZoneId.of("Europe/Moscow")).toLocalDate()
                }
            } ?: return listOf(Errors.INVALID_TIME_FORMAT)

            val databasePayments = agentPaymentService
                .findByDay(registryDate)
                .filter { it.writeStatus && it.status }
                .toSet()
                .associateBy { it._id }

            val registryTicketsWrite = rows!!.associateBy { it.ticketsWriteId }

            for (ticketsWriteId in registryTicketsWrite.keys) {
                if (!databasePayments.containsKey(ticketsWriteId)) {
                    errors.add(Errors.TRANSACTION_MISSING_FROM_DATABASE)
                }
            }

            for (ticketsWriteId in databasePayments.keys) {
                if (!registryTicketsWrite.containsKey(ticketsWriteId)) {
                    errors.add(Errors.TRANSACTION_MISSING_FROM_REGISTRY)
                }
            }

            registryTicketsWrite.forEach { (ticketsWriteId, registry) ->
                val payment = databasePayments[ticketsWriteId] ?: return@forEach
                payment.services = agentPaymentService.findServiceById(payment._id).toList()

                if (payment.cardNum != registry.cardNum) {
                    errors.add(Errors.INVALID_RECONCILIATION_CARD_NUMBER)
                }

                if (payment.amount.toInt() != registry.amount) {
                    errors.add(Errors.INVALID_RECONCILIATION_AMOUNT)
                }

                if (payment.services[0].serviceCode != registry.ticketService) {
                    errors.add(Errors.INVALID_RECONCILIATION_TICKET_SERVICE)
                }

                if (!registry.sam.isNullOrEmpty()) {
                    errors.add(Errors.INVALID_RECONCILIATION_SAM)
                }

                if ((registry.writeResult == 1) != payment.writeStatus) {
                    errors.add(Errors.INVALID_RECONCILIATION_WRITE_RESULT)
                }

                try {
                    val registryTime = registry.writeTime?.let { LocalDateTime.parse(it, formatter) }
                    val paymentTime = payment.ts.atZone(ZoneId.of("Europe/Moscow")).toLocalDateTime().withNano(0)

                    if (registryTime != null && registryTime != paymentTime) {
                        errors.add(Errors.TIME_MISMATCH)
                    }
                } catch (e: Exception) {
                    errors.add(Errors.INVALID_TIME_FORMAT)
                }
            }

            return errors
        }

        /**
         * 1 строка - список названий полей для следующих далее строк.
         */
        internal data class WriteTicketsRegistryHeaderLine(
            val headers: List<String>,
        ) : RegistryLine {

            override fun validate(): List<Errors> {
                if (headers[0] != TICKETS_WRITE_ID) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[1] != CARD_NUM) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[2] != WRITE_TIME) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[3] != TICKET_TYPE) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[4] != TICKET_SERVICE) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[5] != AMOUNT) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[6] != SAM) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)
                if (headers[7] != WRITE_RESULT) return listOf(Errors.TABLE_HEADER_ORDER_IS_INCORRECT)

                return listOf()
            }
        }

        /**
         * 2 строка - итоговая строка
         */
        internal data class WriteTicketsRegistrySummaryLine(
            val ticketsWriteId: String?,
            val cardNum: String?,
            val writeTime: String?,
            val ticketType: Int?,
            val ticketService: Int?,
            val amount: Int?,
            val sam: String?,
            val writeResult: Int?
        ) : RegistryLine {

            override fun validate(): List<Errors> {
                val errors = mutableListOf<Errors>()

                if (ticketsWriteId == null) errors.add(Errors.INVALID_TICKETS_WRITE_ID_IN_SUMMARY_ROW)
                if (cardNum != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (amount == null) errors.add(Errors.INVALID_AMOUNT_IN_SUMMARY_ROW)
                if (writeTime?.matches(PAYMENT_TIME_REGEX) == false) errors.add(Errors.INVALID_WRITE_TIME_IN_SUMMARY_ROW)
                if (ticketType != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (ticketService != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (amount != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (sam != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)
                if (writeResult != null) errors.add(Errors.FIELD_THERE_MUST_BE_EMPTY)

                return errors
            }

            companion object {
                val PAYMENT_TIME_REGEX = Regex("""\d{4}-\d{2}-\d{2}""")
            }
        }

        /**
         * 3+ строки - продажи
         */
        internal data class WriteTicketsRegistryLine(
            val ticketsWriteId: String?,
            val cardNum: String?,
            val writeTime: String?,
            val ticketType: Int?,
            val ticketService: Int?,
            val amount: Int?,
            val sam: String?,
            val writeResult: Int?
        ) : RegistryLine {

            override fun validate(): List<Errors> {
                val errors = mutableListOf<Errors>()

                if (ticketsWriteId == null) errors.add(Errors.INVALID_TICKETS_WRITE_ID)
                if (cardNum?.isNotBlank() == false) errors.add(Errors.INVALID_CARD_NUM)
                if (writeTime?.matches(DATE_REGEX) == false) errors.add(Errors.INVALID_WRITE_TIME)
                if (ticketService == null) errors.add(Errors.INVALID_TICKET_SERVICE)
                if (writeResult !in VALID_WRITE_RESULTS) errors.add(Errors.INVALID_WRITE_RESULT)

                return errors
            }

            companion object {
                private val DATE_REGEX = Regex("""\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}""")
                val VALID_WRITE_RESULTS = setOf(0, 1)
            }
        }
    }
}

/** Регулярка для файла реестра продаж */
const val SALE_REG_REGEX = """salereg_[A-Z]{3}_\d{6}_\d{2}\.csv"""

/** Регулярка для файла реестра записей на носитель билетов */
const val WRITE_TICKETS_REG_REGEX = """writeticketsreg_\d{6}_\d{2}\.csv"""

const val OPERATION = "Operation"
const val PAYMENT_ID = "PaymentID"
const val CARD_NUM = "CardNum"
const val AMOUNT = "Amount"
const val PAYMENT_TIME = "PaymentTime"
const val RESOURCE_AMOUNT = "ResourceAmount"
const val TICKET_TYPE = "TicketType"
const val TICKET_SERVICE = "TicketService"
const val OPERATION_TYPE = "OperationType"
const val SAM = "sam"

const val TICKETS_WRITE_ID = "TicketsWriteID"
const val WRITE_TIME = "WriteTime"
const val WRITE_RESULT = "WriteResult"