package ru.metrosoft.service

import org.springframework.stereotype.Service
import ru.metrosoft.model.AgentRegistry
import ru.metrosoft.model.NewAgentRegistry
import ru.metrosoft.output.persistance.mapper.AgentRegistryMapper
import ru.metrosoft.output.persistance.repository.AgentRegistryRepository
import java.util.*

@Service
class AgentRegistryService(
    private val agentRegistryRepository: AgentRegistryRepository,
    private val agentRegistryMapper: AgentRegistryMapper
) {

    suspend fun newRegistry(ar: NewAgentRegistry) : AgentRegistry {
        return agentRegistryMapper.toDomain(agentRegistryRepository.save(agentRegistryMapper.toPersistModel(ar)))
    }

    suspend fun badOne(agentRegistryId: UUID): Unit {
        agentRegistryRepository.badOne(agentRegistryId)
    }

    suspend fun goodOne(agentRegistryId: UUID): Unit {
        agentRegistryRepository.goodOne(agentRegistryId)
    }

}