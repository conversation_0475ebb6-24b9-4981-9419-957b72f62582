package ru.metrosoft.parser

import kotlinx.coroutines.runBlocking
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import ru.metrosoft.model.Errors
import ru.metrosoft.model.NewAgentRegistry
import ru.metrosoft.model.RegistryImpl
import ru.metrosoft.service.AgentRegistryService
import ru.metrosoft.sputnikgate.service.AgentPaymentService
import java.io.File
import java.nio.file.Files
import java.nio.file.StandardCopyOption
import java.util.*

@Component
class RegistryFileProcessor(
    private val agentPaymentService: AgentPaymentService,
    private val agentRegistryService: AgentRegistryService
) {

    private val logger: Logger = LoggerFactory.getLogger(RegistryFileProcessor::class.java)

    fun processFile(file: File) = runBlocking {
        if (file.nameWithoutExtension.endsWith("_failed")) return@runBlocking
        if (file.nameWithoutExtension.endsWith("_success")) return@runBlocking

        val parentDirName = file.absoluteFile.parentFile.name
        val agentId = try {
            UUID.fromString(parentDirName)
        } catch (e: IllegalArgumentException) {
            logger.error("Invalid agent ID in directory name: $parentDirName. Expected UUID format.")
            renameFileAsFailed(file)
            return@runBlocking
        }

        val registry = agentRegistryService.newRegistry(
            NewAgentRegistry(
                agentId = agentId,
                fileName = file.name,
                status = "NEW",
                content = file.bufferedReader().use { it.readText() }
            )
        )

        RegistryImpl(file).detectRegistryType().apply {
            if (this is RegistryImpl.UnknownRegistry) {
                handleError(registry.id, file, listOf(Errors.FILE_NAME_ERROR))
                return@apply
            }

            parse(file).fold(
                ifLeft = { errors ->
                    handleError(registry.id, file, errors)
                },
                ifRight = { parsedRegistry ->
                    val errors = parsedRegistry.validate()
                    if (errors.isNotEmpty()) {
                        handleError(registry.id, file, errors)
                    }
                }
            )

            val errors = reconciliation(agentPaymentService)
            if (errors.isNotEmpty()) {
                handleError(registry.id, file, errors)
            } else {
                deleteFile(registry.id, file)
            }
        }
    }

    suspend fun handleError(id: UUID, file: File, errors: List<Errors>) {
        errors.forEach { logger.error("${it.message}, в файле: ${file.name}") }

        agentRegistryService.badOne(id)

        renameFileAsFailed(file)
    }

    private fun renameFileAsFailed(file: File) {
        val failedFile = File("${file.parent}/${file.nameWithoutExtension}_failed.csv")
        Files.move(file.toPath(), failedFile.toPath(), StandardCopyOption.REPLACE_EXISTING)
    }

    /** Удаляет файл после успешной обработки */
    suspend private fun deleteFile(id: UUID, file: File) {
        val failedFile = File("${file.parent}/${file.nameWithoutExtension}_success.csv")

        agentRegistryService.goodOne(id)

        Files.move(file.toPath(), failedFile.toPath(), StandardCopyOption.REPLACE_EXISTING)

//        if (file.exists()) file.delete()
    }
}
