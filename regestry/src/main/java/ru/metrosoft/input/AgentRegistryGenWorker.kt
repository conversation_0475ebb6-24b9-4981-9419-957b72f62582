package ru.metrosoft.input

import kotlinx.coroutines.runBlocking
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import ru.metrosoft.sputnikgate.service.AgentNBSRegistryService
import ru.metrosoft.sputnikgate.service.RegistryService
import java.time.LocalDate
import java.time.ZoneId

@Component
class AgentRegistryGenWorker(
    private val registryService: RegistryService,
    private val agentNBSService: AgentNBSRegistryService
) {

    @Scheduled(cron = "\${job.agent-registry}")
    fun run() = runBlocking {
        agentNBSService.getAgentList().forEach { agentNBS ->
            agentNBS.id.let { id ->
                registryService.newRegistry("SALEREG", LocalDate.now(ZoneId.of("Europe/Moscow")).minusDays(1), id)
                registryService.newRegistry("WRITEREG", LocalDate.now(ZoneId.of("Europe/Moscow")).minusDays(1), id)
            }
        }
    }
}