package ru.metrosoft.output.persistance.repository

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import ru.metrosoft.sputnikgate.output.persistance.model.AgentRegistry
import java.util.*

interface AgentRegistryRepository: CoroutineCrudRepository<AgentRegistry, UUID> {

    @Query("UPDATE agent_registry set status = 'BAD' where id = :id")
    fun badOne(id: UUID)

    @Query("UPDATE agent_registry set status = 'SUCCESS' where id = :id")
    fun goodOne(id: UUID)

}